/*
 * Copyright 2012 The Netty Project
 *
 * The Netty Project licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
package co.timekettle.tmkengine.NettyClient;

import co.timekettle.tmkengine.TmkPacket;
import co.timekettle.tmkengine.JsonResponse;
import io.netty.bootstrap.Bootstrap;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioSocketChannel;
import co.timekettle.tmkengine.utils.TmkLogger;
import io.netty.handler.ssl.SslContext;
import io.netty.handler.ssl.SslContextBuilder;
import io.netty.handler.timeout.IdleState;
import io.netty.handler.timeout.IdleStateEvent;
import io.netty.handler.timeout.IdleStateHandler;

import javax.net.ssl.SSLException;
import java.security.cert.CertificateException;

public final class TmkClient {

    private String HOST = "127.0.0.1";
    private int PORT = 8322;
    private long session = 0;
    private boolean enableSSL = false;
    private NioEventLoopGroup group;
    private TmkClientHandler clientHandler;
    private TmkClientIdleHandler clientIdleHandler;
    private TmkClientListener listener;
    public void setListener(TmkClientListener listener) {
        this.listener = listener;
    }
    public void setHost(String host) {
        this.HOST = host;
    }

    public String getHost() {
        return HOST;
    }

    public int getPort() {
        return PORT;
    }

    public void setPort(int port) {
        this.PORT = port;
    }

    public long getSession() {
        return session;
    }

    public void setSession(long session) {
        this.session = session;
    }

    public TmkClient(String host, int port, long session) {
        HOST = host;
        PORT = port;
        this.session = session;
    }

    public TmkClient(String host, int port, long session, boolean enableSSL) {
        HOST = host;
        PORT = port;
        this.session = session;
        this.enableSSL = enableSSL;
    }

    TmkClientHandler getTmkClientHandler() {
        if (clientHandler == null) {
            clientHandler = new TmkClientHandler();
        }
        return clientHandler;
    }

    TmkClientIdleHandler getTmkClientIdleHandler() {
        if (clientIdleHandler == null) {
            clientIdleHandler = new TmkClientIdleHandler();
        }
        return clientIdleHandler;
    }

    void logE(String desc) {
        TmkLogger.e("任务[" + this.session + "]" + desc);
    }

    void logD(String desc) {
        TmkLogger.d("任务[" + this.session + "]" + desc);
    }

    public boolean connect() {
        if (group != null) {
            if (group.isShutdown()) {
                logE("连接已断开, 无法再连接: " + HOST + ":" + PORT);
            } else {
                logD("请勿重复连接: " + HOST + ":" + PORT);
            }
            return false;
        }

        group = new NioEventLoopGroup();
        try {
            // Configure SSL.
            final SslContext sslCtx = enableSSL ? SslContextBuilder.forClient().build() : null;

            Bootstrap b = new Bootstrap();
            b.option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 10000); // 设置超时时间
            b.group(group)
                    .channel(NioSocketChannel.class)
                    .handler(new ChannelInitializer<SocketChannel>() {
                        @Override
                        protected void initChannel(SocketChannel ch) throws Exception {
                            ChannelPipeline pipeline = ch.pipeline();
                            if (sslCtx != null) {
                                pipeline.addLast(sslCtx.newHandler(ch.alloc(), HOST, PORT));
                            }

                            // Enable stream compression (you can remove these two if unnecessary)
//        pipeline.addLast(ZlibCodecFactory.newZlibEncoder(ZlibWrapper.GZIP));
//        pipeline.addLast(ZlibCodecFactory.newZlibDecoder(ZlibWrapper.GZIP));

                            // 添加 IdleStateHandler 检测连接的空闲时间, 在数据 handler 前添加
                            pipeline.addLast(getTmkClientIdleHandler());

                            // Add the number codec first,
                            pipeline.addLast(new TmkPacketDecoder());
                            pipeline.addLast(new TmkPacketEncoder());

                            // and then business logic.
                            pipeline.addLast(getTmkClientHandler());
                        }
                    });

            // Make a new connection.
            ChannelFuture f = b.connect(HOST, PORT).sync();
//            f.channel().closeFuture().addListener((ChannelFutureListener) channelFuture -> {
//                logE("监听到连接被关闭, 若是主动调用 close (接下来会打印主动关闭)触发则无需理会");
//                listener.onDisConnected(null);
//            });

        } catch (SSLException e) {
            logE("证书异常(CertificateException | SSLException): " + e.getLocalizedMessage());
            e.printStackTrace();
            this.disconnect(new TmkEngineException(e));
//            listener.onException(new TmkEngineException(e));
        } catch (Exception e) {
            logE("连接等异常(ConnectTimeoutException | IOException): " + e.getLocalizedMessage());
            // 此处捕获错误:  io.netty.channel.ConnectTimeoutException: connection timed out, java.io.IOException: Connection reset by peer
            e.printStackTrace();
            this.disconnect(new TmkEngineException(e));
//            listener.onException(new TmkEngineException(e));
        } finally {
//            group.shutdownGracefully();
        }

        return true;
    }

    public boolean write(TmkPacket packet) {
//        assert clientHandler != null: "异常: clientHandler 为空";
        if (clientHandler != null)  return clientHandler.sendMsg(packet);
        return false;
    }

    public void disconnect(TmkEngineException exception) {
        try {
            if (clientHandler != null && clientHandler.ctx != null) {
                clientHandler.ctx.channel().closeFuture().addListener((ChannelFutureListener) channelFuture -> {
                    logE("监听到连接被关闭, 若是主动调用 close (接下来会打印主动关闭)触发则无需理会");
                    listener.onFinishedOrDisconnected(exception);
                    if (group != null) {
                        group.shutdownGracefully();
                        group = null;
                    }
                });
                clientHandler.ctx.channel().close();
                clientHandler.ctx.channel().disconnect();
                clientHandler = null;
            } else {
                // 还未连接时
                listener.onFinishedOrDisconnected(exception);
                if (group != null) {
                    group.shutdownGracefully();
                    group = null;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public interface TmkClientListener {
        public void onConnected();

        public void onFinishedOrDisconnected(TmkEngineException e);

        public void onSocketReceive(TmkPacket packet);

        public void onSocketJsonFrame(long session, JsonResponse response); // 接口废弃

        public void onSocketSoundFrame(long session, byte[] data); // 接口废弃
    }

    public static class TmkEngineException {
        public Throwable cause;
        public JsonResponse.Status status;
        public TmkEngineException(JsonResponse.Status status) {
            this.status = status;
        }

        public TmkEngineException(Throwable cause) {
            this.cause = cause;
        }
    }


    class TmkClientHandler extends SimpleChannelInboundHandler<TmkPacket> {
        private ChannelHandlerContext ctx;
        @Override
        public void channelActive(ChannelHandlerContext ctx) {
            this.ctx = ctx;

            if (listener != null) listener.onConnected();
        }

        @Override
        public void channelRead0(ChannelHandlerContext ctx, final TmkPacket msg) {
            if (listener == null) logE("!!!监听器为空, 仍然收到消息: " + (String) msg.get());
            if (listener != null) listener.onSocketReceive(msg);
//            if (msg.getType() == TmkPacket.BODY_JSON) {
//                String text = (String) msg.get();
//                JsonResponse response = JsonResponse.parse(text);
//                listener.onSocketJsonFrame(msg.getSession(), response);
//            } else if (msg.getType() == TmkPacket.BODY_SOUND) {
//                listener.onSocketSoundFrame(msg.getSession(), (byte[]) msg.get());
//            }
        }

        @Override
        public void userEventTriggered(ChannelHandlerContext ctx, Object evt) throws Exception {
            if (evt instanceof IdleStateEvent) {
                IdleStateEvent event = (IdleStateEvent) evt;
                logE("idle 空闲检测事件(读空闲会超时关闭): " + event.state());
                if (event.state() == IdleState.READER_IDLE) {
                    // 读空闲时间内没有收到任何数据
                } else if (event.state() == IdleState.WRITER_IDLE) {
                    // 写空闲时间内没有写入任何数据
                } else if (event.state() == IdleState.ALL_IDLE) {
                    // 读写空闲时间内没有读取或写入任何数据
                    disconnect(new TmkEngineException(JsonResponse.Status.ServerResponeTimeout));
                }
            } else {
                super.userEventTriggered(ctx, evt);
            }
        }

        @Override
        public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
            cause.printStackTrace();
            disconnect(new TmkEngineException(cause));
        }

        public boolean sendMsg(TmkPacket packet) {
            if (!this.ctx.channel().isActive()) {
                logE("socket 通道未被激活/已断开, 不再发送数据");
                return false;
            }
            ChannelFuture future = this.ctx.write(packet);
            assert future != null;
            future.addListener(this.packetSender);
            this.ctx.flush();
            return true;
        }

        private void sendNumbersTest() {
            // Do not send more than 4096 numbers.
            ChannelFuture future = null;
            for (int i = 0; i < 4096; i++) {
                future = this.ctx.write(i);
            }
            assert future != null;
            future.addListener(this.packetSender);
            this.ctx.flush();
        }

        private final ChannelFutureListener packetSender = future -> {
            if (future.isSuccess()) {
//                logD("发送成功");
            } else {
                if (!this.ctx.channel().isActive()) {
                    logE("socket 通道未被激活/已断开, 不再抛出异常");
                    return;
                }
                logE("发送异常: " + future.cause().getLocalizedMessage());
                future.cause().printStackTrace();
                disconnect(new TmkEngineException(future.cause()));
            }
        };
    }

    class TmkClientIdleHandler extends IdleStateHandler {
        private static final int READ_IDLE_TIME = 0; // 读空闲时间
        private static final int WRITE_IDLE_TIME = 0; // 写空闲时间
        private static final int ALL_IDLE_TIME = 8; // 读写空闲时间, 读写均未发生时触发
        public TmkClientIdleHandler() {
            super(READ_IDLE_TIME, WRITE_IDLE_TIME, ALL_IDLE_TIME);
        }
    }
}
