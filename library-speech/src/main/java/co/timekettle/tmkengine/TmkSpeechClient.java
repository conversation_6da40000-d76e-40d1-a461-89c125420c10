package co.timekettle.tmkengine;

import android.content.Context;
import android.text.TextUtils;

import org.json.JSONArray;

import java.util.Arrays;
import java.util.List;

import co.timekettle.kafka.KafkaClient;
import co.timekettle.kafka.KafkaLogger;
import co.timekettle.speech.EngineHost;
import co.timekettle.tmkengine.NettyClient.TmkClient;
import co.timekettle.tmkengine.utils.DNSUtil;
import co.timekettle.tmkengine.utils.EncryptAES;

import co.timekettle.tmkengine.NetSessionContext.ContextListener;
import co.timekettle.tmkengine.utils.SnUtil;
import co.timekettle.tmkengine.utils.TmkLogger;

public class TmkSpeechClient {
    public static String Prefix = "10"; // 10(TmkAndroid), 11(TmkiOS), 12(T1), 13(t1mini)
    public static String ClientVersion = "0.0.0"; // 客户端版本
    public static String ClientUnionKey = "000"; // 客户端唯一key(mac地址/用户ID等)
    public static String ClientType = "app_android";
    public static String HttpApiBaseUrl;
    public static JSONArray noOpusTTSCodeArray;
    private String mSubscriptionKey = null;
    private String specificEngine;
    public EngineHost host;
    private List<EngineHost> defaultHosts = Arrays.asList(new EngineHost("***********", 5050));
    private List<EngineHost> hosts;
    KafkaLogger.ReadTask kafkaLogReadTask; // 销毁时传送日志

    private static TmkSpeechClient instance = null;
    public static TmkSpeechClient shareInstance() {
        if (instance == null) {
            instance = new TmkSpeechClient();
        }
        return instance;
    }

    /**
     * 是否开启 kafka 日志上传
     */
    public static void setShouldUploadKafkaLog(boolean shouldUploadKafkaLog) {
        KafkaClient.ShouldUploadKafkaLog = shouldUploadKafkaLog;
    }

    public void createUtility(Context context, final String subscriptionKey) {
        String msg = subscriptionKey + "." + System.currentTimeMillis();
        try {
            this.mSubscriptionKey = EncryptAES.Encrypt(msg, "woshichuanqi");
        } catch (Exception e) {
            e.printStackTrace();
        }
        KafkaLogger.setRootDirPath(context.getExternalCacheDir().getAbsolutePath());
        if (kafkaLogReadTask != null) {
            kafkaLogReadTask.stop();
            kafkaLogReadTask = null;
        }
    }

    public void destoryUtility() {
        if (kafkaLogReadTask == null) {
            final KafkaClient[] kafkaClient = {null};
            kafkaLogReadTask = new KafkaLogger.ReadTask();
            kafkaLogReadTask.start((String msgJson) -> {
                if (kafkaClient[0] == null) kafkaClient[0] = new KafkaClient();
                kafkaClient[0].send(msgJson);
                return true;
            });
        }
    }

    public void setSpecificHost(EngineHost host) {
        this.host = host;
    }

    public void setSpecificHosts(List<EngineHost> hosts) {
        this.hosts = hosts;
    }

    public void setSpecificEngine(String engine) {
        this.specificEngine = engine;
    }

    public void fetchIPList() {
        DNSUtil.fetch();
    }

    public NetSessionContext createRecognizer(String srcCode,
                                              boolean opus,
                                              ContextListener listener) {
        JsonRecognizeRequest request = new JsonRecognizeRequest();
        request.setSubscriptionKey(mSubscriptionKey);
        request.setType(JsonRequest.Type.Recognize);
        request.setSrcCode(srcCode);
        request.setOpus(opus);
        request.setSn(SnUtil.getSn());
        if (this.specificEngine != null) request.setReqEngine(this.specificEngine);

        return NetSessionContextBuilder.aContext()
                .withRequest(request)
                .withListener(listener)
                .withTcpClient(getTcpClient(request.session))
                .build();
    }

    public NetSessionContext createTranslator(String srcCode,
                                              String dstCode,
                                              String text,
                                              ContextListener listener) {
        JsonTranslateRequest request = new JsonTranslateRequest();
        request.setSubscriptionKey(mSubscriptionKey);
        request.setType(JsonRequest.Type.Translate);
        request.setSrcCode(srcCode);
        request.setDstCode(dstCode);
        request.setText(text);
        request.setSn(SnUtil.getSn());
        if (this.specificEngine != null) request.setReqEngine(this.specificEngine);

        return NetSessionContextBuilder.aContext()
                .withRequest(request)
                .withListener(listener)
                .withTcpClient(getTcpClient(request.session))
                .build();
    }

    public NetSessionContext createSynthesizer(String dstCode,
                                               String text,
                                               ContextListener listener) {
        JsonSynthesizeRequest request = new JsonSynthesizeRequest();
        request.setSubscriptionKey(mSubscriptionKey);
        request.setType(JsonRequest.Type.Synthesize);
        request.setSrcCode(dstCode);
        request.setText(text);
        boolean useTTSOpus = noOpusTTSCodeArray != null && !noOpusTTSCodeArray.toString().contains(dstCode);
        request.setTTSOpus(useTTSOpus);
        request.setGender(JsonSynthesizeRequest.SynthesizeVoiceGender.Female.name());
        if (this.specificEngine != null) request.setReqEngine(this.specificEngine);

        return NetSessionContextBuilder.aContext()
                .withRequest(request)
                .withListener(listener)
                .withTcpClient(getTcpClient(request.session))
                .build();
    }

    public NetSessionContext createSynthesizer(String dstCode,
                                               String voiceName,
                                               String text,
                                               ContextListener listener) {
        JsonSynthesizeRequest request = new JsonSynthesizeRequest();
        request.setSubscriptionKey(mSubscriptionKey);
        request.setType(JsonRequest.Type.Synthesize);
        request.setSrcCode(dstCode);
        request.setText(text);
        boolean useTTSOpus = noOpusTTSCodeArray != null && !noOpusTTSCodeArray.toString().contains(dstCode);
        request.setTTSOpus(useTTSOpus);
        request.setGender(voiceName == null || voiceName.isEmpty() ? JsonSynthesizeRequest.SynthesizeVoiceGender.Female.name() : voiceName);
        if (this.specificEngine != null) request.setReqEngine(this.specificEngine);

        return NetSessionContextBuilder.aContext()
                .withRequest(request)
                .withListener(listener)
                .withTcpClient(getTcpClient(request.session))
                .build();
    }

    public NetSessionContext createSpeechTranslation(String srcCode,
                                                     String dstCode,
                                                     boolean opus,
                                                     ContextListener listener) {
        JsonSpeechTranslationRequest request = new JsonSpeechTranslationRequest();
        request.setSubscriptionKey(mSubscriptionKey);
        request.setType(JsonRequest.Type.SpeechTranslation);
        request.setSrcCode(srcCode);
        request.setDstCode(dstCode);
        request.setOpus(opus);
        if (this.specificEngine != null) request.setReqEngine(this.specificEngine);

        return NetSessionContextBuilder.aContext()
                .withRequest(request)
                .withListener(listener)
                .withTcpClient(getTcpClient(request.session))
                .build();
    }

    public NetSessionContext createSpeechTranslation2(String srcCode,
                                                     String dstCode,
                                                     boolean opus,
                                                     ContextListener listener) {
        JsonSpeechTranslationRequest request = new JsonSpeechTranslationRequest();
        request.setSubscriptionKey(mSubscriptionKey);
        request.setType(JsonRequest.Type.TwoStepSpeechTranslation);
        request.setSrcCode(srcCode);
        request.setDstCode(dstCode);
        request.setOpus(opus);
        if (this.specificEngine != null) request.setReqEngine(this.specificEngine);

        return NetSessionContextBuilder.aContext()
                .withRequest(request)
                .withListener(listener)
                .withTcpClient(getTcpClient(request.session))
                .build();
    }

    public NetSessionContext createSpeechTranslation(String srcCode,
                                                     String dstCode,
                                                     JsonRequest.Type type,
                                                     boolean opus,
                                                     ContextListener listener) {
        JsonSpeechTranslationRequest request = new JsonSpeechTranslationRequest();
        request.setSubscriptionKey(mSubscriptionKey);
        request.setType(type);
//        request.setSrcCode(srcCode);
//        request.setDstCode(dstCode);
        boolean mutiLang = type.isMutilSrcLang() || type.isMutilDstLang();
        request.setSrcCode(mutiLang ? (type.isMutilSrcLang() ? Arrays.asList(srcCode, dstCode).toArray() : Arrays.asList(srcCode).toArray()) : srcCode);
        request.setDstCode(mutiLang ? (type.isMutilDstLang() ? Arrays.asList(srcCode, dstCode).toArray() : Arrays.asList(dstCode).toArray()) : dstCode);
        request.setOpus(opus);
        if (this.specificEngine != null) request.setReqEngine(this.specificEngine);

        return NetSessionContextBuilder.aContext()
                .withRequest(request)
                .withListener(listener)
                .withTcpClient(getTcpClient(request.session))
                .build();
    }

    public NetSessionContext createTaskContext(long taskId,
                                               JsonRequest request,
                                               ContextListener listener) {
        request.setSubscriptionKey(mSubscriptionKey);
        if (this.specificEngine != null) request.setReqEngine(this.specificEngine);

        return NetSessionContextBuilder.aContext()
                .withRequest(request)
                .withListener(listener)
                .withTcpClient(getTcpClient(taskId))
                .build();
    }


//    private TcpClient getTcpClient() {
//        return TcpClientBuilder.aTcpClient()
//                .withHost(host)
//                .withPort(port)
//                .build();
//    }

    private TmkClient getTcpClient(long session) {
        if (host != null && !TextUtils.isEmpty(host.ip)) {
            TmkLogger.d("任务[" + session + "]" + "使用指定单一引擎: " + host.toString());
            return new TmkClient(host.ip, host.port, session, host.ssl);
        } else if (hosts != null && hosts.size() > 0) {
            TmkLogger.d("任务[" + session + "]" + "使用指定引擎列表: " + hosts.get(0).toString());
            return new TmkClient(hosts.get(0).ip, hosts.get(0).port, session, hosts.get(0).ssl);
        } else {
            TmkLogger.d("任务[" + session + "]" + "使用默认引擎列表: " + defaultHosts.get(0).toString());
            return new TmkClient(defaultHosts.get(0).ip, defaultHosts.get(0).port, session, defaultHosts.get(0).ssl);
        }
    }

    public EngineHost getHost() {
        if (host != null && !TextUtils.isEmpty(host.ip)) {
            return new EngineHost(host.ip, host.port, host.ssl);
        } else if (hosts != null && hosts.size() > 0) {
            return new EngineHost(hosts.get(0).ip, hosts.get(0).port, hosts.get(0).ssl);
        } else {
            return new EngineHost(defaultHosts.get(0).ip, defaultHosts.get(0).port, defaultHosts.get(0).ssl);
        }
    }
}
