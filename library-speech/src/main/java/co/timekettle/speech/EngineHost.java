package co.timekettle.speech;

import androidx.annotation.NonNull;

public class EngineHost {
    public String ip;
    public int port = 5050;
    public String type;
    public String code;
    public boolean enabled = true;
    public boolean ssl = false;
    public String country;

    public long connect_cost = Long.MAX_VALUE; // 初始化成最大值

    public EngineHost(String ip) {
        this.ip = ip;
    }

    public EngineHost(String ip, int port) {
        this.ip = ip;
        this.port = port;
    }

    public EngineHost(String ip, int port, boolean ssl) {
        this.ip = ip;
        this.port = port;
        this.ssl = ssl;
    }

    @Override
    public String toString() {
        return "EngineHost{" +
                "ip='" + ip + '\'' +
                ", port=" + port +
                '}';
    }
}
