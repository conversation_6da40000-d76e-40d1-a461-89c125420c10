import kotlin.math.roundToInt

/**
 * 项目构建配置
 *
 * <AUTHOR>
 * @since 2022/04/15
 */
object AndroidConfig {

    const val PACKAGE_NAME = "com.translation666.w3pro"
    const val COMPILE_SDK_VERSION = 33
    const val MIN_SDK_VERSION = 26
    const val TARGET_SDK_VERSION =33  // Android 13
    const val BUILD_TOOLS_VERSION = "32.0.0"
    var VERSION_CODE = 2

    const val VERSION_NAME = "2.02.44"

    /**
     * 是否模块可以单独运行为 App
     */
    const val MODULE_IS_APP = false


}

/**
 * 项目当前的版本状态
 * 该状态直接反映当前App是测试版 还是正式版 或者预览版
 * 打包前记得修改该状态
 * 开发环境:DEBUG、公开测试版:BETA、正式版:RELEASE
 */
object AppVersion {

    const val DEBUG = "VERSION_STATUS_DEBUG"

    const val BETA = "VERSION_STATUS_BETA"

    const val RELEASE = "VERSION_STATUS_RELEASE"
}

