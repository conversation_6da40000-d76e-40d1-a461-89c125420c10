package co.timekettle.module_translate.tools

import com.timekettle.upup.base.utils.logE

/**
 * Create by licoba on 2022/4/27
 * 翻译模块的字符串工具类
 */

object TransStringUtil {

    fun splitNumber(number: String): String {
        val part1 = number.substring(0, 3)
        val part2 = number.substring(3)
        val result = "$part1 $part2"
        return result
    }

    fun splitNickname(nickName: String?): String {
        val subStr = nickName?.take(2)
        val firstChar = if (subStr != null && subStr.length >= 1) subStr[0].uppercase().toString() else ""
        val secondChar = if (subStr != null && subStr.length >= 2) subStr[1].lowercase().toString() else ""
        return firstChar + secondChar
    }

    fun getLanPairString(selfCode: String?, otherCode: String?): String {
        if (selfCode.isNullOrEmpty() || otherCode.isNullOrEmpty()) return ""
        var bigCodeSelf: String = if (selfCode.contains("-")) {
            selfCode.split("-")[0]
        } else {
            selfCode
        }
        var bigCodeOther: String = if (otherCode.contains("-")) {
            otherCode.split("-")[0]
        } else {
            otherCode
        }
        return "${bigCodeSelf}<->${bigCodeOther}"
    }

    // 比较两个语言对是否是一样的，不管哪个在前  哪个在后
    fun comparePairEquals(str1: String?, str2: String?): Boolean {
//        val splitStr = "<->"
        val splitStr = "-"
        val pairString1 = str1?.replace("<->", "-")
        val pairString2 = str2?.replace("<->", "-")
        return if (pairString1.isNullOrEmpty() || pairString2.isNullOrEmpty()) {
            logE("语言对是空的，无法比较")
            false
        } else if (!pairString1.contains(splitStr) || !pairString2.contains(splitStr)) {
            logE("不是正确的语言对格式，无法比较是否一致")
            false
        } else {
            val str11 = pairString1.split(splitStr)[0]
            val str12 = pairString1.split(splitStr)[1]
            val str21 = pairString2.split(splitStr)[0]
            val str22 = pairString2.split(splitStr)[1]
            str11 == str21 && str12 == str22 || str11 == str22 && str12 == str21
        }
    }

    fun getMacSuffix4(rawStr: String?): String {
        return if (rawStr.isNullOrEmpty()) "Empty"
        else if (!rawStr.contains(":") || rawStr.length < 5) "Format Error！"
        else rawStr.takeLast(5).replace(":", "")
    }

    // 获得大语种小尾巴（不需要国际化，但是要用对应语言翻译）
    fun getLanguageTail(lanCode: String): String {
        return when (lanCode.split("-")[0]) {
            "ar" -> "اللغة العربية" // 阿拉伯语的“阿拉伯语” 以下同理
            "bg" -> "Български"
            "ca" -> "Català"
            "cs" -> "Čeština"
            "da" -> "Dansk"
            "de" -> "Deutsch"
            "el" -> "ελληνικά"
            "en" -> "English"
            "es" -> "Español"
            "zh" -> "中文"
            "fi" -> "Suomi"
            "fr" -> "Français"
            "he" -> "עִברִית"
            "hi" -> "हिन्दी"
            "hr" -> "Hrvatski"
            "hu" -> "Magyar"
            "id" -> "Bahasa Indonesia"
            "it" -> "Italiano"
            "ja" -> "日本語"
            "ko" -> "한국인"
            "ms" -> "Bahasa Melayu"
            "nl" -> "Nederlands"
            "nb" -> "Norsk Bokmål"
            "pl" -> "Polski"
            "pt" -> "Português"
            "ro" -> "Română"
            "ru" -> "Русский"
            "sk" -> "Slovenčina"
            "sl" -> "Slovenščina"
            "sv" -> "svenska"
            "ta" -> "தமிழ்"
            "th" -> "ภาษาไทย"
            "tr" -> "Türkçe"
            "vi" -> "Tiếng Việt"
            "uk" -> "Yкраїнська"
            "is" -> "Íslenska"
            "fil" -> "Filipino"
            "ur" -> "اردو"
            "te" -> "తెలుగు"
            "my" -> "မြန်မာဘာသာ"
            "fa" -> "فارسی"
            else -> return ""
        }
    }

    // 获得口音小尾巴（不需要国际化，但是要用对应语言翻译）
    fun getAccentTail(code: String): String {
        return when (code) {
            "ar-AE" -> "الإمارات" // 阿拉伯语的“阿联酋” 以下同理
            "ar-BH" -> "البحرين"
            "ar-DZ" -> "الجزائر"
            "ar-EG" -> "مصر"
            "ar-IL" -> "إسرائيل"
            "ar-IQ" -> "العراق"
            "ar-JO" -> "الأردن"
            "ar-KW" -> "الكويت"
            "ar-LB" -> "لبنان"
            "ar-MA" -> "المغرب"
            "ar-OM" -> "سلطنة عمان"
            "ar-PS" -> "فلسطين"
            "ar-QA" -> "دولة قطر"
            "ar-SA" -> "العربية السعودية"
            "ar-TN" -> "تونس"
            "zh-HK" -> "粤语"
            "zh-CN" -> "简体"
            "zh-TW" -> "繁体"
            "en-US" -> "USA"
            "en-AU" -> "Australia"
            "en-CA" -> "Canada"
            "en-GB" -> "UK"
            "en-GH" -> "Ghana"
            "en-IE" -> "Ireland"
            "en-IN" -> "India"
            "en-KE" -> "Kenya"
            "en-NG" -> "Nigeria"
            "en-SG" -> "Singapore"
            "en-NZ" -> "New Zealand"
            "en-PH" -> "Philippines"
            "en-TZ" -> "Tanzania"
            "en-ZA" -> "South Africa"
            "fr-FR" -> "France"
            "fr-CA" -> "Canada"
            "pt-PT" -> "Portugal"
            "pt-BR" -> "Brasil"
            "es-ES" -> "España"
            "es-US" -> "EE.UU"
            "es-AR" -> "Argentina"
            "es-BO" -> "Bolivia"
            "es-CL" -> "Chile"
            "es-CO" -> "Colombia"
            "es-CR" -> "Costa Rica"
            "es-DO" -> "Dominica"
            "es-EC" -> "Ecuador"
            "es-GT" -> "Guatemala"
            "es-HN" -> "Honduras"
            "es-MX" -> "México"
            "es-NI" -> "Nicaragua"
            "es-PA" -> "Panamá"
            "es-PE" -> "Perú"
            "es-PR" -> "Puerto Rico"
            "es-PY" -> "Paraguay"
            "es-SV" -> "El Salvador"
            "es-UY" -> "Uruguay"
            "es-VE" -> "Venezuela"
            "ta-IN" -> "இந்தியா"
            "ta-SG" -> "சிங்கப்பூர்"
            "ta-LK" -> "இலங்கை"
            "ta-MY" -> "மலேசியா"
            "ur-PK" -> "پاکستان"
            "ur-IN" -> "انڈیا"
            else -> return ""
        }
    }

    // 获取完整的中文显示名称
    fun getChineseFullName(code: String): String {
        return when (code) {
            "ar-AE" -> "阿拉伯语(阿联酋)"
            "ar-BH" -> "阿拉伯语(巴林)"
            "ar-DZ" -> "阿拉伯语(阿尔及利亚)"
            "ar-EG" -> "阿拉伯语(埃及)"
            "ar-IL" -> "阿拉伯语(以色列)"
            "ar-IQ" -> "阿拉伯语(伊拉克)"
            "ar-JO" -> "阿拉伯语(约旦)"
            "ar-KW" -> "阿拉伯语(科威特)"
            "ar-LB" -> "阿拉伯语(黎巴嫩)"
            "ar-MA" -> "阿拉伯语(摩洛哥)"
            "ar-OM" -> "阿拉伯语(阿曼)"
            "ar-PS" -> "阿拉伯语(巴勒斯坦)"
            "ar-QA" -> "阿拉伯语(卡塔尔)"
            "ar-SA" -> "阿拉伯语(沙特阿拉伯)"
            "ar-TN" -> "阿拉伯语(突尼斯)"
            "bg-BG" -> "保加利亚语(保加利亚)"
            "zh-HK" -> "中文(粤语)"
            "zh-CN" -> "中文(简体)"
            "zh-TW" -> "中文(繁体)"
            "ca-ES" -> "加泰罗尼亚语(西班牙)"
            "cs-CZ" -> "捷克语(捷克)"
            "da-DK" -> "丹麦语(丹麦)"
            "en-US" -> "英语(美国)"
            "en-AU" -> "英语(澳大利亚)"
            "en-CA" -> "英语(加拿大)"
            "en-GB" -> "英语(英国)"
            "en-GH" -> "英语(加纳)"
            "en-IE" -> "英语(爱尔兰)"
            "en-IN" -> "英语(印度)"
            "en-KE" -> "英语(肯尼亚)"
            "en-NG" -> "英语(尼日利亚)"
            "en-SG" -> "英语(新加坡)"
            "en-NZ" -> "英语(新西兰)"
            "en-PH" -> "英语(菲律宾)"
            "en-TZ" -> "英语(坦桑尼亚)"
            "en-ZA" -> "英语(南非)"
            "fi-FI" -> "芬兰语(芬兰)"
            "fr-FR" -> "法语(法国)"
            "fr-CA" -> "法语(加拿大)"
            "el-GR" -> "希腊语(希腊)"
            "de-DE" -> "德语(德国)"
            "he-IL" -> "希伯来语(以色列)"
            "hi-IN" -> "印地语(印度)"
            "hr-HR" -> "克罗地亚语(克罗地亚)"
            "hu-HU" -> "匈牙利语(匈牙利)"
            "id-ID" -> "印尼语(印尼)"
            "it-IT" -> "意大利语(意大利)"
            "ja-JP" -> "日语(日本)"
            "ko-KR" -> "韩语(韩国)"
            "ms-MY" -> "马来语(马来西亚)"
            "nl-NL" -> "荷兰语(荷兰)"
            "nb-NO" -> "挪威语(挪威)"
            "pl-PL" -> "波兰语(波兰)"
            "pt-PT" -> "葡萄牙语(葡萄牙)"
            "pt-BR" -> "葡萄牙语(巴西)"
            "ro-RO" -> "罗马尼亚语(罗马尼亚)"
            "ru-RU" -> "俄语(俄罗斯)"
            "es-ES" -> "西班牙语(西班牙)"
            "es-US" -> "西班牙语(美国)"
            "es-AR" -> "西班牙语(阿根廷)"
            "es-BO" -> "西班牙语(玻利维亚)"
            "es-CL" -> "西班牙语(智利)"
            "es-CO" -> "西班牙语(哥伦比亚)"
            "es-CR" -> "西班牙语(哥斯达黎加)"
            "es-DO" -> "西班牙语(多米尼克)"
            "es-EC" -> "西班牙语(厄瓜多尔)"
            "es-GT" -> "西班牙语(危地马拉)"
            "es-HN" -> "西班牙语(洪都拉斯)"
            "es-MX" -> "西班牙语(墨西哥)"
            "es-NI" -> "西班牙语(尼加拉瓜)"
            "es-PA" -> "西班牙语(巴拿马)"
            "es-PE" -> "西班牙语(秘鲁)"
            "es-PR" -> "西班牙语(波多黎各)"
            "es-PY" -> "西班牙语(巴拉圭)"
            "es-SV" -> "西班牙语(萨尔瓦多)"
            "es-UY" -> "西班牙语(乌拉圭)"
            "es-VE" -> "西班牙语(委内瑞拉)"
            "sk-SK" -> "斯洛伐克语(斯洛伐克)"
            "sl-SI" -> "斯洛文尼亚语(斯洛文尼亚)"
            "sv-SE" -> "瑞典语(瑞典)"
            "ta-IN" -> "泰米尔语(印度)"
            "ta-SG" -> "泰米尔语(新加坡)"
            "ta-LK" -> "泰米尔语(斯里兰卡)"
            "ta-MY" -> "泰米尔语(马来西亚)"
            "te-IN" -> "泰卢固语(印度)"
            "th-TH" -> "泰语(泰国)"
            "tr-TR" -> "土耳其语(土耳其)"
            "vi-VN" -> "越南语(越南)"
            "ur-PK" -> "乌尔都语(巴基斯坦)"
            "ur-IN" -> "乌尔都语(印度)"
            "uk-UA" -> "乌克兰语(乌克兰)"
            "is-IS" -> "冰岛语(冰岛)"
            "fil-PH" -> "菲律宾语(菲律宾)"
            "my-MM" -> "缅甸语(缅甸)"
            "fa-IR" -> "波斯语"
            else -> return "未知"
        }
    }

    fun getTapToSpeakString(lanCode: String): String {
        return when (lanCode.split("-")[0]) {
            "ar" -> "انقر فوق الميكروفون أو سماعة الرأس أدناه للتحدث" // 阿拉伯语的“阿拉伯语” 以下同理
            "bg" -> "Щракнете върху микрофона или слушалките по-долу, за да говорите"
            "ca" -> "Feu clic al micròfon o als auriculars a continuació per parlar"
            "cs" -> "Pro zahájení hovoru poklepejte na mikrofon níže nebo na sluchátko do uší"
            "da" -> "Klik på mikrofonen eller headsettet nedenfor for at tale"
            "de" -> "Auf das Mikrofon unten oder Ohrhörer tippen, um zu sprechen."
            "el" -> "Κάντε κλικ στο μικρόφωνο ή στο ακουστικό παρακάτω για να μιλήσετε"
            "en" -> "Tap the microphone below or the earbud to start speaking"
            "es" -> "Escoja el idoma pulsando el micrófono más abajo o hable por el audífono"
            "zh" -> if (lanCode == "zh-TW") "點擊下方麥克風或者耳機說話" else "点击下方麦克风或者耳机说话"
            "fi" -> "Napsauta alla olevaa mikrofonia tai kuulokkeita puhuaksesi"
            "fr" -> "Appuyez sur le microphone ci-dessous ou sur l'écouteur pour commencer à parler."
            "he" -> "לחץ על המיקרופון או האוזניות למטה כדי לדבר"
            "hi" -> "बोलने के लिए नीचे दिए गए माइक्रोफ़ोन या हेडसेट पर क्लिक करें"
            "hr" -> "Pritisnite mikrofon ili slušalice ispod da biste govorili"
            "hu" -> "A beszédhez kattintson az alábbi mikrofonra vagy headsetre"
            "id" -> "Klik mikrofon atau headset di bawah untuk berbicara"
            "it" -> "Tocca il microfono in basso o l'auricolare per iniziare a parlare"
            "ja" -> "下のマイクまたはイヤホンをタップして通話を開始します。"
            "ko" -> "아래의 마이크 또는 헤드셋을 클릭하여 말하세요."
            "ms" -> "Klik mikrofon atau set kepala di bawah untuk bercakap"
            "nl" -> "Klik op de microfoon of headset hieronder om te spreken"
            "nb" -> "Klikk på mikrofonen eller hodesettet nedenfor for å snakke"
            "pl" -> "Kliknij mikrofon lub zestaw słuchawkowy poniżej, aby mówić"
            "pt" -> "Toque no microfone em baixo ou nos auriculares para começar a falar"
            "ro" -> "Faceți clic pe microfonul sau căștile de mai jos pentru a vorbi"
            "ru" -> "Нажмите на микрофон внизу или на наушник, чтобы начать говорить"
            "sk" -> "Klepnutím na mikrofón nižšie alebo na slúchadlo začnite hovoriť"
            "sl" -> "Če želite začeti z govorom, tapnite mikrofon spodaj ali ušesno slušalko"
            "sv" -> "Klicka på mikrofonen eller headsetet nedan för att tala"
            "ta" -> "பேசுவதற்கு கீழே உள்ள மைக்ரோஃபோன் அல்லது ஹெட்செட்டைக் கிளிக் செய்யவும்"
            "th" -> "แตะไมโครโฟนด้านล่างหรือหูฟังเอียร์บัดเพื่อเริ่มพูด"
            "tr" -> "Konuşmak için aşağıdaki mikrofona veya kulaklığa tıklayın"
            "vi" -> "Nhấp vào micrô hoặc tai nghe bên dưới để nói"
            "uk" -> "Натисніть мікрофон або гарнітуру нижче, щоб говорити"
            "is" -> "Smelltu á hljóðnemann eða heyrnartólið fyrir neðan til að tala"
            "fil" -> "I-click ang mikropono o headset sa ibaba para magsalita"
            "ur" -> "بولنے کے لیے نیچے مائیکروفون یا ہیڈسیٹ پر کلک کریں۔"
            "te" -> "మాట్లాడటానికి దిగువ మైక్రోఫోన్ లేదా హెడ్\u200Cసెట్\u200Cను క్లిక్ చేయండి"
            "fa" -> "برای صحبت کردن روی میکروفون یا هدفون زیر کلیک کنید"
            else -> return "Tap the microphone below or the earbud to start speaking"
        }
    }

    /**
     * 获取Zero的「点击下方麦克风说话」的提示，因为Zero无法点击耳机说话，所以和耳机的提示区分开
     * @param lanCode String  语言码，带前缀或者不带前缀都可以： ex.zh-CN/zh/en-US/en
     * @return String 「点击下方麦克风说话」的每个语种的提示文字
     */
    fun getTapToSpeakStringZero(lanCode: String): String {
        // 码表可以在这里找 https://zh.wikipedia.org/wiki/ISO_639-1
        return when (lanCode.split("-")[0]) {
            "zh" -> if (lanCode == "zh-TW") "點擊下方麥克風說話" else "点击下方麦克风说话"
            "ar" -> "انقر فوق الميكروفون أدناه للتحدث" // 阿拉伯语的“阿拉伯语” 以下同理
            "bg" -> "Кликнете върху микрофона по-долу, за да говорите" // 保加利亚语
            "ca" -> "Feu clic al micròfon de sota per parlar" // 加泰罗尼亚语、加泰隆语
            "cs" -> "Chcete-li mluvit, klikněte na mikrofon níže" // 捷克语
            "da" -> "Klik på mikrofonen nedenfor for at tale"  // 丹麦语
            "de" -> "Klicken Sie unten auf das Mikrofon, um zu sprechen" // 德语
            "el" -> "Κάντε κλικ στο μικρόφωνο παρακάτω για να μιλήσετε" // 希腊语
            "en" -> "Click the microphone below to speak" // 英语
            "es" -> "Haga clic en el micrófono de abajo para hablar" // 西班牙语
            "fi" -> "Napsauta alla olevaa mikrofonia puhuaksesi"  // 芬兰语
            "fr" -> "Cliquez sur le microphone ci-dessous pour parler"  // 法语
            "he" -> "לחץ על המיקרופון למטה כדי לדבר"  // 希伯来语
            "hi" -> "बोलने के लिए नीचे दिए गए माइक्रोफ़ोन पर क्लिक करें"  // 印地语
            "hr" -> "Pritisnite mikrofon ispod da biste govorili"  // 克罗地亚语
            "hu" -> "A beszédhez kattintson az alábbi mikrofonra"  // 匈牙利语
            "id" -> "Klik mikrofon di bawah untuk berbicara"  // 印尼语
            "it" -> "Fai clic sul microfono in basso per parlare"  // 意大利语
            "ja" -> "下のマイクをクリックして話してください"  // 日语
            "ko" -> "아래 마이크를 클릭하여 말하세요."  // 韩语、朝鲜语
            "ms" -> "Klik mikrofon di bawah untuk bercakap"   // 马来语
            "nl" -> "Klik op de microfoon hieronder om te spreken"  // 荷兰语
            "nb" -> "Klikk på mikrofonen nedenfor for å snakke"  // 挪威语
            "pl" -> "Kliknij mikrofon poniżej, aby mówić"  // 波兰语
            "pt" -> "Clique no microfone abaixo para falar"   // 葡萄牙语
            "ro" -> "Faceți clic pe microfonul de mai jos pentru a vorbi"  // 罗马尼亚语
            "ru" -> "Нажмите на микрофон ниже, чтобы говорить"  // 俄语
            "sk" -> "Ak chcete hovoriť, kliknite na mikrofón nižšie"   // 斯洛伐克语
            "sl" -> "Če želite govoriti, kliknite spodnji mikrofon"  // 斯洛文尼亚语
            "sv" -> "Klicka på mikrofonen nedan för att tala"  // 瑞典语
            "ta" -> "பேச கீழே உள்ள மைக்ரோஃபோனைக் கிளிக் செய்யவும்"  // 泰米尔语
            "th" -> "คลิกไมโครโฟนด้านล่างเพื่อพูด"  // 泰语
            "tr" -> "Konuşmak için aşağıdaki mikrofona tıklayın"  // 土耳其语
            "vi" -> "Nhấp vào micrô bên dưới để nói"  // 越南语
            "uk" -> "Натисніть мікрофон нижче, щоб говорити"  // 乌克兰语
            "is" -> "Smelltu á hljóðnemann hér að neðan til að tala"  // 冰岛语
            "fil" -> "I-click ang mikropono sa ibaba para magsalita"  // 菲律宾语
            "ur" -> "بولنے کے لیے نیچے مائیکروفون پر کلک کریں۔"  // 乌尔都语
            "te" -> "మాట్లాడటానికి దిగువ మైక్రోఫోన్\u200Cను క్లిక్ చేయండి"  // 泰卢固语
            "fa" -> "برای صحبت کردن روی میکروفون زیر کلیک کنید"  // 波斯语
            else -> return "Click the microphone below to speak"  // 默认返回英语的翻译
        }
    }

    fun getPleaseSpeakString(lanCode: String): String {
        return when (lanCode.split("-")[0]) {
            "ar" -> "....تكلم XXX من فضلك" // 阿拉伯语的“阿拉伯语” 以下同理
            "bg" -> "Говорете XXX, моля..."
            "ca" -> "Digueu XXX..."
            "cs" -> "Prosím mluvte XXX..."
            "da" -> "Sig XXX..."
            "de" -> "Bitte sprechen sie XXX..."
            "el" -> "Παρακαλούμε πείτε XXX..."
            "en" -> "Speak XXX please..."
            "es" -> "Por favor diga XXX..."
            "zh" -> if (lanCode == "zh-TW") "請說XXX..." else "请说XXX..."
            "fi" -> "Ole hyvä ja sano XXX..."
            "fr" -> "S'il vous plaît ditesXXX..."
            "he" -> "XXX אנא אמור"
            "hi" -> "ूँ! कृपया XXXबोलें।..."
            "hr" -> "Recite XXX..."
            "hu" -> "Mondja a XXX kifejezést,kérem..."
            "id" -> "Tolong katakan XXX..."
            "it" -> "Ti prega di dire XXX..."
            "ja" -> "XXXでお話しください..."
            "ko" -> "XXX로 말해 주세요..."
            "ms" -> "Sila nyatakan XXX..."
            "nb" -> "Si XXX..."
            "nl" -> "Zeg alstublieft XXX..."
            "pl" -> "Powiedz XXX..."
            "pt" -> "Por favor, fala XXX..."
            "ro" -> "Vă rugăm să spuneți XXX..."
            "ru" -> "Говорите XXX..."
            "sk" -> "Prosím hovorte XXX..."
            "sl" -> "Govorite XXX prosim..."
            "sv" -> "Vänligen säga XXX..."
            "ta" -> "XXX என்று சொல்லுங்கள்..."
            "th" -> "กรุณาพูด XXX..."
            "tr" -> "Lütfen XXX deyin..."
            "vi" -> "Hãy nói XXX..."
            "uk" -> "будь ласка, розмовляйте XXX...."
            "is" -> "vinsamlegast tala XXX..."
            "fil" -> "mangyaring magsalita ng XXX..."
            "ur" -> "...بولیں XXX برائے مہربانی"
            "te" -> "దయచేసి XXX అని చెప్పండి..."
            "bn" -> "বলুন XXX..."
            "my" -> "XXX ဟု ပြောပါ..."
            "fa" -> "...XXX لطفاً بگویید"
            else -> return "Speak XXX please..."
        }
    }

    fun getSpeakString(lanCode: String): String {
        return when (lanCode.split("-")[0]) {
            "ar" -> "يرجى تقريب الجهاز من مصدر الصوت." // 阿拉伯语的“阿拉伯语” 以下同理
            "bg" -> "Моля, приближете устройството до аудио източника."
            "ca" -> "Si us plau, acosta el dispositiu a la font d'àudio."
            "cs" -> "Přiblížte prosím zařízení k zvukovému zdroji."
            "da" -> "Venligst bring enheden tættere på lydkilden."
            "de" -> "Bitte bringen Sie das Gerät in die Nähe der Audioquelle."
            "el" -> "Παρακαλώ φέρτε τη συσκευή κοντά στην πηγή ήχου."
            "en" -> "Please bring the device close to the audio source."
            "es" -> "Por favor, acerque el dispositivo a la fuente de audio."
            "zh" -> if (lanCode == "zh-TW") "請將設備靠近音頻源。" else "请将设备靠近音频源。"
            "fi" -> "Ole hyvä ja tuo laite lähemmäksi äänilähdettä."
            "fr" -> "Veuillez rapprocher l'appareil de la source audio."
            "he" -> "אנא הביאו את המכשיר לקרבת מקור השמע."
            "hi" -> "कृपया उपकरण को ऑडियो स्रोत के पास ले जाएं।"
            "hr" -> "Molimo približite uređaj izvoru zvuka."
            "hu" -> "Kérjük, hozza közelebb az eszközt az audioforráshoz."
            "id" -> "Silakan mendekatkan perangkat ke sumber audio."
            "it" -> "Si prega di avvicinare il dispositivo alla fonte audio."
            "ja" -> "デバイスを音源に近づけてください。"
            "ko" -> "기기를 오디오 소스에 가깝게 가져다 주세요."
            "ms" -> "Sila mendekatkan peranti ke sumber audio."
            "nb" -> "Vennligst nær enheten til lydkilden."
            "nl" -> "Breng het apparaat dichter bij de audiobron."
            "pl" -> "Proszę zbliżyć urządzenie do źródła dźwięku."
            "pt" -> "Por favor, aproxime o dispositivo à fonte de áudio."
            "ro" -> "Vă rugăm să apropiați dispozitivul de sursa audio."
            "ru" -> "Пожалуйста, приблизьте устройство к источнику аудио."
            "sk" -> "Prosím, priblížte zariadenie k zvukovému zdroju."
            "sl" -> "Prosim, približajte napravo k viru zvoka."
            "sv" -> "Vänligen placera enheten nära ljudkällan."
            "ta" -> "தயவுசெய்து சாதனத்தை ஒலி மூலத்திற்கு அருகில் கொண்டு செல்லவும்."
            "th" -> "โปรดเอาอุปกรณ์ใกล้กับแหล่งเสียง"
            "tr" -> "Lütfen cihazı ses kaynağına yaklaştırın."
            "vi" -> "Vui lòng mang thiết bị gần nguồn âm thanh."
            "uk" -> "Будь ласка, наблизьте пристрій до джерела звуку."
            "is" -> "Vinsamlegast kærðu tækið nær hljóðuppspretti."
            "fil" -> "Mangyaring dalhin ang aparato malapit sa pinagmulan ng tunog."
            "ur" -> "براہ کرم آلہ کو آڈیو سورس کے قریب لے جائیں۔"
            "te" -> "దయచేసి పరిష్కరించు ఉపకరణాన్ని ఆడియో మూలంలో కొనసాగించండి."
            "bn" -> "দয়া করে ডিভাইসটি অডিও সোর্সের কাছে রাখুন."
            "my" -> "စက်ပစ္စည်းကို အသံထွက်ရာနေရာနှင့် နီးကပ်စွာထားပါ။"
            "fa" -> "لطفاً دستگاه را به منبع صوتی نزدیکتر کنید"
            else -> return "Please bring the device close to the audio source."
        }
    }

    fun getSpeechString(lanCode: String): String {
        return when (lanCode.split("-")[0]) {
            "ar" -> "الرجاء النقر على \"ابدأ\" للبدء."
            "bg" -> "Моля, кликнете \"Старт\", за да започнете."
            "ca" -> "Si us plau, feu clic a \"Inicia\" per començar."
            "cs" -> "Klikněte na \"Start\" pro zahájení."
            "da" -> "Klik venligst på \"Start\" for at begynde."
            "de" -> "Bitte klicken Sie auf \"Start\", um zu beginnen."
            "el" -> "Παρακαλώ κάντε κλικ στο \"Έναρξη\" για να ξεκινήσετε."
            "en" -> "Please click \"Start\" to begin."
            "es" -> "Por favor, haz clic en \"Iniciar\" para comenzar."
            "zh" -> if (lanCode == "zh-TW") "請點選開始演講。" else "请点击开始演讲。"
            "fi" -> "Napsauta \"Aloita\" aloittaaksesi."
            "fr" -> "Veuillez cliquer sur « Démarrer » pour commencer."
            "he" -> "אנא לחץ על \"התחל\" כדי להתחיל."
            "hi" -> "शुरू करने के लिए कृपया \"प्रारंभ\" पर क्लिक करें।"
            "hr" -> "Kliknite \"Start\" za početak."
            "hu" -> "Kérjük, kattintson a \"Start\" gombra a kezdéshez."
            "id" -> "Silakan klik \"Mulai\" untuk memulai."
            "it" -> "Per favore, clicca su \"Inizia\" per cominciare."
            "ja" -> "開始するには「スタート」をクリックしてください。"
            "ko" -> "시작하려면 \"시작\"을 클릭하세요."
            "ms" -> "Sila klik \"Mula\" untuk memulakan."
            "nb" -> "Vennligst klikk på \"Start\" for å begynne."
            "nl" -> "Klik op \"Start\" om te beginnen."
            "pl" -> "Proszę kliknąć \"Start\", aby rozpocząć."
            "pt" -> "Por favor, clique em \"Iniciar\" para começar."
            "ro" -> "Vă rugăm să faceți clic pe \"Start\" pentru a începe."
            "ru" -> "Пожалуйста, нажмите \"Начать\", чтобы начать."
            "sk" -> "Kliknite na \"Štart\" pre začatie."
            "sl" -> "Prosimo, kliknite \"Začni\" za začetek."
            "sv" -> "Klicka på \"Start\" för att börja."
            "ta" -> "தொடங்க \"தொடங்கு\" என்பதைக் கிளிக் செய்யவும்."
            "th" -> "กรุณาคลิก \"เริ่มต้น\" เพื่อเริ่มต้น"
            "tr" -> "Başlamak için lütfen \"Başlat\"a tıklayın."
            "vi" -> "Vui lòng nhấp vào \"Bắt đầu\" để bắt đầu."
            "uk" -> "Будь ласка, натисніть \"Почати\", щоб розпочати."
            "is" -> "Vinsamlegast smelltu á \"Byrja\" til að hefja."
            "fil" -> "Paki-click ang \"Start\" para magsimula."
            "ur" -> "براہ کرم شروع کرنے کے لیے \"شروع کریں\" پر کلک کریں"
            "te" -> "ప్రారంభించడానికి దయచేసి \"ప్రారంభించు\" పై క్లిక్ చేయండి."
            "bn" -> "দয়া করে বক্তৃতা \"শুরু করতে\" ক্লিক করুন."
            "my" -> "စတင်ဟောပြောရန် နှိပ်ပါ။"
            "fa" -> "لطفاً برای شروع صحبت کلیک کنید"
            else -> return "Please click \"Start\" to begin."
        }
    }

    fun getVideoSpeakString(lanCode: String): String {
        return when (lanCode.split("-")[0]) {
            "ar" -> "بعد اكتشاف تشغيل الصوت، تبدأ الترجمة تلقائيًا." // 阿拉伯语的“阿拉伯语” 以下同理
            "bg" -> "След като бъде открито възпроизвеждане на аудио, преводът започва автоматично."
            "ca" -> "Després de detectar la reproducció d’àudio, la traducció comença automàticament."
            "cs" -> "Po detekci přehrání zvuku se překlad spustí automaticky."
            "da" -> "Oversættelsen starter automatisk, når lydafspilning detekteres."
            "de" -> "Die Übersetzung startet automatisch, wenn die Audiowiedergabe erkannt wird."
            "el" -> "Μετά την ανίχνευση της αναπαραγωγής ήχου, η μετάφραση ξεκινά αυτόματα."
            "en" -> "After detecting audio playback, translation starts automatically."
            "es" -> "Después de detectar la reproducción de audio, la traducción comienza automáticamente."
            "zh" -> if (lanCode == "zh-TW") "偵測到音訊播放後，翻譯自動開始。" else "检测到音频播放后，翻译自动开始。"
            "fi" -> "Kun äänentoisto on havaittu, käännös alkaa automaattisesti."
            "fr" -> "Après avoir détecté la lecture audio, la traduction démarre automatiquement."
            "he" -> "כאשר מתגלה השמעת שמע, התרגום מתחיל אוטומטית."
            "hi" -> "ऑडियो प्लेबैक का पता लगाने के बाद, अनुवाद स्वचालित रूप से शुरू हो जाता है"
            "hr" -> "Nakon otkrivanja zvučne reprodukcije, prijevod započinje automatski."
            "hu" -> "Amikor az audiós játszást észleli, a fordítás kezdődik automatikusan ."
            "id" -> "Setelah pemutaran audio terdeteksi, penerjemahan dimulai secara otomatis."
            "it" -> "Dopo aver rilevato la riproduzione audio, la traduzione inizia automaticamente."
            "ja" -> "音声を検知したら、通訳が自動で起動します。"
            "ko" -> "오디오 재생이 감지되면 번역이 자동으로 시작됩니다."
            "ms" -> "Selepas main balik audio dikesan, penterjemahan bermula secara automatik."
            "nb" -> "Oversettelsen starter automatisk når lydavspilling oppdages."
            "nl" -> "De vertaling start automatisch wanneer het geluidsafspelen wordt gedetecteerd."
            "pl" -> "Tłumaczenie rozpocznie się automatycznie w momencie wykrycia odtwarzania audio."
            "pt" -> "Após detectar a reprodução de áudio, a tradução é iniciada automaticamente."
            "ro" -> "După detectarea redării audio, traducerea începe automat."
            "ru" -> "После обнаружения воспроизведения аудио перевод начинается автоматически."
            "sk" -> "Po zistení prehrávania zvuku sa preklad spustí automaticky."
            "sl" -> "Po zaznavanju predvajanja zvoka se prevod začne samodejno."
            "sv" -> "Översättningen startar automatiskt när ljuduppspelning detekteras."
            "ta" -> "ஆடியோ பிளேபேக்கைக் கண்டறிந்த பிறகு, மொழிபெயர்ப்பு தானாகவே தொடங்கும்."
            "th" -> "เมื่อพบการเล่นเสียง จะเริ่มแปลอัตโนมัติ"
            "tr" -> "Ses oynatımı algılandıktan sonra çeviri otomatik olarak başlar."
            "vi" -> "Sau khi phát hiện phát lại âm thanh, quá trình dịch sẽ tự động bắt đầu."
            "uk" -> "Після виявлення відтворення аудіо переклад починається автоматично."
            "is" -> "Eftir að hljóðspilun hefur verið greind byrjar þýðingin sjálfkraf."
            "fil" -> "Matapos madetect ang audio playback, awtomatikong nagsisimula ang pagsasalin."
            "ur" -> "آڈیو پلے بیک کا پتہ لگانے کے بعد، ترجمہ خود بخود شروع ہو جاتا ہے۔"
            "te" -> "ఆడియో ప్లేబ్యాక్\u200Cని గుర్తించిన తర్వాత, అనువాదం స్వయంచాలకంగా ప్రారంభమవుతుంది."
            "bn" -> "অডিও প্লে শনাক্ত হলে, অনুবাদ স্বয়ংক্রিয়ভাবে শুরু হবে।."
            "my" -> "အသံဖွင့်ထားသည်ကိုတွေ့ရှိပါက ဘာသာပြန်ခြင်းသည် အလိုအလျောက် စတင်မည်ဖြစ်သည်။"
            "fa" -> "با تشخیص صدا، ترجمه به طور خودکار شروع می\u200Cشود"
            else -> return "After detecting audio playback, translation starts automatically."
        }
    }

    /// “正在听”提示
    fun getListeningTip(code: String): String {
        if (code.contains("TW") || code.contains("HK")) {
            return "正在聽..."
        }
        val brief = code.split("-")[0]
        if (brief == "ar") { //阿拉伯 需反转
            return "الاستماع ..."
        } else if (brief == "bg") {
            return "слушам..."
        } else if (brief == "ca") {
            return "escoltant..."
        } else if (brief == "cs") {
            return "Naslouchání..."
        } else if (brief == "da") {
            return "hører efter..."
        } else if (brief == "de") {
            return "Hören..."
        } else if (brief == "el") {
            return "ακούγοντας..."
        } else if (brief == "en") {
            return "Listening..."
        } else if (brief == "es") {
            return "escuchar..."
        } else if (brief == "zh") {
            return "正在听..."
        } else if (brief == "fi") {
            return "kuuntelen..."
        } else if (brief == "fr") {
            return "écoute..."
        } else if (brief == "hu") {
            return "hallgat..."
        } else if (brief == "id") {
            return "Mendengarkan..."
        } else if (brief == "he") { // 需反转
            return "הַקשָׁבָה..."
        } else if (brief == "hi") {
            return "सुनना..."
        } else if (brief == "hr") {
            return "slušanje..."
        } else if (brief == "it") {
            return "ascoltando..."
        } else if (brief == "ja") {
            return "聞いている..."
        } else if (brief == "ko") {
            return "청취..."
        } else if (brief == "ms") {
            return "mendengar..."
        } else if (brief == "nl") {
            return "luisteren..."
        } else if (brief == "nb") {
            return "lytter..."
        } else if (brief == "pl") {
            return "słuchający..."
        } else if (brief == "pt") {
            return "audição..."
        } else if (brief == "ro") {
            return "ascultare..."
        } else if (brief == "ru") {
            return "слушаю..."
        } else if (brief == "sk") {
            return "počúvanie..."
        } else if (brief == "sl") {
            return "poslušanje..."
        } else if (brief == "sv") {
            return "lyssnande..."
        } else if (brief == "ta") {
            return "கேட்கிறது..."
        } else if (brief == "th") {
            return "การฟัง..."
        } else if (brief == "tr") {
            return "dinleme..."
        } else if (brief == "vi") {
            return "đang nghe..."
        } else if (brief == "uk") {
            return "слухаю..."
        } else if (brief == "is") {
            return "að hlusta..."
        } else if (brief == "fil") {
            return "nakikinig..."
        } else if (brief == "ur") { //需反转
            return "سن رہا ہے..."
        } else if (brief == "te") {
            return "వింటూ..."
        } else if (brief == "my") {
            return "နားထောင်ခြင်း..."
        } else if (brief == "fa") {
            return "در حال گوش دادن..."
        } else {
            return "Listening..."
        }
    }

}