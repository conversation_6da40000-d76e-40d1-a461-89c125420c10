package co.timekettle.module_translate.tools

import SpConstant
import android.content.Context
import android.util.Log
import co.timekettle.btkit.BleUtil
import co.timekettle.btkit.bean.RawBlePeripheral
import co.timekettle.module_translate.bean.SpeechConfig
import co.timekettle.sip.utils.GsonUtil
import co.timekettle.speech.AiSpeechManager
import co.timekettle.speech.AudioChannel
import co.timekettle.speech.BuildConfig
import co.timekettle.speech.EngineHost
import co.timekettle.speech.ISpeechConstant
import co.timekettle.speech.OfflineManager
import co.timekettle.speech.RecordManager
import co.timekettle.speech.SpeechError
import co.timekettle.speech.SpeechRequest
import co.timekettle.speech.utils.AiSpeechLogUtil
import co.timekettle.speech.utils.BleLossStatistics
import co.timekettle.tmkengine.JsonSynthesizeRequest
import co.timekettle.tmkengine.TmkSpeechClient
import com.google.gson.reflect.TypeToken
import com.timekettle.upup.base.BaseApp
import com.timekettle.upup.base.utils.SpUtils
import com.timekettle.upup.base.utils.logD
import com.timekettle.upup.base.utils.logE
import com.timekettle.upup.comm.constant.SpKey
import com.timekettle.upup.comm.constant.TmkProductType
import com.timekettle.upup.comm.constant.TranslateMode
import com.timekettle.upup.comm.service.home.HomeServiceImplWrap
import com.timekettle.upup.comm.service.trans.TransServiceImplWrap.getSpeechBaseUrl
import com.timekettle.upup.comm.utils.SoundUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * 模式工具, 一般使用步骤:
 * enterMode()
 * startMode()
 * ...
 * ... 随时热更新通道信息
 * updateChannel()
 * ...
 * ... 暂停后更新通道信息(语言等)
 * pauseMode()
 * updateChannel()
 * resumeMode()
 * ... 切换说话通道
 * switchToChannel()
 * ...
 * pauseMode()
 * resumeMode()
 * ...
 * stopMode()
 * exitMode()
 */
open class ModeUtil {
    var selfCode = "zh-CN"
    var otherCode = "en-US"
    open var modeDesc = "wt2(001/004)同传模式" // 注意修改, 现绑定了判断, 如丢包统计, 后续修改成<模式的枚举>
    open var TAG = "ModeUtil"

    /// 每个产品录音(单个包)有默认的 vad 最小能量的阈值, 此值根据产品和模式变化, 如 W3 同传 是 AudioChannel.defalutMinVadEnergy / 3, W3其他模式时 AudioChannel.defalutMinVadEnergy / 10;
    var minVadEnergy = AudioChannel.defalutMinVadEnergy
    var mProduct: TmkProductType? = HomeServiceImplWrap.getUserProduct()
    var mOnTouchEventListener: ((directionRole: RawBlePeripheral.Role, chKey: String?) -> Unit)? = null

    fun setOnTouchEventListener(listener: (directionRole: RawBlePeripheral.Role, chKey: String?) -> Unit) {
        mOnTouchEventListener = listener
    }

    var statistics: BleLossStatistics? = null

    // 进入模式, 初始化
    open fun enterMode(context: Context, offlineConfig: Map<String, Any>?) {
        //查看ip列表缓存，如果缓存为空，则重新拉取ip列表并更新缓存
        val type = object : TypeToken<ArrayList<EngineHost>>() {}.type
        val hosts: ArrayList<EngineHost> = GsonUtil.fromJson(SpUtils.getString(SpKey.CATCH_HOSTS,"[]"), type)

        enableDebugAudioFile = SpUtils.getBoolean(SpKey.IS_RECORD_AUDIO_OPEN, false)
        // 进入模式页
        AiSpeechLogUtil.setLogLevel(2)
        AiSpeechLogUtil.setLogCallback { level, tag, msg, tr ->
            when (level) {
                1 -> logE(msg, tag)
                2 -> logD(msg, tag)
            }
        }

        if (SpUtils.getBoolean(SpKey.IS_DEBUG_STATUS, false) || BuildConfig.DEBUG) { // <<<< 测试面板打开时, 才监听和显示
            val host = SpUtils.getString(SpKey.TMK_ENGINE_HOST_IP, "")
            if (host.isNotEmpty()) {
                Log.d(TAG, "enterMode: 选择了指定服务器: $host")
                AiSpeechManager.shareInstance()
                    .create(context, "9A5C1E41066458D50F91636A111FED89", EngineHost(host))
            } else {
                Log.d(TAG, "enterMode: 没有指定服务器，由程序自己选择")
                AiSpeechManager.shareInstance().create(context, "9A5C1E41066458D50F91636A111FED89", EngineHost(""))
                if (hosts.isEmpty()) {
                    SpeechUtil.fetchHosts(getSpeechBaseUrl() + SpConstant.HOST_ROUTES + "?enableSSL=true")
                }else{
                    AiSpeechManager.shareInstance().setSpecificHost(hosts[0])
                }
            }
        } else { // 调试面板未打开，走正常的逻辑
            AiSpeechManager.shareInstance().create(context, "9A5C1E41066458D50F91636A111FED89", EngineHost(""))
            if (hosts.isEmpty()) {
                SpeechUtil.fetchHosts(getSpeechBaseUrl() + SpConstant.HOST_ROUTES + "?enableSSL=true")
            }else{
                AiSpeechManager.shareInstance().setSpecificHost(hosts[0])
            }
        }

        BaseApp.mCoroutineScope.launch(Dispatchers.IO) {
            //进入模式前，先同步后台switch接口配置
            SpeechUtil.fetchConfig(getSpeechBaseUrl() + SpConstant.HOST_SWITCH) { config: SpeechConfig?, speechError: SpeechError? ->
                if (speechError == null) {
                    if (config != null) {
                        AiSpeechManager.shareInstance().enableSpeechTranslation(config!!.enableSpeechTranslation)
                        AiSpeechManager.shareInstance().enableOpusTransmission(config.enableOpusTransmission)
                        AiSpeechManager.shareInstance().setNoOpusTTSCodeArray(config.noOpusTTSCodeArray)
                        TmkSpeechClient.setShouldUploadKafkaLog(config.enableKafkaLog)
                        onStreamTtsState(config.enableStreamTts)
                        onMeetStreamTtsState(config.enableMeetStreamTts)
                    }
                } else {
                    logE("fetchConfig error: ${speechError.code} ${speechError.desc}")
                }

                null
            }
        }

        statistics = BleLossStatistics(context, SpUtils.getBoolean("CACHE_ALL_FILE",false))
    }

    open fun onStreamTtsState(state: Boolean) {

    }

    open fun onMeetStreamTtsState(state: Boolean) {

    }

    // 进入模式, 初始化
    open fun enterMode(context: Context) {
        this.enterMode(context, null)
    }

    // 退出模式, 反初始化
    open fun exitMode() {
        // 退出模式页
        RecordManager.shareInstance().stop()
        AiSpeechManager.shareInstance().destroy()
        closeOffline()
        // FIXME: 需要放置在 ble 设备相关的模式类下面 
        BleUtil.shared.setTouchEventCallback(null)
    }

    open fun startMode(listener: AiSpeechManager.Listener) {
        logD("startMode", TAG)
        this.startMode(this.selfCode, this.otherCode, listener)
    }

    open fun startMode(selfCode: String, listener: AiSpeechManager.Listener) {
        this.selfCode = selfCode
//        this.otherCode = otherCode
        logD("开始模式，selfCode：${selfCode} otherCode：${otherCode}")
        AiSpeechManager.shareInstance().setListener(listener)
        AiSpeechManager.shareInstance().setTranslateDisabled(true)
        AiSpeechManager.shareInstance().isSynthesizeDisabled = true
    }

    open fun startMode(selfCode: String, otherCode: String, listener: AiSpeechManager.Listener?) {
        this.selfCode = selfCode
        this.otherCode = otherCode
        logD("开始模式，selfCode：${selfCode} otherCode：${otherCode}")
        AiSpeechManager.shareInstance().setListener(listener)
        AiSpeechManager.shareInstance().setTranslateDisabled(false)
        AiSpeechManager.shareInstance().isSynthesizeDisabled = false
        SoundUtil.requestAudioFocus()
    }

    open fun stopMode() {
        logD("stopMode", TAG)
        RecordManager.shareInstance().stop()
        AiSpeechManager.shareInstance().setListener(null)
    }

    // 暂停录音, 如用于进入设置页面等
    open fun pauseMode() {
        RecordManager.shareInstance().muteAllRecorder()
    }

    // 恢复录音, 如用于进入设置页面后回来模式页等
    open fun resumeMode() {
        RecordManager.shareInstance().unmuteAllRecorder()
    }

    // 会议采访特定接口, 录音暂停
    open fun pauseMode2() {
        RecordManager.shareInstance().pauseRecorder(ISpeechConstant.RECORDER.USB.toString())
    }

    // 会议采访特定接口, 录音恢复
    open fun resumeMode2() {
        RecordManager.shareInstance().resumeRecorder(ISpeechConstant.RECORDER.USB.toString())
    }

    // 切换通道, 通常用于触控模式
    open fun switchToChannel(chkey: String) {
        AiSpeechManager.shareInstance().disableAllAudioChannel()
        AiSpeechManager.shareInstance().enableAudioChannel(chkey)
    }

    // 启用通道, 通常用于触控模式
    fun enableChannel(chkey: String) {
        AiSpeechManager.shareInstance().enableAudioChannel(chkey)
    }

    // 禁用通道, 通常用于触控模式
    fun disableChannel(chkey: String) {
        AiSpeechManager.shareInstance().disableAudioChannel(chkey)
    }

    // 通道是否已禁用, 通常用于触控模式
    fun isEnableChannel(chkey: String): Boolean {
        return AiSpeechManager.shareInstance().isEnableAudioChannel(chkey)
    }

    // 通过通道的role获取整个通道对象
    fun getPipesByRole(role: AudioChannel.Role): MutableList<AudioChannel> {
        return AiSpeechManager.shareInstance().getAudioChannels(role.toString())
    }

    // 禁用所有通道，通常用于同传模式
    fun disableAllChannel() {
        AiSpeechManager.shareInstance().disableAllAudioChannel()
    }

    // 启用所有通道，通常用于同传模式
    fun enableAllChannel() {
        AiSpeechManager.shareInstance().enableAllAudioChannel()
    }

    // 是否有正在使用的通道
    fun hasActiveChannel(): Boolean {
        AiSpeechManager.shareInstance().audioChannels.forEach {
            if (it.isEnabled) return true
        }
        return false
    }

    // 全局参数和通道参数更新
    open fun updateChannel(options: Map<String, Any>) {
        AiSpeechManager.shareInstance().updateChannel(options)
    }

    fun updateLang(selfCode: String, otherCode: String) {
        for (channel in AiSpeechManager.shareInstance().audioChannels) {
//            if (channel.role == AudioChannel.Role.Self.toString()) {
                channel.srcCode = selfCode
                channel.dstCode = otherCode
//            } else {
//                channel.srcCode = otherCode
//                channel.dstCode = selfCode
//            }
        }
        this.selfCode = selfCode
        this.otherCode = otherCode
    }

    /**
     * 设置灵敏度的值, 0.01f, 0.1f, 1.0f, 5.0f, 10.0f
     * @param sen 灵敏度倍数
     */
    fun setSensitivity(sen: Float) {
        AiSpeechManager.shareInstance()
            .setMinVadEnergy((sen * minVadEnergy).toInt());
    }

    /**
     * 设置断句时长, 一般的等级为 0.5f, 1.0f, 2.0f, 2.5f, 3.0f
     * @param duration 断句时长, 单位 ms
     */
    fun setBreakTime(duration: Int) {
        AiSpeechManager.shareInstance().setVadEnd(duration);
    }

    /**
     * 开启自定义翻译
     */
    fun enableCustomMt() {
    }

    /**
     * 关闭自定义翻译
     */
    fun disableCustomMt() {
    }

    /**
     * 设置 TTS 语音类型
     * @param isManVoiceSelf 母语播报是否是男声
     * @param isManVoiceOther 外语播报是否是男声
     * @param isSelf 是否是音色选择页面的 Self 端选择
     */
    fun setVoice(isManVoiceSelf: Boolean, isManVoiceOther: Boolean, isSelf: Boolean = false) {
        logD("set voice isManVoiceSelf = $isManVoiceSelf, isManVoiceOther = $isManVoiceOther, isSelf = $isSelf")
        for (channel in AiSpeechManager.shareInstance().audioChannels) {
            // 一对一模式需要区分两只耳机的 Channel 对应播放音色
            if (HomeServiceImplWrap.getUserModel() == TranslateMode.SIMUL) {
                channel.optsForEngine = HashMap<String?, Any?>().apply {
                    put(
                        SpeechRequest.OptsKeyTtsVoiceName,
                        if (channel.key == "language_choose") {
                            if (isSelf) {
                                if (isManVoiceSelf) JsonSynthesizeRequest.SynthesizeVoiceGender.Male.name
                                else JsonSynthesizeRequest.SynthesizeVoiceGender.Female.name
                            } else {
                                if (isManVoiceOther) JsonSynthesizeRequest.SynthesizeVoiceGender.Male.name
                                else JsonSynthesizeRequest.SynthesizeVoiceGender.Female.name
                            }
                        } else if (channel.role == AudioChannel.Role.Self.toString()) {
                            if (isManVoiceOther) JsonSynthesizeRequest.SynthesizeVoiceGender.Male.name
                            else JsonSynthesizeRequest.SynthesizeVoiceGender.Female.name
                        } else {
                            if (isManVoiceSelf) JsonSynthesizeRequest.SynthesizeVoiceGender.Male.name
                            else JsonSynthesizeRequest.SynthesizeVoiceGender.Female.name
                        }
                    )
                }
            // 音视频模式需要区分两只耳机的 Channel 对应播放音色
            } else if (HomeServiceImplWrap.getUserModel() == TranslateMode.VIDEO) {
                channel.optsForEngine = HashMap<String?, Any?>().apply {
                    put(
                        SpeechRequest.OptsKeyTtsVoiceName,
                        if (channel.key == "language_choose") {
                            if (isSelf) {
                                if (isManVoiceSelf) JsonSynthesizeRequest.SynthesizeVoiceGender.Male.name
                                else JsonSynthesizeRequest.SynthesizeVoiceGender.Female.name
                            } else {
                                if (isManVoiceOther) JsonSynthesizeRequest.SynthesizeVoiceGender.Male.name
                                else JsonSynthesizeRequest.SynthesizeVoiceGender.Female.name
                            }
                        } else if (channel.key == VideoModeUtil.HOST_KEY) {
                            if (isManVoiceOther) JsonSynthesizeRequest.SynthesizeVoiceGender.Male.name
                            else JsonSynthesizeRequest.SynthesizeVoiceGender.Female.name
                        } else {
                            if (isManVoiceSelf) JsonSynthesizeRequest.SynthesizeVoiceGender.Male.name
                            else JsonSynthesizeRequest.SynthesizeVoiceGender.Female.name
                        }
                    )
                }
            // 其他模式统一设置
            } else {
                channel.optsForEngine = HashMap<String?, Any?>().apply {
                    put(
                        SpeechRequest.OptsKeyTtsVoiceName,
                        if (isManVoiceSelf) JsonSynthesizeRequest.SynthesizeVoiceGender.Male.name
                        else JsonSynthesizeRequest.SynthesizeVoiceGender.Female.name
                    )
                }
            }
        }
    }

    /**
     * 设置自定义翻译表 id
     */
    fun setCustomMtTableId(id: Int) {
    }

    /**
     * 翻译接口, 一般用于模式页, 通过通道配置去翻译, 自定义或者其他翻译
     * @param channel 通道
     * @param text 需要翻译的文本
     */
    fun translate(
        channel: AudioChannel,
        text: String,
        code: String,
        dstCode: String,
        callback: (String) -> Unit
    ) {
        AiSpeechManager.shareInstance().translate(channel, text, code, dstCode) {
            callback(it)
            it
        }
    }

    /**
     * 合成播放接口, 一般用于模式页, 通过通道配置去合成并通过通道指定 speaker 播放
     * @param channel 通道
     * @param text 需要合成播放的文本
     */
    fun playText(
        channel: AudioChannel,
        session: Long,
        text: String,
        code: String,
        callback: (Long) -> Unit
    ) {
        AiSpeechManager.shareInstance().playText(channel, session, text, code) {
            callback(it)
            it
        }
    }

    fun isReadyOffline() {
        OfflineManager.getInstance().isReadyOffline(selfCode, otherCode)
    }

    /**
     * 当前语言对是否开启了离线
     */
    fun isEnableOffline(): Boolean {
        return OfflineManager.getInstance().isEnable(selfCode, otherCode)
    }

    /**
     * 当前语言对开启了离线
     */
    fun openOffline(): OfflineManager.CheckResult {
        return OfflineManager.getInstance().openOffline(selfCode, otherCode)
    }

    /**
     * 当前语言对关闭了离线
     */
    fun closeOffline(): Boolean {
        return OfflineManager.getInstance().closeOffline(selfCode, otherCode)
    }

    /**
     * 当前语言对是否支持离线
     */
    open fun isSupportOffline(): Boolean {
        return OfflineManager.getInstance().isSupport(selfCode, otherCode)
    }

    // 全局参数
    companion object {
        /// 生成调试的音频文件
        var enableDebugAudioFile = SpUtils.getBoolean(SpKey.IS_RECORD_AUDIO_OPEN, false)
        fun enableTtsAndPlay() {
            logD("开启TTS")
            AiSpeechManager.shareInstance().isSynthesizeDisabled = false
        }

        fun disableTtsAndPlay() {
            logD("关闭TTS")
            AiSpeechManager.shareInstance().isSynthesizeDisabled = true
        }

        /**
         * 翻译接口, 一般用于历史记录等非模式页
         * @param text 需要翻译的文本
         */
        fun translate(text: String, code: String, dstCode: String, callback: (String) -> Unit) {
            AiSpeechManager.shareInstance().translate(text, code, dstCode) {
                callback(it)
                it // 返回但未
            }
        }

        /**
         * 合成播放接口, 一般用于历史记录等非模式页, 通过指定 speaker 播放
         * @param text 需要合成播放的文本
         * @param code 需要合成播放的文本的语言code
         * @speaker speaker 播放器类型
         * @extRole extRole 排查某类角色播放
         */
        fun playText(
            text: String,
            code: String,
            speaker: String,
            extRole: String,
            callback: () -> Unit
        ) {
            AiSpeechManager.shareInstance().playText(text, code, speaker, extRole) {
                callback()
                it
            }
        }

        fun isReadyOffline(selfCode: String, otherCode: String): OfflineManager.CheckResult {
            return OfflineManager.getInstance().isReadyOffline(selfCode, otherCode)
        }

        // 是否支持离线
        fun isSupportOffline(selfCode: String, otherCode: String): Boolean {
            return OfflineManager.getInstance().isSupport(selfCode, otherCode)
        }

        fun isEnableOffline(selfCode: String, otherCode: String): Boolean {
            return OfflineManager.getInstance().isEnable(selfCode, otherCode)
        }

        fun openOffline(selfCode: String, otherCode: String): OfflineManager.CheckResult {
            return OfflineManager.getInstance().openOffline(selfCode, otherCode)
        }

        fun closeOffline(selfCode: String, otherCode: String): Boolean {
            return OfflineManager.getInstance().closeOffline(selfCode, otherCode)
        }
    }

}

