package co.timekettle.module_translate.ui.activity

import android.annotation.SuppressLint
import android.bluetooth.BluetoothA2dp
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothManager
import android.bluetooth.BluetoothProfile
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import android.provider.Settings
import android.view.KeyEvent
import androidx.activity.viewModels
import androidx.lifecycle.lifecycleScope
import co.timekettle.module_translate.bean.ProductSetting
import co.timekettle.module_translate.bean.SettingEnum
import co.timekettle.module_translate.constant.IntentKey
import co.timekettle.module_translate.databinding.VideoActivitySettingBinding
import co.timekettle.module_translate.tools.ModeUtil
import co.timekettle.module_translate.tools.TransManager
import co.timekettle.module_translate.ui.pop.BreakTimeSettingPop
import co.timekettle.module_translate.ui.pop.FontSizeSettingPop
import co.timekettle.module_translate.ui.pop.PlayContentSettingPop
import co.timekettle.module_translate.ui.pop.PlaySpeedSettingPop
import co.timekettle.module_translate.ui.vm.VideoSettingViewModel
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.NetworkUtils
import com.timekettle.upup.base.BaseApp
import com.timekettle.upup.base.ktx.observeLiveData
import com.timekettle.upup.base.utils.EventBusRegister
import com.timekettle.upup.base.utils.logD
import com.timekettle.upup.comm.R
import com.timekettle.upup.comm.base.BaseActivity
import com.timekettle.upup.comm.conference.ConController
import com.timekettle.upup.comm.conference.addCallStateBackListener
import com.timekettle.upup.comm.conference.removeCallStateBackListener
import com.timekettle.upup.comm.constant.TranslateMode
import com.timekettle.upup.comm.dialog.DialogFactory
import com.timekettle.upup.comm.model.DongleConnectEvent
import com.timekettle.upup.comm.model.SensorsCustomEvent
import com.timekettle.upup.comm.service.home.HomeServiceImplWrap
import com.timekettle.upup.comm.utils.IntentHelper
import com.timekettle.upup.comm.utils.SensorsUtil
import com.timekettle.upup.comm.utils.doTaskOnInterval
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.lang.ref.WeakReference
import java.text.DecimalFormat
import kotlin.math.roundToInt

/**
 * 音视频翻译设置
 *
 * @author: Pengwei Wang
 * @date: 2024/8/2
 */
@EventBusRegister
class VideoActivitySetting : BaseActivity<VideoActivitySettingBinding, VideoSettingViewModel>() {

    private lateinit var setting: ProductSetting//产品设置
    private lateinit var mModeUtil: ModeUtil
    override val mViewModel: VideoSettingViewModel by viewModels()
    private var callStatusListener: ((Int, Int, String, String, String, String) -> Unit)? = null

    private val bluetoothManager by lazy {
        applicationContext.getSystemService(Context.BLUETOOTH_SERVICE) as BluetoothManager
    }

    private val bluetoothAdapter by lazy {
        bluetoothManager.adapter
    }

    private var profileProxy: BluetoothProfile? = null

    private val profileListener = object : BluetoothProfile.ServiceListener {
        private val weakActivity = WeakReference(this@VideoActivitySetting)
        override fun onServiceConnected(profile: Int, proxy: BluetoothProfile) {
            if (profile == BluetoothProfile.A2DP) {
                weakActivity.get()?.handleA2dpConnected(proxy) // 开始监听设备状态
                weakActivity.get()?.closeProfile()
            }
        }

        override fun onServiceDisconnected(profile: Int) { }
    }

    private val a2dpEventReceiver = object : BroadcastReceiver() {
        private val weakActivity = WeakReference(this@VideoActivitySetting)
        override fun onReceive(context: Context, intent: Intent) {
            val action = intent.action
            if (action == BluetoothA2dp.ACTION_CONNECTION_STATE_CHANGED) {
                val state = intent.getIntExtra(BluetoothA2dp.EXTRA_STATE, BluetoothProfile.STATE_DISCONNECTED)
                if (state == BluetoothProfile.STATE_CONNECTED || state == BluetoothProfile.STATE_DISCONNECTED) {
                    logD("A2DP 状态变更")
                    weakActivity.get()?.connectProfile()
                }
            }
        }
    }

    private fun connectProfile() {
        bluetoothAdapter.getProfileProxy(
            applicationContext, // 使用 Application Context
            profileListener,
            BluetoothProfile.A2DP
        )
    }

    private fun handleA2dpConnected(proxy: BluetoothProfile) {
        profileProxy = proxy
        val devices = proxy.connectedDevices
        runOnUiThread { updateBtUI(devices) }
    }

    fun closeProfile() {
        profileProxy?.let { proxy ->
            bluetoothAdapter?.closeProfileProxy(BluetoothProfile.A2DP, proxy)
            profileProxy = null
        }
    }

    private val dongleDisconnectDialog by lazy {
        DialogFactory.createConfirmDialog(
            context = ActivityUtils.getTopActivity(),
            titleText = BaseApp.context.getString(R.string.common_alert_tip),
            content = BaseApp.context.getString(R.string.dongle_disconnect_content),
            canceledOnTouchOutside = false,
            cancelable = false,
            confirmCall = {
                ActivityUtils.finishToActivity(VideoActivityConnect::class.java, false, true)
            },
        )
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        setting = TransManager.getModelSetting(HomeServiceImplWrap.getUserModel())//设置赋值，先从缓存拿，没有直接new一个默认
        mModeUtil = IntentHelper.getObjectForKey(IntentKey.ModeUtil) as ModeUtil

        mViewModel.mModeUtil = mModeUtil
        mViewModel.offlineAvailable.value = mModeUtil.isSupportOffline()
        super.onCreate(savedInstanceState)
    }

    override fun initObserve() {
        observeLiveData(mViewModel.isShowOriginal, ::updateShowOriginal)
        observeLiveData(mViewModel.isShowOriginalOther, ::updateOriginalVoiceOther)
        observeLiveData(mViewModel.breakTimeItem, ::updateBreakTimeItem)
        observeLiveData(mViewModel.fontSizeItem, ::updateFontSizeItem)
        observeLiveData(mViewModel.ttsSpeed, ::updateTtsSpeedItem)
        observeLiveData(mViewModel.X1PlayContent, ::updateX1PlayContent)
    }

    override fun initRequestData() {
        //进入界面先根setting，更新ui,离线初始化在onCreate中
        mViewModel.isShowOriginal.value = setting.isShowOriginal
        mViewModel.isShowOriginalOther.value = setting.isShowOriginalOther
        mViewModel.breakTimeItem.value = setting.breakTime
        mViewModel.fontSizeItem.value = setting.fontSize
        mViewModel.ttsSpeed.value = setting.ttsSpeed
        mViewModel.X1PlayContent.value = setting.x1PlayContent
        mViewModel.minVadHeadsetEnergyItem.value =
            if (setting.minVadHeadsetEnergy == "-1")
                DecimalFormat("0.0#").format((100000.0 / mModeUtil.minVadEnergy).roundToInt())
            else setting.minVadHeadsetEnergy
        mViewModel.minVadHostEnergyItem.value =
            if (setting.minVadHostEnergy == "-1")
                DecimalFormat("0.0#").format((100000.0 / mModeUtil.minVadEnergy).roundToInt())
            else setting.minVadHostEnergy
    }

    private fun showDongleDialog() {
        if (!dongleDisconnectDialog.isShowing) {
            dongleDisconnectDialog.show()
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onDongleConnectEvent(event: DongleConnectEvent) {
        if (!event.isConnect) {
            showDongleDialog()
        }
    }

    override fun VideoActivitySettingBinding.initView() {
        vTitleBar.vTitleTv.text = getString(R.string.trans_setting_title)
        addWifiBatteryView(vTitleBar.vTitleLayout, false)
        SensorsUtil.trackEvent(
            SensorsCustomEvent.X1_TranslationSettingsEntrance.name, hashMapOf(
                "ModelType" to "音视频翻译"
            )
        )

        lifecycleScope.launch {
            doTaskOnInterval(1000L) { updateWifiStatus() }
        }
    }

    private suspend fun updateWifiStatus() {
        withContext(Dispatchers.Main) {
            mBinding.tvWifi.text = getWifiSSID()
        }
    }

    private fun getWifiSSID() =
        if (NetworkUtils.isWifiConnected()) NetworkUtils.getSSID() else getString(R.string.trans_wifi_not_connected)

    @SuppressLint("MissingPermission")
    private fun updateBtUI(devices: List<BluetoothDevice>) {
        logD("update BT device = $devices")
        if (devices.isNotEmpty()) {
            val deviceName = devices[0].name ?: devices[0].address
            mBinding.tvBt.text = deviceName
        } else {
            mBinding.tvBt.text = getString(R.string.trans_bluetooth_not_connected)
        }
    }

    //是否显示原文
    private fun updateShowOriginal(b: Boolean) {
        mBinding.ivOpenOriginal.isChecked = b
        if (b) {
//            mBinding.tvLeft.setTextColor(getColor(R.color.color_43a5ff))
            mBinding.tvOriginal.text = getString(R.string.trans_show_original)
        } else {
//            mBinding.tvLeft.setTextColor(getColor(R.color.white))
            mBinding.tvOriginal.text = getString(R.string.trans_hide_original)
        }

        SensorsUtil.trackEvent(
            SensorsCustomEvent.X1_ShowOriginalTextSettingOption.name, hashMapOf(
                "Switch_Setting_Value" to if (b) "开" else "关",
                "ModelType" to "音视频翻译"
            )
        )
    }

    //对方是否能听到原声
    private fun updateOriginalVoiceOther(b: Boolean) {
        mBinding.ivOpenOriginalOther.isChecked = b
    }

    override fun initListener() {
        //显示原文
        mBinding.ivOpenOriginal.setOnCheckedChangeListener { _, b ->
            mViewModel.updateOriginalShow(b)
        }

        //对方是否能听到原声
        mBinding.ivOpenOriginalOther.setOnCheckedChangeListener { _, b ->
            mViewModel.updateShowOriginalOther(b)
        }

        val breakPop = BreakTimeSettingPop(this@VideoActivitySetting, mViewModel, setting)

        // 获取屏幕宽度
//        val screenWidth = Resources.getSystem().displayMetrics.widthPixels
//        // 计算 PopupWindow 的位置
//        val contentView = breakPop.contentView // 获取 PopupWindow 的内容视图
//        val popupWidth = contentView.measuredWidth // 测量 PopupWindow 的宽度
//        val popupX = screenWidth - popupWidth

        //停顿时长
        mBinding.llBreakTime.setOnClickListener {
            //时长的点击，打开popWindow
//            breakPop.showAtLocation(mBinding.root, Gravity.NO_GRAVITY, popupX, 0)
            breakPop.showPopupWindow()
        }

        //翻译播放速度
        val speedPop = PlaySpeedSettingPop(this@VideoActivitySetting, mViewModel, setting)

        mBinding.llPlaySpeed.setOnClickListener {
//            //判断连接的耳机是否大于0
//            if (BleUtil.shared.connectedPeripherals.size == 0) {
//                //没有连接设备，弹框
//                EventBusUtils.postEvent(ShowDeviceDialogListEvent(HomeServiceImplWrap.getUserModel()))
//                return@setOnClickListener
//            }
            speedPop.showPopupWindow()
        }

        val fontPop = FontSizeSettingPop(this@VideoActivitySetting, mViewModel, setting)

        mBinding.llUpFontSize.setOnClickListener {
            //点击字体设置
            fontPop.showPopupWindow()
        }

        val playContentSettingPop = PlayContentSettingPop(this@VideoActivitySetting, mViewModel, setting)

        mBinding.llPlayContent.setOnClickListener {
            playContentSettingPop.showPopupWindow()
        }

        mBinding.llBt.setOnClickListener {
            startActivity(Intent(Settings.ACTION_BLUETOOTH_SETTINGS))
        }

        mBinding.llWifi.setOnClickListener {
            startActivity(Intent(Settings.ACTION_WIFI_SETTINGS))
        }

        callStatusListener = { state, _, _, _, _, _ ->
            runOnUiThread {
                if (state == 6) {
                    finish()
                }
            }
        }

        ConController.addCallStateBackListener(callStatusListener)

        connectProfile()

        registerReceiver(a2dpEventReceiver, IntentFilter().apply {
            addAction(BluetoothA2dp.ACTION_CONNECTION_STATE_CHANGED)
        })

    }

    //更新播放时长
    private fun updateBreakTimeItem(breakTime: SettingEnum.BreakTime) {
        val text: String
        when(breakTime.timeValue) {
            0.6f -> {
                text = getString(R.string.trans_short_more)
            }

            0.8f -> {
                text = getString(R.string.trans_short)
            }

            1.0f -> {
                text = getString(R.string.trans_standard)
            }

            2.0f -> {
                text = getString(R.string.trans_long)
            }

            3.0f -> {
                text = getString(R.string.trans_long_more)
            }

            else -> {
                text = getString(R.string.trans_standard)
            }
        }
        mBinding.tvBreakTime.text = text
    }

    //更新字体大小
    private fun updateFontSizeItem(fontSize: SettingEnum.FontSize) {
        when(fontSize.fontValue) {
            SettingEnum.FontSize.SmallSmall.fontValue -> {
                mBinding.tvSmall.setTextColor(getColor(R.color.color_43a5ff))
                mBinding.tvNormal.setTextColor(getColor(R.color.comm_btn_white))
                mBinding.tvBig.setTextColor(getColor(R.color.comm_btn_white))
            }

            SettingEnum.FontSize.Normal.fontValue -> {
                mBinding.tvSmall.setTextColor(getColor(R.color.comm_btn_white))
                mBinding.tvNormal.setTextColor(getColor(R.color.color_43a5ff))
                mBinding.tvBig.setTextColor(getColor(R.color.comm_btn_white))
            }

            SettingEnum.FontSize.BigBig.fontValue -> {
                mBinding.tvSmall.setTextColor(getColor(R.color.comm_btn_white))
                mBinding.tvNormal.setTextColor(getColor(R.color.comm_btn_white))
                mBinding.tvBig.setTextColor(getColor(R.color.color_43a5ff))
            }

            else -> {

            }
        }
    }

    //显示tts播放速度
    private fun updateTtsSpeedItem(ttsSpeed: SettingEnum.TtsSpeed) {
        mBinding.tvPlaySpeed.text = ttsSpeed.showText
    }

    private fun updateX1PlayContent(playContent: SettingEnum.X1PlayContent) {
        val text = when(playContent.type) {
            1 -> {
                getString(R.string.dongle_setting_play_translation)
            }

            2 -> {
                getString(R.string.dongle_setting_play_origin_and_translation)
            }

            else -> {
                getString(R.string.dongle_setting_play_origin)
            }
        }
        mBinding.tvPlayContent.text = text
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        if (keyCode == 24 || keyCode == 25) {
            return super.onKeyDown(keyCode, event)
        }

        return true
    }

    override fun onKeyUp(keyCode: Int, event: KeyEvent?): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            onBackPressed()
        }

        return true
    }

    override fun onDestroy() {
        super.onDestroy()
        ConController.removeCallStateBackListener(callStatusListener)

        closeProfile()
        unregisterReceiver(a2dpEventReceiver)
    }

}