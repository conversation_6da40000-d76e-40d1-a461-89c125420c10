package co.timekettle.module_translate.ui.repo

import com.timekettle.upup.base.mvvm.m.BaseRepository
import com.timekettle.upup.base.utils.SpUtils
import com.timekettle.upup.base.utils.logD
import com.timekettle.upup.comm.bean.CallRecordEntity
import com.timekettle.upup.comm.constant.SpKey
import com.timekettle.upup.comm.dao.CallRecordDao
import com.timekettle.upup.comm.dao.MsgBeanDao
import javax.inject.Inject

class PhoneMsgRepository @Inject constructor(
    private val callRecordDao: CallRecordDao,
    private val msgBeanDao: MsgBeanDao
): BaseRepository(){

    suspend fun insert(callRecordEntity: CallRecordEntity){
        if (!SpUtils.getBoolean(SpKey.IS_SAVE_RECORD, true)) {
            return
        }
        callRecordDao.insert(callRecordEntity)
    }

    suspend fun update(callRecordEntity: CallRecordEntity) {
        if (!SpUtils.getBoolean(SpKey.IS_SAVE_RECORD, true)) {
            return
        }
        callRecordDao.update(callRecordEntity)
    }

    suspend fun insertAll(list: List<CallRecordEntity>) {
        callRecordDao.insert(list)
    }

    suspend fun getCallRecordByPhoneNumber(phoneNumber: String): List<CallRecordEntity> {
        return callRecordDao.getCallRecordByPhoneNumber(phoneNumber)
    }

    suspend fun getAllHistory(): List<CallRecordEntity> {
        return callRecordDao.getAllCallRecord()
    }

    suspend fun deleteHistoryList(historyEntities: List<CallRecordEntity>) {
        callRecordDao.delete(historyEntities)
        msgBeanDao.deleteMsgDetailByPhoneDateList(historyEntities.map { it.date })
    }

    suspend fun getLastCallRecord(): CallRecordEntity {
        return callRecordDao.getLastCallRecord()
    }

}