package co.timekettle.module_translate.ui.activity

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import androidx.activity.OnBackPressedCallback
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.annotation.RequiresApi
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import co.timekettle.btkit.BleCmdContant
import co.timekettle.btkit.BleUtil
import co.timekettle.btkit.BytesTrans
import co.timekettle.btkit.bean.RawBlePeripheral
import co.timekettle.btkit.bean.RawBlePeripheral.Role
import co.timekettle.module_translate.bean.MeetEntity
import co.timekettle.module_translate.bean.MeetingMsgBean
import co.timekettle.module_translate.bean.MemberEntity
import co.timekettle.module_translate.bean.SettingEnum
import co.timekettle.module_translate.constant.Constant.BleRecordEventName
import co.timekettle.module_translate.constant.IntentKey
import co.timekettle.module_translate.databinding.MeetingActivityMainBinding
import co.timekettle.module_translate.tools.TransLanguageTool
import co.timekettle.module_translate.tools.TransManager
import co.timekettle.module_translate.ui.adapter.HeadMemberAdapterMeeting
import co.timekettle.module_translate.ui.adapter.MsgAdapterMeeting
import co.timekettle.module_translate.ui.ktx.startTransSettingActivity
import co.timekettle.module_translate.ui.pop.MeetingMorePop
import co.timekettle.module_translate.ui.util.RecycleViewUtil
import co.timekettle.module_translate.ui.util.ResUtil.getDrawableId
import co.timekettle.module_translate.ui.util.TransUiUtil.showNowRecordToast
import co.timekettle.module_translate.ui.vm.MeetingMainViewModel
import co.timekettle.module_translate.ui.widget.LottiePlayAudioView
import co.timekettle.opus.OpusCodec
import co.timekettle.sip.call.CallManager
import co.timekettle.sip.call.sendFrame
import co.timekettle.sip.call.sendSip
import co.timekettle.sip.call.stopAudio
import co.timekettle.sip.entity.ReqEntity
import co.timekettle.speech.AgcProcessor
import co.timekettle.speech.AiSpeechManager
import co.timekettle.speech.AudioChannel
import co.timekettle.speech.ISpeechConstant
import co.timekettle.speech.SpeakManager
import co.timekettle.speech.TestRecorder
import co.timekettle.speech.utils.RingBuffer
import com.alibaba.android.arouter.facade.annotation.Route
import com.blankj.utilcode.util.NetworkUtils
import com.timekettle.upup.base.ktx.animateAlpha0
import com.timekettle.upup.base.ktx.animateAlpha1
import com.timekettle.upup.base.ktx.isInvisible
import com.timekettle.upup.base.ktx.observeLiveData
import com.timekettle.upup.base.ktx.openActivity1
import com.timekettle.upup.base.ktx.startKtxActivity
import com.timekettle.upup.base.ktx.visible
import com.timekettle.upup.base.utils.EventBusRegister
import com.timekettle.upup.base.utils.EventBusUtils
import com.timekettle.upup.base.utils.SpUtils
import com.timekettle.upup.base.utils.countDownCoroutines
import com.timekettle.upup.base.utils.logD
import com.timekettle.upup.base.utils.showToast
import com.timekettle.upup.comm.BuildConfig
import com.timekettle.upup.comm.R
import com.timekettle.upup.comm.base.BaseActivity
import com.timekettle.upup.comm.conference.ConController
import com.timekettle.upup.comm.constant.Constant
import com.timekettle.upup.comm.constant.RouteUrl
import com.timekettle.upup.comm.constant.SpKey
import com.timekettle.upup.comm.constant.TranslateMode
import com.timekettle.upup.comm.dialog.DialogFactory
import com.timekettle.upup.comm.ktx.setLedLevel
import com.timekettle.upup.comm.ktx.setTextSizeByLanguage
import com.timekettle.upup.comm.model.AccountBean
import com.timekettle.upup.comm.model.CLoseSendEvent
import com.timekettle.upup.comm.model.MeetingLanguageUpdateEvent
import com.timekettle.upup.comm.model.MeetingUserNameUpdateEvent
import com.timekettle.upup.comm.model.SensorsCustomEvent
import com.timekettle.upup.comm.model.ShowDeviceDialogListEvent
import com.timekettle.upup.comm.receiver.BatteryReceiver
import com.timekettle.upup.comm.service.home.HomeServiceImplWrap
import com.timekettle.upup.comm.service.trans.TransServiceImplWrap
import com.timekettle.upup.comm.tools.DeviceManager
import com.timekettle.upup.comm.tools.DeviceTool
import com.timekettle.upup.comm.utils.IntentHelper
import com.timekettle.upup.comm.utils.SensorsUtil
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 * 三个地方会进入这个界面
 * */
@EventBusRegister
@AndroidEntryPoint
@Route(path = RouteUrl.Translate.MeetingActivityMain)
class MeetingActivityMain : BaseActivity<MeetingActivityMainBinding, MeetingMainViewModel>(), LifecycleOwner {

    private var mLayoutManager: LinearLayoutManager? = null
    private var msgList = mutableListOf<MeetingMsgBean>()
    private var headMemberList = mutableListOf<MemberEntity>()
    private var meetEntity: MeetEntity? = null
    private lateinit var meetingID: String
    private lateinit var mMsgMeetingAdapter: MsgAdapterMeeting
    private lateinit var mAdapter: HeadMemberAdapterMeeting
    var jobNeedScrollToEnd: Job? = null  //  是否需要滚动到底部
    private lateinit var morePop: MeetingMorePop
    var sipAccount: AccountBean? = null
    var jobNetworkReConnect: Job? = null  //开启网络连接倒计时
    private lateinit var opusCodec: OpusCodec
    var shouldAssignLang = true

    private var channel: AudioChannel? = null

    private var bleListener: BleUtil.Listener? = null
    private var meetingType = "0"
    private var jobPure: Job? = null
    private var isClicked = false //播放按钮是否被点击
    private var x = 0f
    private var y = 0f
    private var currentMode: Int = -1
    private var hideJob: Job? = null  // 检测是否服务异常的心跳

    private var agc: AgcProcessor? = null
    private var fileHandle: TestRecorder? = null
    private val channelReadCache = RingBuffer(20)
//    private val channelSendCache = RingBuffer(20)

//    private var hasNewMsgIn: Boolean = true // 是否有新的消息进来了
//    private var remainSecond: Int = 0   // 滑动还剩下的需要等待的时间

    override val mViewModel: MeetingMainViewModel by viewModels()

    val resultLauncher =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            if (result.resultCode == RESULT_OK) {
                val code = result.data?.getStringExtra("lang_code")
                //会议模式选择的语言，修改成没成功，需要后端控制，在会议详情里面
                ConController.setLanguage(code.toString())
                //切换语言后，查询会议成员信息
                ConController.getConferenceInfo()
            }
        }

    private val mBatteryListener = { action: String? ->
        if (action == Intent.ACTION_POWER_CONNECTED && BatteryReceiver.isBaseCharging()) {
            window.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        } else if (action == Intent.ACTION_POWER_DISCONNECTED) {
            window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        opusCodec = OpusCodec(16000, 1)

        //保存当前使用的模式
        HomeServiceImplWrap.saveUserModel(TranslateMode.MEETING)
        HomeServiceImplWrap.setPhoneMode(true)
        HomeServiceImplWrap.setInMode(true)
        Constant.isMeeting = true
//        TransServiceImplWrap.setInMeeting(true)

//        mViewModel.setProductAndMode()

        mViewModel.addCallStatusListener()
//        mViewModel.listenRegStatus()
        NetworkUtils.registerNetworkStatusChangedListener(netWorkListener)

        BatteryReceiver.addChangeListener(mBatteryListener)
    }

    override fun initObserve() {
        observeLiveData(mViewModel.liveMsgList, ::processMsgList)
        observeLiveData(mViewModel.isKicked, ::memberIsKicked)
        observeLiveData(mViewModel.meetingIsFinish, ::meetingIsFinish)
        observeLiveData(mViewModel.assignedLang, ::showAssignedDialog)
        observeLiveData(mViewModel.meetDetail, ::meetDetailUpdate)
        observeLiveData(mViewModel.errorMsg, ::callStatusError)
        observeLiveData(mViewModel.liveFontEnum, ::updateFontSize)
        observeLiveData(mViewModel.isShowOriginal, ::processShowOriginal)
        observeLiveData(mViewModel.hangupLiveData, ::processHangup)
    }

    private fun processHangup(data: Int) {
        DialogFactory.createKnowDialog(this@MeetingActivityMain,
            content = getString(R.string.trans_meeting_net_error),
            knowCall = {
                if (meetingType != "1") {
                    startKtxActivity<MeetingActivityHome>()
                } else {
                    mViewModel.hangUp()
                    finish()
                }
            }).show()
    }

    private fun processShowOriginal(isShowOriginal: Boolean) {
        mMsgMeetingAdapter.setShowOriginal(isShowOriginal)
    }

    private fun updateFontSize(fontSize: SettingEnum.FontSize) {
        val fontSp = fontSize.fontValue
        mMsgMeetingAdapter.setFont(fontSp)
    }

    private fun showAssignedDialog(b: Boolean) {
        if (b && shouldAssignLang) {
            shouldAssignLang = false
            DialogFactory.createTipsDialog(this,
                sureText = getString(R.string.trans_meeting_switch_language),
                content = getString(R.string.trans_meeting_exceed_language),
                cancelable = true,
                canceledOnTouchOutside = true,
                onConfirmListener = {
                    val param = IntentKey.SipAccount to sipAccount
                    openActivity1<ChooseLangActivity>(param)
//                    resultLauncher.launch(Intent(this@MeetingActivityMain, MeetingChooseLangActivity::class.java).apply { putExtra("mode_type", 2) })
//                    this.openActivity1<MeetingChooseLangActivity>("mode_type" to 2)
                }).show()
        }
    }

    /**
     * 心跳检测通话过程异常，两个地方回调这里，一个是异常(字符串不为空)，一个是异常恢复（字符串为空）
     */
    private fun callStatusError(errorText: String) {
        if (errorText.isNotEmpty()) {
            showBottomBar()
            //底部状态栏显示后要让列表滑动到最底部，否则会造成挡住的视觉差
//            startScrollJob(0) { RecycleViewUtil.scrollToBottom(mBinding.vRecycleView) }
            mBinding.tvBottomTip.text = errorText
            mBinding.tvBottomTip.background = ContextCompat.getDrawable(
                this@MeetingActivityMain,
                co.timekettle.module_translate.R.drawable.bg_meeting_net_error
            )
        } else {
            //说明通话恢复了
            hideBottomBar()
        }
    }

    /**
     * 会议详情更新了
     */
    private fun meetDetailUpdate(meetEntity: MeetEntity?) {
//        CallManager.stopAudio()
        if (meetEntity?.mode == 0) {
            //演讲模式，只有群主开启rtp
//            var isAdmin =
//                meetEntity?.admin == mViewModel.mSipAccount.sipName/* CallManager.stopAudio()*/
//            if (isAdmin) {
//                //演讲状态只有群主才能发送rtp流
//                CallManager.startAudio()
//            } else {
//                //其他人关闭
//                CallManager.stopAudio()
//            }
            //演讲模式，全员禁言中
//            if (mBinding.tvBottomTip.isVisible){
//                //正在显示
//            }

//            showBottomBar()

            //底部状态栏显示后要让列表滑动到最底部，否则会造成挡住的视觉差
//            startScrollJob(0) { RecycleViewUtil.scrollToBottom(mBinding.vRecycleView) }

//            mBinding.tvBottomTip.text = "全员禁言中"
//            mBinding.tvBottomTip.background = ContextCompat.getDrawable(this@MeetingActivityMain, co.timekettle.module_translate.R.drawable.bg_bottom_tip)

        } else {
            //会议状态，rtp全部开启
//            CallManager.startAudio()

            hideBottomBar()
        }

        updateMember(meetEntity!!)

    }

    private fun updateMember(meetEntity: MeetEntity) {
        logD("会议人员详情：${meetEntity.member}", TAG)

        this.meetEntity = meetEntity

        var list = meetEntity.member!!
        if (list.size > 3) {
            list = list.subList(0, 3)
        }

        for (i in list) {
            i.drawableId = getDrawableId(i.id)
        }

        headMemberList.clear()
        headMemberList.addAll(list)
        mAdapter.setAdminAndModel(meetEntity.admin, meetEntity.mode == 0)

        //根据模式更新按钮
        if (meetEntity.mode == 0) {
            //演讲=禁言
            mBinding.tvState.text = getString(R.string.translate_meeting_speech_forbiden)
            mBinding.imgForbid.setImageResource(co.timekettle.module_translate.R.mipmap.metting_group_icon_forbiden)
        } else {
            mBinding.tvState.text = getString(R.string.translate_meeting_speech_allow)
            mBinding.imgForbid.setImageResource(co.timekettle.module_translate.R.mipmap.metting_group_icon_allow)
        }

        //群主
        if (meetEntity.admin == mViewModel.mSipAccount.sipName) {
            if (currentMode != meetEntity.mode) {
                currentMode = meetEntity.mode

                if (currentMode == 0) {
                    showToast(getString(R.string.trans_meeting_switched_to_speech_mode))
                } else {
                    showToast(getString(R.string.trans_meeting_switched_to_discussion_mode))
                }
            }

            setLedLevel(this, 8)
            Log.d("asdffasdf3riwergfwer","111")

        } else {
            //不是群主
            if (currentMode != meetEntity.mode) {
                currentMode = meetEntity.mode

                if (currentMode == 0) {
                    showToast(getString(R.string.trans_meeting_switched_to_speech_mode_for_admin))
                    setLedLevel(this, 0)
                    Log.d("asdffasdf3riwergfwer","222")
                } else {
                    showToast(getString(R.string.trans_meeting_switched_to_discussion_mode))
                    setLedLevel(this, 8)
                    Log.d("asdffasdf3riwergfwer","333")
                }
            }

        }
    }

    //会议已结束
    private fun meetingIsFinish(b: Boolean) {
        if (b) {
            setLedLevel(this@MeetingActivityMain, 0)
            Log.d("asdffasdf3riwergfwer","444")
            CallManager.stopAudio() //停在当前页，不过暂停音频流，不能说话

            //已结束无状态恢复
            mBinding.llUtils.isVisible = false
            mBinding.tvBottomTip.isVisible = true
            mBinding.tvBottomTip.text = getString(R.string.translate_meeting_dissolved)
            mBinding.tvBottomTip.background = ContextCompat.getDrawable(
                this@MeetingActivityMain, co.timekettle.module_translate.R.drawable.bg_bottom_tip
            )

            if (morePop.isShowing) {
                mViewModel.isNeedShow = false
                morePop.dismiss()
            }

            DialogFactory.createKnowDialog(this@MeetingActivityMain,
                content = getString(R.string.common_dissolve_this_meeting),
                knowCall = {
                    if (meetingType != "1") {
                        startKtxActivity<MeetingActivityHome>()
                    } else {
                        mViewModel.hangUp()
                        finish()
                    }
                }).show()
        }
    }

    //被踢出
    private fun memberIsKicked(b: Boolean?) {
        if (b == true) {
            DialogFactory.createTipsDialog(this@MeetingActivityMain,
                content = getString(R.string.translate_out_of_the_meeting),
                cancelable = false,
                canceledOnTouchOutside = false,
                onConfirmListener = {
                    mViewModel.isNeedShow = false
                    startKtxActivity<MeetingActivityHome>()//回到home
                    finish()
                }).show()
        }
    }

    private fun updateMsgUser(meetEntity: MeetEntity?) {
        val memberEntity = meetEntity?.member?.find { it.user == sipAccount?.sipName }
        mMsgMeetingAdapter.setCurrentMeet(memberEntity)
    }

    private fun processMsgList(meetingMsgList: MutableList<MeetingMsgBean>) {
        runOnUiThread {
            val oldSize = msgList.size
            msgList.clear()
            msgList.addAll(meetingMsgList)
            mMsgMeetingAdapter.setList(msgList)

            if (oldSize <= msgList.size) { //代表的是消息插入、消息更新
//                hasNewMsgIn = true // 代表有新消息了

                if (mViewModel.isLoadHistory) {
                    mViewModel.isLoadHistory = false
                    //加载的是历史数据，直接滚动到底部
                    RecycleViewUtil.scrollToBottom(mBinding.vRecycleView)
                }

                if (RecycleViewUtil.isRecyclerViewScrolling(mBinding.vRecycleView)) {
                    return@runOnUiThread
                }

//                if (remainSecond == 0) {
                    RecycleViewUtil.scrollToBottom(mBinding.vRecycleView)
//                }
            }
        }
    }

//    //开启滑动到底部的倒计时
//    private fun startScrollJob(totalSecond: Int = 5, todo: () -> Unit?) {
////        if(jobNeedScrollToEnd!=null) return
//        jobNeedScrollToEnd = countDownCoroutines(totalSecond, lifecycleScope, onTick = { second ->
//            if (second == 0) {
//                todo()
//                jobNeedScrollToEnd = null
//            }
//        })
//    }

    override fun initRequestData() {
        SensorsUtil.trackEvent(
            SensorsCustomEvent.X1_EnterXXMode.name, hashMapOf(
                "ModelType" to "多人会议"
            )
        )
    }

    private val netWorkListener = object : NetworkUtils.OnNetworkStatusChangedListener {
        override fun onDisconnected() {
            logD("网络已断开", TAG)

            showToast(getString(R.string.common_network_error_check_it))

//            showBottomBar()
            //底部状态栏显示后要让列表滑动到最底部，否则会造成挡住的视觉差
//            RecycleViewUtil.scrollToBottom(mBinding.vRecycleView)

//            mBinding.tvBottomTip.text = getString(R.string.common_network_error_check_it)
//            mBinding.tvBottomTip.background = ContextCompat.getDrawable(
//                this@MeetingActivityMain,
//                co.timekettle.module_translate.R.drawable.bg_meeting_net_error
//            )

            //实现计时器15秒倒计时，如果15s内，网络恢复不用管，超过14弹窗，确认重新入会
            startNetworkConnectJob()
        }

        override fun onConnected(networkType: NetworkUtils.NetworkType?) {
            logD("网络已连接", TAG)

            ConController.getConferenceInfo()

//            hideBottomBar()

            //14秒内如果连上了网络，取消正在进行的定时任务
//            if(jobNetworkReConnect?.isActive == true){
//                jobNetworkReConnect?.cancel()
//                jobNetworkReConnect=null
//            }
        }
    }

    //网络异常定时任务
    private fun startNetworkConnectJob() {
        jobNetworkReConnect = countDownCoroutines(14, lifecycleScope, onTick = { second ->
            if (NetworkUtils.isConnected()) {
                //连上了
                jobNetworkReConnect?.cancel()
                jobNetworkReConnect = null
                return@countDownCoroutines
            }
            if (second == 0) {
                //弹框提示网络异常，是否需要挂断？
                DialogFactory.createTipsDialog(this@MeetingActivityMain,
                    content = getString(R.string.translate_meeting_rejoin),
                    cancelable = false,
                    canceledOnTouchOutside = false,
                    onConfirmListener = {
                        mViewModel.hangUp()//挂断
                        startKtxActivity<MeetingActivityHome>()//回到home
                        finish()
                    }).show()
                jobNetworkReConnect = null
            }
        })

    }

    private fun initBleListener() {
        bleListener = object : BleUtil.Listener {
            override fun dispatchEvent(type: BleUtil.BleEventName, perip: RawBlePeripheral?) {
                when (type) {

                    BleUtil.BleEventName.BleConnectStandby -> {
                        logD("模式内连接耳机", TAG)

                        //因为homeactivity筛选本机的耳机进行连接，这里只是监听重新连接然后发送命令
//                        resetBleCmds()

                        BleUtil.shared.sendCmdToQueue(perip, BleCmdContant.AppCmdId.EnableButton)

                        DeviceTool.connectDevice(this::class.java.name,perip) {
                            lifecycleScope.launch {
//                                mViewModel.headsetIsDisConnected = false
//                                CallManager.startAudio()
                                delay(100)
                                val blePeripherals = mutableListOf(perip)
                                BleUtil.shared.sendBleStopCmds(blePeripherals.toTypedArray(), true)
                                delay(500)
                                BleUtil.shared.sendBleStartCmds(blePeripherals.toTypedArray(), true)
                            }
                        }
                    }

                    BleUtil.BleEventName.BleDisconnectedPeripheral -> {
                        if (BleUtil.shared.connectedPeripherals.size == 0) {
                            EventBusUtils.postEvent(ShowDeviceDialogListEvent(TranslateMode.PHONE))
//                            mViewModel.headsetIsDisConnected = true
//                            CallManager.stopAudio()
                        }

                        DeviceTool.disconnectDevice(this::class.java.name) {
                            val blePeripherals = BleUtil.shared.connectedPeripherals
                            BleUtil.shared.sendBleStartCmds(blePeripherals.toTypedArray(), true)
                        }
                    }

                    else -> {}
                }
            }

            override fun onBluetoothStateUpdate(state: Int) {}
        }

        BleUtil.shared.addListener(bleListener)
    }

    private fun resetBleCmds() {
        lifecycleScope.launch {
            val blePeripherals = BleUtil.shared.connectedPeripherals
            logD("重新发送BLE设备指令设备数$blePeripherals", TAG)
            BleUtil.shared.sendBleStopCmds(blePeripherals.toTypedArray(), true)
            delay(1000)
//                BleUtil.shared.sendBleStartCmds(blePeripherals.toTypedArray(), true)
            mViewModel.startMode2()
        }
    }


    private fun processBack() {
        DialogFactory.createConfirmCancelDialog(this@MeetingActivityMain,
            content = getString(R.string.translate_exit_cur_mode_tip),
            confirmCall = {
                ConController.leaveConference()//主动退出会议
                mViewModel.hangUp()//挂断

                if (meetingType != "1") {
                    startKtxActivity<MeetingActivityHome>()//回到home
                } else {
                    finish()
                }
            },
            cancelCall = {

            },
            closeCall = {

            }).show()
    }

    private fun getEmptySpeakView(): View {
        return LayoutInflater.from(this)
            .inflate(co.timekettle.module_translate.R.layout.layout_footer_meet, null)
    }

    @RequiresApi(Build.VERSION_CODES.O)
    override fun MeetingActivityMainBinding.initView() {
        EventBusUtils.postEvent(CLoseSendEvent(1))

        sipAccount = IntentHelper.getObjectForKey(IntentKey.SipAccount) as AccountBean
        meetingType = intent.getStringExtra(IntentKey.MeetingType).toString()
        meetingID = intent.getStringExtra(IntentKey.MeetingID).toString()
        val password = intent.getStringExtra(IntentKey.MeetingPassword).toString()
        mViewModel.setSipAccountAndId(sipAccount!!, meetingID, password)

        TransServiceImplWrap.setMeetingId(meetingID)
        TransServiceImplWrap.setPassword(password)

        if (BuildConfig.DEBUG) {
            tvAccountID.visibility = View.VISIBLE
            tvAccountID.text = sipAccount?.sipName
        }

        agc = AgcProcessor(ISpeechConstant.DefaultSampleRate, 320, 3.0f)
        if (SpUtils.getBoolean(SpKey.IS_RECORD_AUDIO_OPEN, false)) {
            fileHandle = TestRecorder(this@MeetingActivityMain, "TK_Record", null,  "sip-raw-record", true) // 此处设置录音文件开关
        }

        vTitleTv.setTextSizeByLanguage(10f,"fr", "th", "ru", "de")
        tvMeetingID.setTextSizeByLanguage(10f,"fr", "th", "ru", "de")
        tvMeetingID.text = meetingID

        mViewModel.queryHistoryById()

        mMsgMeetingAdapter = MsgAdapterMeeting(msgList)
        mMsgMeetingAdapter.setFooterView(getEmptySpeakView())
        mLayoutManager = LinearLayoutManager(this@MeetingActivityMain)
        vRecycleView.adapter = mMsgMeetingAdapter
        vRecycleView.layoutManager = mLayoutManager

        mAdapter = HeadMemberAdapterMeeting(headMemberList)
        mLayoutManager =
            LinearLayoutManager(this@MeetingActivityMain, LinearLayoutManager.HORIZONTAL, false)
        rvHeadMember.adapter = mAdapter
        rvHeadMember.layoutManager = mLayoutManager

        //todo 先去根据系统默认语言，后续增加入群以后根据语言是否大于五个来进行更改
        mViewModel.updateMeetingLanguage()

//        if (BleUtil.shared.connectedPeripherals.size > 0) {
//            logD("当前连接的耳机数: ${BleUtil.shared.connectedPeripherals.size}，耳机已连接，开启rtp流", "MeetingActivityMain")
//            mViewModel.headsetIsDisConnected = false
//            CallManager.startAudio()
//        }

        //这里应该将vm传入进去更新列表状态
        morePop = MeetingMorePop(
            this@MeetingActivityMain, this@MeetingActivityMain, mViewModel, meetingType
        )

        //监听sip指令
        mViewModel.listenMessage()

        //主动查询成员列表
        ConController.getConferenceInfo()

        //进来先打开
        lifecycleScope.launch(Dispatchers.Main) {
            delay(500)
            showPop()
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun initListener() {

        initBleListener()

        mBinding.vBackIv.setOnClickListener {
            isClicked = true
            processBack()
        }

        mBinding.imgHead.setOnClickListener {
            isClicked = true
            showPop()
        }

        mBinding.imgHead2.setOnClickListener {
            isClicked = true
            showPop()
        }

        mBinding.vTitleSettingIcon.setOnClickListener {
            isClicked = true
            startTransSettingActivity(mViewModel.mTranslateMode, mViewModel.modeUtil)
        }

        mBinding.vTitleSettingIcon2.setOnClickListener {
            isClicked = true
            startTransSettingActivity(mViewModel.mTranslateMode, mViewModel.modeUtil)
        }

        mBinding.vTopDevice.setOnClickListener {
            isClicked = true
        }

        mBinding.vTitleTv.setOnClickListener {
            isClicked = true
        }

        mBinding.imgMore.setOnClickListener {
            isClicked = true
            IntentHelper.addObjectForKey(meetEntity, IntentKey.MeetingMemberList)
            val param1 = IntentKey.MeetingID to mViewModel.meetingId
            val param2 = IntentKey.MeetingPassword to mViewModel.password
            startKtxActivity<MeetingActivityMember>(param1, param2)
        }

        mBinding.rvHeadMember.setOnTouchListener { _, motionEvent ->
            if (motionEvent.action == MotionEvent.ACTION_DOWN) {
                isClicked = true
                IntentHelper.addObjectForKey(meetEntity, IntentKey.MeetingMemberList)
                val param1 = IntentKey.MeetingID to mViewModel.meetingId
                val param2 = IntentKey.MeetingPassword to mViewModel.password
                startKtxActivity<MeetingActivityMember>(param1, param2)
            }

            true
        }

        onBackPressedDispatcher.addCallback(
            this@MeetingActivityMain,
            object : OnBackPressedCallback(true) {
                override fun handleOnBackPressed() {
                    processBack()
                }
            })

        mMsgMeetingAdapter.apply {

            addChildClickViewIds(co.timekettle.module_translate.R.id.v_play_btn)

            setOnItemChildClickListener { adapter, view, position ->

                //判断连接的耳机是否大于0
                if (BleUtil.shared.connectedPeripherals.size == 0) {
                    //没有连接设备，弹框
                    EventBusUtils.postEvent(ShowDeviceDialogListEvent(TranslateMode.MEETING))
                    return@setOnItemChildClickListener
                }

                isClicked = true

                if (view is LottiePlayAudioView) {
                    val msgBean = (adapter as MsgAdapterMeeting).data[position]
                    if (view.playState == LottiePlayAudioView.PlayState.Playing) {
                        mViewModel.stopPlayText(msgBean)
                        mViewModel.updatePlayMsgBean(msgBean.apply { isPlaying = false })
                        mMsgMeetingAdapter.notifyDataSetChanged()
                    } else {
                        //没有正在播放
                        SpeakManager.shareInstance().stop();//先停止所有播放
                        val channels = AiSpeechManager.shareInstance().audioChannels
                        if (channels.isEmpty()) {
                            //没有通道不播放
                            return@setOnItemChildClickListener
                        }
                        mViewModel.playText(msgBean, startCallBack = {
                            if (msgBean.session == it) {
                                mViewModel.updatePlayMsgBean(msgBean.apply { isPlaying = true })
                                mMsgMeetingAdapter.notifyDataSetChanged()
                            }
                        }, finishCallback = {
                            lifecycleScope.launch(Dispatchers.Main) {
                                if (msgBean.session == it) {
                                    mViewModel.updatePlayMsgBean(msgBean.apply {
                                        isPlaying = false
                                    })
                                    mMsgMeetingAdapter.notifyDataSetChanged()
                                }
                            }
                        })
                    }
                }
            }
        }

        channel = AudioChannel(this, "meet", SpUtils.getBoolean(SpKey.IS_RECORD_AUDIO_OPEN, false))
        channel?.minVadEnergy = AudioChannel.defalutMinVadEnergy / 10
        channel?.setMarkVadDataTag(true)
        channel?.listener = object : AudioChannel.Listener {

            override fun onVadBegin(channel: AudioChannel?, session: Long) {
                if (meetEntity?.admin == mViewModel.mSipAccount.sipName || meetEntity?.mode == 1) {
                    runOnUiThread {
                        CallManager.sendSip(ReqEntity(1, "1"))
                        Log.e("ttttttttttt", "onVadBegin: " + channel?.key)
                    }
                }
            }

            override fun onVadEnd(channel: AudioChannel?, session: Long) {
                if (meetEntity?.admin == mViewModel.mSipAccount.sipName || meetEntity?.mode == 1) {
                    runOnUiThread {
                        CallManager.sendSip(ReqEntity(1, "0"))
                        Log.e("ttttttttttt", "onVadEnd: " + channel?.key)
                    }
                }
            }

            override fun onActivity(channel: AudioChannel?, session: Long, data: ShortArray?, volume: Float) {
//                    Log.e("ttttttttttt", "size: " + data?.size)

//                    channelSendCache.write(BytesTrans.getInstance().Shorts2Bytes(data))
//                    while (channelSendCache.readable() >= 640) {
//                        val packet = ByteArray(640)
//                        channelSendCache.read(packet)
//                        sendFrame(packet)
//                    }
            }
        }

    }

    private fun showPop() {
        if (!NetworkUtils.isConnected()) {
            showToast(getString(R.string.common_network_disconnected))
        }

        if (!morePop.isShowing) {
            morePop.showPopupWindow()
            mViewModel.openPopTimes++
        }
    }

    private fun hidePop() {
        if (morePop.isShowing) {
            morePop.dismiss()
        }
    }

    //注册选择语言事件
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMeetingLanguageUpdate(event: MeetingLanguageUpdateEvent) {
        //保存选择完的语言
        DeviceManager.saveLastlyMeetingUseLanguage(event.code)
        //发送了语言选择事件，先缓存，下次进界面先获取缓存
        mViewModel.updateMeetingLanguage(event.code)

    }

    //修改昵称事件
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMeetingNameUpdate(event: MeetingUserNameUpdateEvent) {
        logD("onMeetingNameUpdate: ${event.name}", TAG)
        //todo 将当前修改后的名字同步到后台，更改成功后会有回调，然后保存缓存，下一次注册时获取缓存的昵称
        ConController.editNickName(event.name)
        //保存sp -----不保存，每次进入会议默认都是user
//        saveMeetingNickname(event.name)
    }

    override fun onResume() {
        super.onResume()

        mViewModel.enterMode(this@MeetingActivityMain)
        mViewModel.startMode()

        val setting = TransManager.getModelSetting(HomeServiceImplWrap.getUserModel())
        mViewModel.liveFontEnum.value = setting.fontSize
        mViewModel.isShowOriginal.value = setting.isShowOriginal
//        mViewModel.modeUtil.setBreakTime((getBreakTimeValue(setting.breakTime) * 1000).toInt())
        channel?.setVadEnd((getBreakTimeValue(setting.breakTime) * 1000).toInt() / 16)
        AiSpeechManager.shareInstance().audioChannels.forEach {
            SpeakManager.shareInstance().update(it.speakerType, setting.ttsSpeed.value)
        }
        mViewModel.modeUtil.setVoice(setting.ttsVoiceIsManSelf, setting.ttsVoiceIsManOther)

        startPureJob()

        BleUtil.shared.setTouchEventCallback { peripheral, _ ->
            // 触控点击的回调
            val blePeripherals = BleUtil.shared.connectedPeripherals
            DeviceTool.asWSeries(peripheral)?.let {
                showNowRecordToast(it.role == Role.Left)
            }
            if (blePeripherals.size == 2) {
                DeviceTool.asWSeries(peripheral)?.let {
                    if (it.macSuffix4 != HomeServiceImplWrap.getFirstDeviceMac()) {
                        HomeServiceImplWrap.saveFirstDeviceMac(it.macSuffix4)
                        lifecycleScope.launch {
                            BleUtil.shared.sendBleStopCmds(blePeripherals.toTypedArray(), true)
                            delay(500)
                            BleUtil.shared.sendBleStartCmds(arrayOf(peripheral), true)
                        }
                    }
                }

//                lifecycleScope.launch {
//                    BleUtil.shared.sendBleStartCmds(arrayOf(peripheral), true)
//                    delay(500)
//                    for (blePeripheral in blePeripherals) {
//                        if (blePeripheral.id != peripheral.id) {
//                            BleUtil.shared.sendCmd(blePeripheral, BleCmdContant.AppCmdId.RecordStop)
//                            break
//                        }
//                    }
//                }

            }
        }

        if (meetEntity?.admin == mViewModel.mSipAccount.sipName) {
            setLedLevel(this, 8)
            Log.d("asdffasdf3riwergfwer","555")
        } else {
            //不是群主
            if (currentMode == 0) {
                setLedLevel(this, 0)
                Log.d("asdffasdf3riwergfwer","666")
            } else if (currentMode == 1) {
                setLedLevel(this, 8)
                Log.d("asdffasdf3riwergfwer","777")
            }
        }

        if (mViewModel.openPopTimes <= 1) {
            hideJob = Job()
            lifecycleScope.launch(Dispatchers.Main + hideJob!!) {
                delay(5000)
                hidePop()
            }
        }
    }

    private fun getBreakTimeValue(breakTimeEnum: SettingEnum.BreakTime): Float {
        val breakTimeList = mutableListOf(0.6f, 0.8f, 1.0f, 2.0f, 3.0f) // 单位秒
        return breakTimeList[SettingEnum.BreakTime.values().indexOf(breakTimeEnum)]
    }

    override fun onPause() {
        super.onPause()
        mViewModel.stopMode()
        mViewModel.exitMode()
        hideJob?.cancel()
        hideJob = null
        stopPureJob()
    }

    @Subscribe(threadMode = ThreadMode.POSTING)
    fun onHandleEvent(e: Map<String?, Any?>) {
        if (!e.containsKey(BleRecordEventName)) return
        val args = e[BleRecordEventName] as Array<*>?
        val data = args!![1] as ByteArray

        val headerLen = 4
        val encodedBuffer = ByteArray(data.size - headerLen)
        System.arraycopy(data, headerLen, encodedBuffer, 0, encodedBuffer.size)
        val decodedBuffer = ByteArray(640)
        opusCodec.decode(encodedBuffer, decodedBuffer, 320)

        val input = BytesTrans.getInstance().Bytes2Shorts(decodedBuffer)
        val output = ShortArray(input.size)
        agc?.let {
            it.processAgc(output, input)
            val bOutput = BytesTrans.getInstance().Shorts2Bytes(output)
            sendFrame(bOutput)
            fileHandle?.write(bOutput)
        }

        channelReadCache.write(decodedBuffer)
        while (channelReadCache.readable() >= ISpeechConstant.DefaultBytesPerPacketInMono) {
            val packet = ByteArray(ISpeechConstant.DefaultBytesPerPacketInMono)
            channelReadCache.read(packet)
            channel?.write(BytesTrans.getInstance().Bytes2Shorts(packet))
        }
    }

    override fun onDestroy() {
        BleUtil.shared.removeListener(bleListener)
        NetworkUtils.unregisterNetworkStatusChangedListener(netWorkListener)
        BatteryReceiver.removeChangeListener(mBatteryListener)
        mBinding.vTitleBar.removeCallbacks(runnable)
        fileHandle?.close()

//        AiSpeechManager.shareInstance().destroy()

        mViewModel.removeListener()
        mViewModel.hangUp()//挂断
        TransServiceImplWrap.setMeetingId("")
        TransServiceImplWrap.setPassword("")
        HomeServiceImplWrap.setPhoneMode(false)
        HomeServiceImplWrap.setInMode(false)
        Constant.isMeeting = false
//        TransServiceImplWrap.setInMeeting(false)
//        EventBusUtils.postEvent(SipRegisterEvent())
        EventBusUtils.postEvent(ShowDeviceDialogListEvent(TranslateMode.MEETING, show = false))
        setLedLevel(this, 0)

        SensorsUtil.trackEvent(
            SensorsCustomEvent.X1_ExitXXMode.name, hashMapOf(
                "ModelType" to "多人会议"
            )
        )

        val self = TransLanguageTool.getFullLanguageName(mViewModel.meetingLangCode)
        SensorsUtil.trackEvent(
            SensorsCustomEvent.X1_LanguageSelectionMultiPerson.name, hashMapOf(
                "SelectLanguage" to self
            )
        )

        co.timekettle.module_translate.constant.Constant.MEET_ENTITY = null
        super.onDestroy()
    }

    fun showBottomBar() {
        mBinding.tvBottomTip.visibility = View.VISIBLE
        //最后两条可见，滚动到最后的位置，否则会造成视觉差
        if (RecycleViewUtil.areLastFiveItemsVisible(mBinding.vRecycleView)) {
            RecycleViewUtil.scrollToBottom(mBinding.vRecycleView)
        }
    }

    private fun hideBottomBar() {
        mBinding.tvBottomTip.visibility = View.GONE
    }

    //开启纯净模式倒计时
    private fun startPureJob(totalSecond: Int = 5) {
        jobPure?.cancel()
        jobPure = null
        jobPure = countDownCoroutines(totalSecond, lifecycleScope, onTick = { second ->
            if (second == 0) {
                enterPureMode()
                jobPure = null
            }
        })
    }

    private fun stopPureJob() {
        jobPure?.cancel()
        jobPure = null
    }

    // 进入纯净模式
    private fun enterPureMode() {
        if (mBinding.vTitleBar.isInvisible || mBinding.vTopDevice.isInvisible) return
        hideControlBar()
        mBinding.vTitleBar.animateAlpha0()
    }

    // 退出纯净模式
    // needAutoEnter：是否需要再退出之后，又自动进入纯净模式
    private fun exitPureMode() {
        showTopBar()
        mBinding.vTitleBar.animateAlpha1()
        startPureJob()
    }

    private val runnable = {
        if (!isClicked) {
            if (mBinding.vTitleBar.isVisible) {
                enterPureMode()
            } else {
                exitPureMode()
            }
        } else {
            isClicked = false
        }
    }

    private fun exchangePureMode() {
        mBinding.vTitleBar.removeCallbacks(runnable)
        mBinding.vTitleBar.postDelayed(runnable, 200)
    }

    private fun showTopBar() {
        if (!mBinding.layoutState.isVisible) {
            mBinding.layoutState.visible()
        }

        mBinding.layoutState.animate().translationY(mBinding.layoutState.height.toFloat()).alpha(0f)
            .setDuration(300L).start()

        mBinding.vTitleBar.animate().translationY(-0f).alpha(1f).setDuration(300L).start()
    }

    private fun hideControlBar() {
        lifecycleScope.launchWhenResumed {
            mBinding.vTitleBar.animate().translationY(-mBinding.vTitleBar.height.toFloat())
                .alpha(0f).setDuration(300L).start()

            mBinding.layoutState.animate().translationY(-0f).alpha(1f).setDuration(300L).start()

            if (!mBinding.layoutState.isVisible) {
                mBinding.layoutState.visible()
            }
        }

    }

    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        when (ev?.action) {
            MotionEvent.ACTION_DOWN -> {
                x = ev.x
                y = ev.y
            }

            MotionEvent.ACTION_UP -> {
                if (ev.x == x && ev.y == y) {
                    exchangePureMode()
                }
            }

            else -> {}
        }
        return super.dispatchTouchEvent(ev)
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        if (keyCode == 24 || keyCode == 25) {
            return super.onKeyDown(keyCode, event)
        }
        return true
    }

    override fun onKeyUp(keyCode: Int, event: KeyEvent?): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            onBackPressed()
        }
        return true
    }

    companion object {
        const val TAG = "MeetingActivityMain"
    }

}