package co.timekettle.module_translate.ui.activity

import android.annotation.SuppressLint
import android.bluetooth.BluetoothA2dp
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothManager
import android.bluetooth.BluetoothProfile
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.os.VibrationEffect
import android.os.Vibrator
import android.provider.Settings
import android.util.Log
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.WindowManager
import android.widget.ScrollView
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import co.timekettle.module_translate.bean.LanguageJsonBeanChild
import co.timekettle.module_translate.bean.OfflineUiEvent
import co.timekettle.module_translate.bean.SettingEnum
import co.timekettle.module_translate.constant.IntentKey
import co.timekettle.module_translate.databinding.KeypadActivityMainBinding
import co.timekettle.module_translate.tools.TransLanguageTool
import co.timekettle.module_translate.tools.TransManager
import co.timekettle.module_translate.ui.adapter.KeypadAdapter
import co.timekettle.module_translate.ui.ktx.startTransSettingActivity
import co.timekettle.module_translate.ui.util.RecycleViewUtil
import co.timekettle.module_translate.ui.vm.KeypadViewModel
import co.timekettle.module_translate.ui.widget.LottiePlayAudioView
import co.timekettle.speech.AiSpeechManager
import co.timekettle.speech.OfflineManager
import co.timekettle.speech.SpeakManager
import com.airbnb.lottie.LottieAnimationView
import com.alibaba.android.arouter.facade.annotation.Autowired
import com.alibaba.android.arouter.facade.annotation.Route
import com.alibaba.android.arouter.launcher.ARouter
import com.blankj.utilcode.util.NetworkUtils
import com.timekettle.upup.base.BaseApp
import com.timekettle.upup.base.ktx.clickDelay
import com.timekettle.upup.base.ktx.gone
import com.timekettle.upup.base.ktx.observeLiveData
import com.timekettle.upup.base.ktx.openActivity1
import com.timekettle.upup.base.ktx.setClickEffect
import com.timekettle.upup.base.ktx.startKtxActivity
import com.timekettle.upup.base.ktx.visible
import com.timekettle.upup.base.utils.Debouncer
import com.timekettle.upup.base.utils.logD
import com.timekettle.upup.base.utils.logE
import com.timekettle.upup.base.utils.showToast
import com.timekettle.upup.comm.R
import com.timekettle.upup.comm.base.BaseActivity
import com.timekettle.upup.comm.bean.MsgBean
import com.timekettle.upup.comm.bean.MsgListWrapper
import com.timekettle.upup.comm.constant.LanDirection
import com.timekettle.upup.comm.constant.RouteUrl
import com.timekettle.upup.comm.constant.TranslateMode
import com.timekettle.upup.comm.dialog.DialogFactory
import com.timekettle.upup.comm.ktx.setLedLevel
import com.timekettle.upup.comm.model.SensorsCustomEvent
import com.timekettle.upup.comm.receiver.BatteryReceiver
import com.timekettle.upup.comm.service.home.HomeServiceImplWrap
import com.timekettle.upup.comm.utils.SensorsUtil
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import java.lang.ref.WeakReference

/**
 * 手持翻译页面
 */
@AndroidEntryPoint
@Route(path = RouteUrl.Translate.KeypadActivityMain)
class KeypadActivityMain : BaseActivity<KeypadActivityMainBinding, KeypadViewModel>() {

    @JvmField
    @Autowired(name = IntentKey.KeypadType)
    var type: Int = 0

    private lateinit var mMsgAdapter: KeypadAdapter
    private var msgList = mutableListOf<MsgBean>()
    private var mLayoutManager: LinearLayoutManager? = null
    private var jobNeedScrollToEnd: Job? = null  //  是否需要滚动到底部
    private var statTime: Long = 0
    override val mViewModel: KeypadViewModel by viewModels()
    private val debouncer = Debouncer(1000) // 设置延迟时间为 600 毫秒
    private var hasNewMsgIn: Boolean = true // 是否有新的消息进来了
    private var remainSecond: Int = 0   // 滑动还剩下的需要等待的时间
    private lateinit var loadingLav: LottieAnimationView
    private var isCanceled: Boolean = false
    private var isKeyUp: Boolean = false
    private var downTime: Long = 0L
    private var upTime: Long = 0L
    private val MAX_COUNT = 3 * 60
    private var countDown = 0
    private val handler = Handler(Looper.getMainLooper())

    private val mBatteryListener = { action: String? ->
        if (action == Intent.ACTION_POWER_CONNECTED && BatteryReceiver.isBaseCharging()) {
            window.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        } else if (action == Intent.ACTION_POWER_DISCONNECTED) {
            window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        }
    }

    private val bluetoothManager by lazy {
        applicationContext.getSystemService(Context.BLUETOOTH_SERVICE) as BluetoothManager
    }

    private val bluetoothAdapter by lazy {
        bluetoothManager.adapter
    }

    private var profileProxy: BluetoothProfile? = null

    private val profileListener = object : BluetoothProfile.ServiceListener {
        private val weakActivity = WeakReference(this@KeypadActivityMain)
        override fun onServiceConnected(profile: Int, proxy: BluetoothProfile) {
            if (profile == BluetoothProfile.A2DP) {
                weakActivity.get()?.handleA2dpConnected(proxy) // 开始监听设备状态
                weakActivity.get()?.closeProfile()
            }
        }

        override fun onServiceDisconnected(profile: Int) { }
    }

    private val a2dpEventReceiver = object : BroadcastReceiver() {
        private val weakActivity = WeakReference(this@KeypadActivityMain)
        override fun onReceive(context: Context, intent: Intent) {
            val action = intent.action
            if (action == BluetoothA2dp.ACTION_CONNECTION_STATE_CHANGED) {
                val state = intent.getIntExtra(BluetoothA2dp.EXTRA_STATE, BluetoothProfile.STATE_DISCONNECTED)
                if (state == BluetoothProfile.STATE_CONNECTED || state == BluetoothProfile.STATE_DISCONNECTED) {
                    logD("A2DP 状态变更")
                    weakActivity.get()?.connectProfile()
                }
            }
        }
    }

    private fun connectProfile() {
        bluetoothAdapter.getProfileProxy(
            applicationContext, // 使用 Application Context
            profileListener,
            BluetoothProfile.A2DP
        )
    }

    private fun handleA2dpConnected(proxy: BluetoothProfile) {
        profileProxy = proxy
        val devices = proxy.connectedDevices
        runOnUiThread { updateBtUI(devices) }
    }

    fun closeProfile() {
        profileProxy?.let { proxy ->
            bluetoothAdapter?.closeProfileProxy(BluetoothProfile.A2DP, proxy)
            profileProxy = null
        }
    }

    override fun initObserve() {
        observeLiveData(mViewModel.vadLiveData, ::processVad)
        observeLiveData(mViewModel.languageLiveData, ::processLanguageUpdate)
        observeLiveData(mViewModel.liveOfflineUiEvent, ::processOfflineUiEvent)
        observeLiveData(mViewModel.liveMsgList, ::processMsgList)
        observeLiveData(mViewModel.recognizeLiveData, ::processRecognize)
        observeLiveData(mViewModel.liveFontEnum, ::processFontEnum)
        observeLiveData(mViewModel.isShowOriginal, ::processShowOriginal)
        observeLiveData(mViewModel.errorLiveData, ::processError)
    }

    private fun processError(code: Int) {
        loadingLav.gone()
        mBinding.vRecycleView.scrollToPosition(mMsgAdapter.itemCount - 2)
    }

    private fun processShowOriginal(isShowOriginal: Boolean) {
        mMsgAdapter.setShowOriginal(isShowOriginal)
    }

    private fun processVad(type: Int) {
        if (type == 1) {
            mBinding.lavSpeak.setAnimation(ANI_VOLUME_HIGH)
        } else if (type == 2) {
            mBinding.lavSpeak.setAnimation(ANI_VOLUME_LOW)
        }
        mBinding.lavSpeak.resumeAnimation()
    }

    private fun processMsgList(listWrapper: MsgListWrapper) {
        runOnUiThread {
            val oldSize = msgList.size
            msgList.clear()
            msgList.addAll(listWrapper.data)
            mMsgAdapter.setList(msgList)
            if (oldSize <= msgList.size) { //代表的是消息插入、消息更新
                if (loadingLav.isVisible) {
                    loadingLav.gone()
                }

                hasNewMsgIn = true // 代表有新消息了

                if (RecycleViewUtil.isRecyclerViewScrolling(mBinding.vRecycleView)) {
                    return@runOnUiThread
                }

                if (remainSecond == 0) {
                    mBinding.vRecycleView.scrollToPosition(mMsgAdapter.itemCount - 2)
                }
            }
        }
    }

    private fun processRecognize(text: String) {
        mBinding.tvSpeakShow.text = text
        mBinding.slSpeak.fullScroll(ScrollView.FOCUS_DOWN)
    }

    private fun processLanguageUpdate(value: Array<LanguageJsonBeanChild>) {
        mBinding.tvBottom.text = value[0].language
        mBinding.tvUp.text = value[1].language
        mBinding.tvRight.text = value[0].language
        mBinding.tvLeft.text = value[1].language
        mViewModel.updateLang()
    }

    /**
     * 更新字体大小
     */
    private fun processFontEnum(fontSize: SettingEnum.FontSize) {
        val fontSp = fontSize.fontValue
        mMsgAdapter.setFont(fontSp + 6)
    }

    override fun initRequestData() {
        SensorsUtil.trackEvent(
            SensorsCustomEvent.X1_EnterXXMode.name, hashMapOf(
                "ModelType" to "手持翻译模式"
            )
        )

        statTime = System.currentTimeMillis()
    }

    @SuppressLint("MissingInflatedId")
    override fun KeypadActivityMainBinding.initView() {
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON) //防止息屏
        HomeServiceImplWrap.saveUserModel(TranslateMode.KEYPAD)
        HomeServiceImplWrap.setInMode(true)

        mMsgAdapter = KeypadAdapter(msgList)
        val footView = LayoutInflater.from(this@KeypadActivityMain)
            .inflate(co.timekettle.module_translate.R.layout.layout_footer_keypad, null)
        loadingLav = footView.findViewById(co.timekettle.module_translate.R.id.lav_loading)
        mMsgAdapter.setFooterView(footView)
        mLayoutManager = LinearLayoutManager(this@KeypadActivityMain)
        vRecycleView.layoutManager = mLayoutManager
        vRecycleView.adapter = mMsgAdapter

        //初始化离线，否则用不了离线功能
        OfflineManager.getInstance().init(this@KeypadActivityMain)
        NetworkUtils.registerNetworkStatusChangedListener(netWorkListener)
        setSpeechNetWork()
        mViewModel.setProductAndMode()

        vTitleBar.vTitleTv.text = getString(R.string.home_one_touch)
        tvCountDown.text = getString(R.string.trans_keypad_stop_recording).replace("XXX","10")

        vTitleBar.vTitleQuestionIcon.gone()
        mLayoutManager = LinearLayoutManager(this@KeypadActivityMain)
        setClickEffect(layoutTop, layoutBottom)

        mBinding.lavPress.playAnimation()

        connectProfile()

        registerReceiver(a2dpEventReceiver, IntentFilter().apply {
            addAction(BluetoothA2dp.ACTION_CONNECTION_STATE_CHANGED)
        })

        BatteryReceiver.addChangeListener(mBatteryListener)

        if (type != 1) return

        //获取Vibrator实例
        val vibrator = getSystemService(Context.VIBRATOR_SERVICE) as Vibrator

        if (vibrator.hasVibrator()) {
            val duration = 100L // 震动的持续时间（毫秒）
            val amplitude = 255 // 震动的强度（0-255）

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                val vibrationEffect = VibrationEffect.createOneShot(duration, amplitude)
                vibrator.vibrate(vibrationEffect)
            } else {
                // 旧版本的 Android 只支持设置持续时间，不支持设置强度
                vibrator.vibrate(duration)
            }

        } else {
            // 设备不支持震动
        }

    }

    @SuppressLint("ClickableViewAccessibility")
    override fun initListener() {

        mBinding.layoutTop.setOnClickListener { debouncer.debounce { clickBtnUp() } }

        mBinding.layoutBottom.setOnClickListener { debouncer.debounce { clickBtnBottom() } }

        mBinding.tvLeft.setOnClickListener {
            clickBtnUp()
        }

        mBinding.tvRight.setOnClickListener {
            clickBtnBottom()
        }

        mBinding.vTitleBar.vBackIv.setOnClickListener {
            finish()
        }

        mBinding.layoutBt.setOnClickListener {
            startActivity(Intent(Settings.ACTION_BLUETOOTH_SETTINGS))
        }

        mBinding.btnGuide.setOnClickListener {
            ARouter.getInstance().build(RouteUrl.Home.KeypadActivityGuide)
                .withInt(IntentKey.KeypadActivityGuideType, 1).navigation()
        }

        mBinding.vTitleBar.vSettingIcon.clickDelay {
            startTransSettingActivity(HomeServiceImplWrap.getUserModel(), mViewModel.modeUtil)
        }

        mMsgAdapter.apply {
            addChildClickViewIds(co.timekettle.module_translate.R.id.v_play_btn)
            setOnItemChildClickListener { adapter, view, position ->
                if (view is LottiePlayAudioView) {
                    debouncer.debounce {
                        val msgBean = (adapter as KeypadAdapter).data[position]
                        if (view.playState == LottiePlayAudioView.PlayState.Playing) {
                            //如果正在播放，就停止
                            mViewModel.stopPlayText(msgBean)
                            mViewModel.updateMsgBean(msgBean.apply { isPlaying = false })
                            adapter.notifyDataSetChanged()
                        } else {
                            //没有正在播放
                            SpeakManager.shareInstance().stop() //先停止所有播放
                            mViewModel.playText(msgBean, startCallBack = {
                                mViewModel.updateMsgBean(msgBean.apply { isPlaying = true })
                                adapter.notifyDataSetChanged()
                            }, finishCallback = {
                                lifecycleScope.launch(Dispatchers.Main) {
                                    if (msgBean.session == it) {
                                        mViewModel.updateMsgBean(msgBean.apply {
                                            isPlaying = false
                                        })
                                        adapter.notifyDataSetChanged()
                                    }
                                }
                            })
                        }
                    }
                }
            }
        }
    }

    private fun clickBtnBottom() {
        val setting = TransManager.getModelSetting(HomeServiceImplWrap.getUserModel())
        if (setting.isOpenOffline) {
            startKtxActivity<TranslateActivityOffline>()
        } else {
            val param = IntentKey.LanguageType to LanDirection.Mine
            openActivity1<ChooseLangActivity>(param)
        }
    }

    private fun clickBtnUp() {
        val setting = TransManager.getModelSetting(HomeServiceImplWrap.getUserModel())
        if (setting.isOpenOffline) {
            startKtxActivity<TranslateActivityOffline>()
        } else {
            val param = IntentKey.LanguageType to LanDirection.Other
            openActivity1<ChooseLangActivity>(param)
        }
    }

    private val netWorkListener = object : NetworkUtils.OnNetworkStatusChangedListener {
        override fun onDisconnected() {
            if (!mViewModel.modeUtil.isSupportOffline()) {
                showToast(getString(R.string.common_network_disconnected))
            }
            setSpeechNetWork()
        }

        override fun onConnected(networkType: NetworkUtils.NetworkType?) {
            setSpeechNetWork()
        }
    }

    private fun setSpeechNetWork() {
        AiSpeechManager.shareInstance().isOnlyOffline = !NetworkUtils.isConnected() ||
                TransManager.getModelSetting(HomeServiceImplWrap.getUserModel()).isOpenOffline
    }

    /**
     * 从设置界面返回去加载设置信息，恢复通道可用
     */
    override fun onResume() {
        super.onResume()
        HomeServiceImplWrap.saveUserModel(TranslateMode.KEYPAD)

        mViewModel.enterMode(this@KeypadActivityMain)
        mViewModel.startMode()

        val setting = TransManager.getModelSetting(HomeServiceImplWrap.getUserModel())
        mViewModel.liveFontEnum.value = setting.fontSize
        mViewModel.isShowOriginal.value = setting.isShowOriginal
        mViewModel.modeUtil.setBreakTime((getBreakTimeValue(setting.breakTime) * 1000).toInt())
        AiSpeechManager.shareInstance().audioChannels.forEach {
            SpeakManager.shareInstance().update(it.speakerType, setting.ttsSpeed.value)
        }
        mViewModel.modeUtil.setVoice(setting.ttsVoiceIsManSelf, setting.ttsVoiceIsManOther)

        if (setting.isOpenOffline) {
            if (setting.offlineCode.contains("<->")) {

            }
        } else {
            mViewModel.updateLanguage("", "")
        }
    }

    @SuppressLint("MissingPermission")
    private fun updateBtUI(devices: List<BluetoothDevice>) {
        logD("update BT device = $devices")
        if (devices.isNotEmpty()) {
            val deviceName = devices[0].name ?: devices[0].address
            mBinding.ivBt.setImageResource(co.timekettle.module_translate.R.mipmap.ask_add_icon_bluethooth)
            mBinding.tvBt.text = deviceName
        } else {
            mBinding.ivBt.setImageResource(co.timekettle.module_translate.R.mipmap.ask_add_icon_link)
            mBinding.tvBt.text = getString(R.string.trans_bluetooth_speaker)
        }
    }

    private fun getBreakTimeValue(breakTimeEnum: SettingEnum.BreakTime): Float {
        val breakTimeList = mutableListOf(0.6f, 0.8f, 1.0f, 2.0f, 3.0f) // 单位秒
        return breakTimeList[SettingEnum.BreakTime.values().indexOf(breakTimeEnum)]
    }

    override fun onPause() {
        super.onPause()
        mViewModel.stopMode()
        mViewModel.exitMode()
    }

    override fun onDestroy() {
        HomeServiceImplWrap.setInMode(false)
        handler.removeCallbacksAndMessages(null)
        NetworkUtils.unregisterNetworkStatusChangedListener(netWorkListener)

        BatteryReceiver.removeChangeListener(mBatteryListener)

        SensorsUtil.trackEvent(
            SensorsCustomEvent.X1_ExitXXMode.name, hashMapOf(
                "ModelType" to "手持翻译模式"
            )
        )

        SensorsUtil.trackEvent(
            SensorsCustomEvent.X1_AskAndGoUsageDuration.name, hashMapOf(
                "ModeDuration" to (System.currentTimeMillis() - statTime) / 1000
            )
        )

        val self = TransLanguageTool.getFullLanguageName(TransManager.getLastlyUseLanguageSelf())
        SensorsUtil.trackEvent(
            SensorsCustomEvent.X1_LanguageSelectionAskAndGo.name, hashMapOf(
                "SelectLanguage" to self
            )
        )

        closeProfile()
        unregisterReceiver(a2dpEventReceiver)
        super.onDestroy()
    }

    private fun processOfflineUiEvent(event: OfflineUiEvent) {
        when (event) {
            OfflineUiEvent.YouCanOpenOffline -> {
                DialogFactory.createConfirmCancelDialog(this,
                    titleText = BaseApp.context.getString(R.string.common_alert_tip),
                    BaseApp.context.getString(R.string.common_network_error_check_it),
                    confirmText = BaseApp.context.getString(R.string.common_cancel),
                    confirmCall = {},
                    cancelText = BaseApp.context.getString(R.string.common_go_setting),
                    cancelCall = {
                        wifiForResult.launch(Intent(Settings.ACTION_WIFI_SETTINGS))
                    }).show()
            }

            OfflineUiEvent.NetWorkError -> {
                showToast(BaseApp.context.getString(R.string.common_network_error_check_it))
            }
        }
    }

    private val countDownRunnable: Runnable = object : Runnable {
        override fun run() {
            if (++countDown >= MAX_COUNT) {
                onKeyUp()
            }
            if (countDown == MAX_COUNT - 10) {
                mBinding.tvCountDown.visible()
            }
            handler.postDelayed(this, 1000)
        }
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            if (isKeyCode && event!!.eventTime - event.downTime > 0) {
                isKeyCode = false

                downTime = System.currentTimeMillis()
                handler.removeCallbacksAndMessages(null)

                setLedLevel(this, 8)
                mBinding.vRecycleView.scrollToPosition(mMsgAdapter.itemCount - 1)
                loadingLav.visible()
                mBinding.tvCountDown.gone()
                mBinding.layoutChoose.gone()
                mBinding.layoutList.visible()
                mBinding.layoutSpeak.visible()
                mBinding.lavSpeak.playAnimation()
                mBinding.tvSpeakShow.text = getString(R.string.translate_speak_please)
                mViewModel.isStartSpeak = true
                mViewModel.isComplete = false
                mViewModel.isVadBegin = false
                mViewModel.isDetermineLanguage = false
                isCanceled = false
                mViewModel.originalText = ""
                mViewModel.translateText = ""
                mViewModel.originalTempText = ""
                mViewModel.translateTempText = ""
                mViewModel.enableChannel()
                mViewModel.stopAllWorker()
                countDown = 0
                isKeyUp = false
                handler.post(countDownRunnable)
            }
        } else if (keyCode == 24 || keyCode == 25) {
            return super.onKeyDown(keyCode, event)
        }

        return true
    }

    override fun onKeyUp(keyCode: Int, event: KeyEvent?): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            if (!isKeyCode) {
                isKeyCode = true
                onKeyUp()
            } else {
                onBackPressed()
            }
        }

        return true
    }

    private fun onKeyUp() {
        if (isKeyUp) return
        isKeyUp = true

        upTime = System.currentTimeMillis()

        setLedLevel(this, 0)
        mBinding.lavSpeak.cancelAnimation()
        mBinding.layoutSpeak.gone()
        mViewModel.isStartSpeak = false
        mViewModel.disableChannel()

        handler.removeCallbacks(countDownRunnable)

        if (mViewModel.isComplete) {
            mViewModel.msgBean?.let {
                Log.d("sdasdifawieqweqsdfsd", "***1***")
                mViewModel.playText(it)
                mViewModel.msgBean = null
            }
        }

        handler.postDelayed({
            loadingLav.gone()
            if (msgList.isEmpty()) {
                Log.d("asdfquwefquwefiqw","1")
                mBinding.layoutChoose.visible()
                mBinding.layoutList.gone()
            } else if (!mViewModel.isVadBegin) {
                Log.d("asdfquwefquwefiqw","2")
                mBinding.vRecycleView.scrollToPosition(mMsgAdapter.itemCount - 2)
                if (!isCanceled && upTime - downTime > 1000) {
                    showToast(getString(R.string.trans_keypad_speak_again))
                }
            } else {
                Log.d("asdfquwefquwefiqw","3")
                mBinding.vRecycleView.scrollToPosition(mMsgAdapter.itemCount - 2)
            }

            mViewModel.isVadBegin = false
        }, 1000)
    }

    private val wifiForResult =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result: ActivityResult ->
            mViewModel.checkWifiAgain()
        }

    companion object {
        private const val ANI_VOLUME_HIGH = "ani_hand_recoimd_high.json"
        private const val ANI_VOLUME_LOW = "ani_hand_recoimd_low.json"
    }

}