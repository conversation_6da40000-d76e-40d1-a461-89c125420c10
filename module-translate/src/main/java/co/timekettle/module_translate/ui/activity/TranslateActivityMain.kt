package co.timekettle.module_translate.ui.activity

import android.app.Dialog
import android.content.Context
import android.content.Intent
import android.media.AudioManager
import android.os.Bundle
import android.provider.Settings
import android.util.Log
import android.view.Gravity
import android.view.KeyEvent
import android.view.WindowManager
import android.widget.PopupWindow
import androidx.activity.OnBackPressedCallback
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.fragment.app.Fragment
import androidx.fragment.app.commit
import androidx.lifecycle.lifecycleScope
import co.timekettle.btkit.BleUtil
import co.timekettle.btkit.bean.BleEventBean
import co.timekettle.btkit.bean.RawBlePeripheral
import co.timekettle.module_translate.bean.OfflineUiEvent
import co.timekettle.module_translate.bean.SettingEnum
import co.timekettle.module_translate.databinding.TranslateActivityMainBinding
import co.timekettle.module_translate.tools.TransLanguageTool
import co.timekettle.module_translate.tools.TransManager
import co.timekettle.module_translate.ui.fragment.TransFragmentSameSide
import co.timekettle.module_translate.ui.vm.TransViewModel
import co.timekettle.speech.AiSpeechManager
import co.timekettle.speech.OfflineManager
import co.timekettle.speech.SpeakManager
import com.alibaba.android.arouter.facade.annotation.Autowired
import com.alibaba.android.arouter.facade.annotation.Route
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.NetworkUtils
import com.hjq.permissions.OnPermissionCallback
import com.hjq.permissions.Permission
import com.hjq.permissions.XXPermissions
import com.timekettle.upup.base.BaseApp
import com.timekettle.upup.base.ktx.observeLiveData
import com.timekettle.upup.base.utils.EventBusRegister
import com.timekettle.upup.base.utils.SpUtils
import com.timekettle.upup.base.utils.logD
import com.timekettle.upup.base.utils.showDebugToast
import com.timekettle.upup.base.utils.showToast
import com.timekettle.upup.comm.BuildConfig
import com.timekettle.upup.comm.R
import com.timekettle.upup.comm.base.BaseActivity
import com.timekettle.upup.comm.constant.RouteUrl
import com.timekettle.upup.comm.constant.SpKey
import com.timekettle.upup.comm.dialog.DialogFactory
import com.timekettle.upup.comm.model.SensorsCustomEvent
import com.timekettle.upup.comm.receiver.BatteryReceiver
import com.timekettle.upup.comm.service.home.HomeServiceImplWrap
import com.timekettle.upup.comm.utils.SensorsUtil
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.math.BigDecimal

/**
 * 双人对话页面
 */
@EventBusRegister
@AndroidEntryPoint
@Route(path = RouteUrl.Translate.TranslateActivityMain)
class TranslateActivityMain : BaseActivity<TranslateActivityMainBinding, TransViewModel>() {

    @JvmField
    @Autowired(name = "offlineSelfCode")
    var offlineSelfCode: String = ""

    @JvmField
    @Autowired(name = "offlineOtherCode")
    var offlineOtherCode: String = ""

    var mPermissionTopPop: PopupWindow? = null
    private var mReplaceFragment: Fragment? = null
    private var mDialogDisconnect: Dialog? = null
    private var statTime: Long = 0
    override val mViewModel: TransViewModel by viewModels()
    private val STREAM_BLE_VOLUME = "BLE_VOLUME"
    private val MIN_STREAM_BLE_VOLUME = 0
    private val MAX_STREAM_BLE_VOLUME = 100
    private val DEFAULT_STREAM_BLE_VOLUME = 80
    lateinit var mAudioManager: AudioManager

    private val mBatteryListener = { action: String? ->
        if (action == Intent.ACTION_POWER_CONNECTED && BatteryReceiver.isBaseCharging()) {
            window.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        } else if (action == Intent.ACTION_POWER_DISCONNECTED) {
            window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        }
    }

    override fun initObserve() {
        observeLiveData(mViewModel.liveOfflineUiEvent, ::processOfflineUiEvent)
    }

    override fun initRequestData() {
        SensorsUtil.trackEvent(
            SensorsCustomEvent.X1_EnterXXMode.name, hashMapOf(
                "ModelType" to "双人对话"
            )
        )

        statTime = System.currentTimeMillis()
    }

    override fun TranslateActivityMainBinding.initView() {
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON) // 防止息屏
        mAudioManager = getSystemService(Context.AUDIO_SERVICE) as AudioManager
        //初始化离线，否则用不了离线功能
        OfflineManager.getInstance().init(this@TranslateActivityMain)
        replaceFragment()
        checkPermissions()

        BatteryReceiver.addChangeListener(mBatteryListener)
    }



    fun processBack() {
        DialogFactory.createConfirmCancelDialog(this@TranslateActivityMain,
            content = getString(R.string.translate_exit),
            confirmCall = {
                finish()
            }).show()
    }

    override fun initListener() {
        onBackPressedDispatcher.addCallback(
            this@TranslateActivityMain,
            object : OnBackPressedCallback(true) {
                override fun handleOnBackPressed() {
                    processBack()
                }
            })

    }

    private fun checkPermissions() {
        lifecycleScope.launchWhenStarted {
            if (!XXPermissions.isGranted(
                    this@TranslateActivityMain,
                    Permission.RECORD_AUDIO,
                    Permission.READ_PHONE_STATE
                )
            ) {
                showDebugToast("需要权限")
            }

            XXPermissions.with(this@TranslateActivityMain)
                .permission(Permission.RECORD_AUDIO)
                .permission(Permission.READ_PHONE_STATE)
                .request(object : OnPermissionCallback {
                    override fun onGranted(permissions: MutableList<String>, all: Boolean) {
                        mPermissionTopPop?.dismiss()
                        mPermissionTopPop = null
                        if (all) {
//                            setSpeechNetWork()
//                            setTransSetting()
                        } else {
                            showDebugToast("权限未获取到！检查一下")
                        }
                    }

                    override fun onDenied(permissions: MutableList<String>, never: Boolean) {
                        mPermissionTopPop?.dismiss()
                        mPermissionTopPop = null
                        showDebugToast("权限未获取到！检查一下")
                    }
                })
            delay(200)
            mPermissionTopPop?.showAtLocation(mBinding.root, Gravity.TOP, 0, 0)

        }
    }

    private fun getBreakTimeValue(breakTimeEnum: SettingEnum.BreakTime): Float {
        val breakTimeList = mutableListOf(0.6f, 0.8f, 1.0f, 2.0f, 3.0f) // 单位秒
        return breakTimeList[SettingEnum.BreakTime.values().indexOf(breakTimeEnum)]
    }

    fun setTransSetting() {
        val setting = TransManager.getModelSetting(HomeServiceImplWrap.getUserModel())
        AiSpeechManager.shareInstance().audioChannels.forEach {
            SpeakManager.shareInstance().update(it.speakerType, setting.ttsSpeed.value)
        }
        mViewModel.modeUtil.setBreakTime((getBreakTimeValue(setting.breakTime) * 1000).toInt())
    }

    private fun replaceFragment() {
        supportFragmentManager.commit(true) {
            setCustomAnimations(
                co.timekettle.module_translate.R.anim.fade_in,
                co.timekettle.module_translate.R.anim.fade_out
            )
            mReplaceFragment = mReplaceFragment ?: TransFragmentSameSide()
            replace(co.timekettle.module_translate.R.id.ll_frame, mReplaceFragment!!)
        }

    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mViewModel.setProductAndMode(offlineSelfCode, offlineOtherCode)
        NetworkUtils.registerNetworkStatusChangedListener(netWorkListener)
        HomeServiceImplWrap.setInMode(true)
    }

    private val netWorkListener = object : NetworkUtils.OnNetworkStatusChangedListener {
        override fun onDisconnected() {
            if (!mViewModel.modeUtil.isSupportOffline()) {
                showToast(getString(R.string.common_network_disconnected))
            }
            setSpeechNetWork()
        }

        override fun onConnected(networkType: NetworkUtils.NetworkType?) {
            setSpeechNetWork()
        }

    }

    private fun setSpeechNetWork() {
        AiSpeechManager.shareInstance().isOnlyOffline = !NetworkUtils.isConnected() ||
                TransManager.getModelSetting(HomeServiceImplWrap.getUserModel()).isOpenOffline
    }

    @Subscribe(threadMode = ThreadMode.POSTING)
    fun onBleEvent(event: BleEventBean) {
        if (event.eventName == BleUtil.BleEventName.BleDisconnectedPeripheral) {
            logD("设备BLE已断开 $event", TAG)
            showBleDisConnectDialog(event.blePeripheral)
        } else if (event.eventName == BleUtil.BleEventName.BleDisconnectedSubPeripheral) {
            logD("设备BLE从机已断开 $event", TAG)
            showBleDisConnectDialog(event.blePeripheral)
        }
    }

    private fun showBleDisConnectDialog(per: RawBlePeripheral) {
        lifecycleScope.launch {
            if (mDialogDisconnect != null) return@launch
            mDialogDisconnect = DialogFactory.createConfirmDialog(
                context = ActivityUtils.getTopActivity(),
                titleText = BaseApp.context.getString(com.timekettle.upup.comm.R.string.common_alert_tip),
                content = BaseApp.context.getString(com.timekettle.upup.comm.R.string.device_already_disconnected_use_after_connect)
                    .replace("XXX", per.name.trim()),
                canceledOnTouchOutside = false,
                cancelable = false,
                confirmCall = {
                    ActivityUtils.finishToActivity(TranslateActivityHome::class.java,false,true)
                },
            )
            mDialogDisconnect?.show()
        }
    }

    //从设置界面返回去加载设置信息，恢复通道可用
    override fun onResume() {
        super.onResume()
        mViewModel.enterMode(this@TranslateActivityMain)
        mViewModel.startMode()
        setSpeechNetWork()
        val setting = TransManager.getModelSetting(HomeServiceImplWrap.getUserModel())
        if (SpUtils.getBoolean(SpKey.IS_DEBUG_STATUS, false) || BuildConfig.DEBUG) {
            if (setting.minVadHeadsetEnergy != "-1") {
                Log.d("translate", "set headset vad energy factor = ${setting.minVadHeadsetEnergy}")
                AiSpeechManager.shareInstance().setMinVadEnergy(
                    BigDecimal(100000).div(setting.minVadHeadsetEnergy.toBigDecimal()).toInt()
                )
            }
        }
        mViewModel.liveFontEnum.value = setting.fontSize
        mViewModel.liveIsShowOriginal.value = setting.isShowOriginal
        mViewModel.modeUtil.setBreakTime((getBreakTimeValue(setting.breakTime) * 1000).toInt())
        AiSpeechManager.shareInstance().audioChannels.forEach {
            SpeakManager.shareInstance().update(it.speakerType, setting.ttsSpeed.value)
        }
        mViewModel.modeUtil.setVoice(setting.ttsVoiceIsManSelf, setting.ttsVoiceIsManOther)

//        mViewModel.resumeMode()
        if (mViewModel.liveIsPause.value == false) {
            mViewModel.enableAllChannel()
        }
    }

    override fun onPause() {
        super.onPause()
        mViewModel.stopMode()
        mViewModel.exitMode()
    }

    override fun onDestroy() {
//        mViewModel.stopMode()
//        mViewModel.exitMode()
        HomeServiceImplWrap.setInMode(false)
        NetworkUtils.unregisterNetworkStatusChangedListener(netWorkListener)

        BatteryReceiver.removeChangeListener(mBatteryListener)

        SensorsUtil.trackEvent(
            SensorsCustomEvent.X1_EnterXXMode.name, hashMapOf(
                "ModelType" to "双人对话"
            )
        )

        SensorsUtil.trackEvent(
            SensorsCustomEvent.X1_OneOnOneUsageDuration.name, hashMapOf(
                "ModeDuration" to (System.currentTimeMillis() - statTime) / 1000
            )
        )

        val self = TransLanguageTool.getFullLanguageName(mViewModel.selfCode)
        val other = TransLanguageTool.getFullLanguageName(mViewModel.otherCode)
        SensorsUtil.trackEvent(
            SensorsCustomEvent.X1_LanguageSelectionOneOnOne.name, hashMapOf(
                "SelectLanguage" to "$self-$other"
            )
        )
        super.onDestroy()
    }

    private fun processOfflineUiEvent(event: OfflineUiEvent) {
        when (event) {
            OfflineUiEvent.YouCanOpenOffline -> {
                DialogFactory.createConfirmCancelDialog(this,
                    titleText = BaseApp.context.getString(R.string.common_alert_tip),
                    BaseApp.context.getString(R.string.common_network_error_check_it),
                    confirmText = BaseApp.context.getString(R.string.common_cancel),
                    confirmCall = {},
                    cancelText = BaseApp.context.getString(R.string.common_go_setting),
                    cancelCall = {
                        wifiForResult.launch(Intent(Settings.ACTION_WIFI_SETTINGS))
                    }).show()
            }

            OfflineUiEvent.NetWorkError -> {
                showToast(BaseApp.context.getString(com.timekettle.upup.comm.R.string.common_network_error_check_it))
            }
        }
    }

    override fun onStop() {
        super.onStop()
    }

    // 监听，适配BLE音量的调节
    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        if (keyCode == KeyEvent.KEYCODE_VOLUME_DOWN || keyCode == KeyEvent.KEYCODE_VOLUME_UP) {
            var bLEVolume = Settings.System.getInt(
                contentResolver,
                STREAM_BLE_VOLUME,
                DEFAULT_STREAM_BLE_VOLUME
            )
            if (keyCode == KeyEvent.KEYCODE_VOLUME_DOWN) {
                bLEVolume -= 4
            } else {
                bLEVolume += 4
            }
            if (bLEVolume < MIN_STREAM_BLE_VOLUME) {
                bLEVolume = MIN_STREAM_BLE_VOLUME
            }
            if (bLEVolume > MAX_STREAM_BLE_VOLUME) {
                bLEVolume = MAX_STREAM_BLE_VOLUME
            }
            Settings.System.putInt(contentResolver, STREAM_BLE_VOLUME, bLEVolume)
            mAudioManager.adjustStreamVolume(
                AudioManager.STREAM_MUSIC,
                AudioManager.ADJUST_SAME,
                AudioManager.FLAG_SHOW_UI
            )
            return true
        }
        return super.onKeyDown(keyCode, event)
    }

    override fun onKeyUp(keyCode: Int, event: KeyEvent?): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            onBackPressed()
        }
        return true
    }


    private val wifiForResult =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result: ActivityResult ->
            mViewModel.checkWifiAgain()
        }

    companion object {
        const val TAG = "TranslateActivityMain"
    }

}