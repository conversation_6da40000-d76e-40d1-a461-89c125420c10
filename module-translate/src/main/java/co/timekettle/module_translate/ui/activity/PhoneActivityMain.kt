package co.timekettle.module_translate.ui.activity

import android.animation.ValueAnimator
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.media.AudioManager
import android.os.Bundle
import android.util.Log
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import android.widget.TextView
import androidx.activity.OnBackPressedCallback
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import androidx.core.view.updateLayoutParams
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import co.timekettle.btkit.BleCmdContant
import co.timekettle.btkit.BleUtil
import co.timekettle.btkit.bean.RawBlePeripheral
import co.timekettle.btkit.bean.WT2BlePeripheral
import co.timekettle.module_translate.R
import co.timekettle.module_translate.bean.LanguageJsonBeanChild
import co.timekettle.module_translate.bean.SettingEnum
import co.timekettle.module_translate.constant.Constant.BleRecordEventName
import co.timekettle.module_translate.constant.IntentKey
import co.timekettle.module_translate.databinding.ActivityPhoneMainBinding
import co.timekettle.module_translate.tools.TransLanguageTool
import co.timekettle.module_translate.tools.TransManager
import co.timekettle.module_translate.tools.TransStringUtil
import co.timekettle.module_translate.ui.adapter.PhoneMsgAdapter
import co.timekettle.module_translate.ui.ktx.startTransSettingActivity
import co.timekettle.module_translate.ui.util.RecycleViewUtil
import co.timekettle.module_translate.ui.util.TransUiUtil
import co.timekettle.module_translate.ui.vm.MeetingMainViewModel
import co.timekettle.module_translate.ui.vm.PhoneMainVM
import co.timekettle.module_translate.ui.widget.LottiePlayAudioView
import co.timekettle.opus.OpusCodec
import co.timekettle.sip.audio.SipAudioTrack
import co.timekettle.sip.call.CallManager
import co.timekettle.sip.call.sendFrame
import co.timekettle.sip.call.sendSip
import co.timekettle.sip.entity.ReqEntity
import co.timekettle.sip.utils.GsonUtil
import co.timekettle.speech.AiSpeechManager
import co.timekettle.speech.SpeakManager
import co.timekettle.speech.TestRecorder
import com.alibaba.android.arouter.facade.annotation.Autowired
import com.alibaba.android.arouter.facade.annotation.Route
import com.blankj.utilcode.util.ConvertUtils
import com.timekettle.upup.base.ktx.animateAlpha0
import com.timekettle.upup.base.ktx.animateAlpha1
import com.timekettle.upup.base.ktx.gone
import com.timekettle.upup.base.ktx.isInvisible
import com.timekettle.upup.base.ktx.observeLiveData
import com.timekettle.upup.base.ktx.startKtxActivity
import com.timekettle.upup.base.utils.EventBusRegister
import com.timekettle.upup.base.utils.EventBusUtils
import com.timekettle.upup.base.utils.countDownCoroutines
import com.timekettle.upup.base.utils.logD
import com.timekettle.upup.base.utils.showDebugToast
import com.timekettle.upup.comm.base.BaseActivity
import com.timekettle.upup.comm.bean.MsgBean
import com.timekettle.upup.comm.bean.MsgDirection
import com.timekettle.upup.comm.bean.MsgListWrapper
import com.timekettle.upup.comm.conference.ConController
import com.timekettle.upup.comm.conference.addCallMessageBackListener
import com.timekettle.upup.comm.conference.addCallStateBackListener
import com.timekettle.upup.comm.conference.removeCallMessageBackListener
import com.timekettle.upup.comm.conference.removeCallStateBackListener
import com.timekettle.upup.comm.constant.RouteUrl
import com.timekettle.upup.comm.constant.TranslateMode
import com.timekettle.upup.comm.dialog.DialogFactory
import com.timekettle.upup.comm.ktx.setLedLevel
import com.timekettle.upup.comm.model.SensorsCustomEvent
import com.timekettle.upup.comm.model.ShowDeviceDialogListEvent
import com.timekettle.upup.comm.receiver.BatteryReceiver
import com.timekettle.upup.comm.service.home.HomeServiceImplWrap
import com.timekettle.upup.comm.tools.DeviceTool
import com.timekettle.upup.comm.utils.SensorsUtil
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.json.JSONObject

/**
 * 远程语音模式页面、远程模式、语音模式、电话翻译页面
 */
@EventBusRegister
@AndroidEntryPoint
@Route(path = RouteUrl.Translate.PhoneActivityMain)
class PhoneActivityMain : BaseActivity<ActivityPhoneMainBinding, PhoneMainVM>() {

    @JvmField
    @Autowired(name = IntentKey.OtherLang)
    var otherLang: String = ""

    @JvmField
    @Autowired(name = IntentKey.Account)
    var account: String = ""

    private var opusCodec = OpusCodec(16000, 1)
    private var msgList = mutableListOf<MsgBean>()
    private lateinit var mMsgAdapter: PhoneMsgAdapter
    private var mLayoutManager: LinearLayoutManager? = null
    private var jobNeedScrollToEnd: Job? = null  //  是否需要滚动到底部
    private var callStatusListener: ((Int, Int, String, String, String, String) -> Unit)? = null
    private var messageListener: ((String) -> Unit)? = null
    private lateinit var oldCode: String
    private var bleListener: BleUtil.Listener? = null
    override val mViewModel: PhoneMainVM by viewModels()
    private var jobPure: Job? = null
    private var isClicked = false //播放按钮是否被点击
    private var hasNewMsgIn: Boolean = true // 是否有新的消息进来了
    private var remainSecond: Int = 0   // 滑动还剩下的需要等待的时间
    private var isRemoved = false
    private var x = 0f
    private var y = 0f
    private var onCreate: Boolean = false

    private val mBatteryListener = { action: String? ->
        if (action == Intent.ACTION_POWER_CONNECTED && BatteryReceiver.isBaseCharging()) {
            window.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        } else if (action == Intent.ACTION_POWER_DISCONNECTED) {
            window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        }
    }

    private var fileHandle: TestRecorder? = null
    private var fileHandleRemote: TestRecorder? = null

//    private val filePath = Environment.getExternalStorageDirectory().absolutePath + "/16ktts(zh-CN)-10s音频.pcm"
//    private val buffer = ByteArray(640)
//    private var fileInputStream = FileInputStream(filePath)

    private val resultLauncher =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            if (result.resultCode == RESULT_OK) {
                val code = result.data?.getStringExtra("lang_code")
                mBinding.tvMeetingLang.text = TransLanguageTool.getLanguageNameByCode(code)
                TransManager.saveLastlyPhoneUseLanguage(code!!)

                isRemoved = false
                mViewModel.setSelfLang(code)
                mMsgAdapter.setFooterView(getFootView())
                showDebugToast("${mViewModel.getSelfLang()} --- ${mViewModel.getOtherLang()}")
                mViewModel.updateLan(code, mViewModel.getOtherLang())
                CallManager.sendSip(ReqEntity(333, mViewModel.getSelfLang()))
            }
        }

    private val volumeReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            if (intent.action.equals("android.media.VOLUME_CHANGED_ACTION")) {
                setVolume()
            }
        }
    }

    private fun setVolume() {
        // 获取系统音量
        val audioManager = getSystemService(Context.AUDIO_SERVICE) as AudioManager
        val systemVolume = audioManager.getStreamVolume(AudioManager.STREAM_MUSIC)

        // 计算音量比例
        val maxVolume = audioManager.getStreamMaxVolume(AudioManager.STREAM_MUSIC)
        val volumeRatio = systemVolume.toFloat() / maxVolume

        SipAudioTrack.setVolume(volumeRatio)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        HomeServiceImplWrap.saveUserModel(TranslateMode.PHONE)
        HomeServiceImplWrap.setPhoneMode(true)
        HomeServiceImplWrap.setInMode(true)

        if (HomeServiceImplWrap.getPhoneState() == PJSIP_INV_STATE_DISCONNECTED) {
            finish()
        } else {
            if (BleUtil.shared.connectedPeripherals.size == 0) {
                EventBusUtils.postEvent(ShowDeviceDialogListEvent(TranslateMode.PHONE))
            }

            onCreate = true
        }

//        fileHandle = TestRecorder(this, "TK_Record", null, "phone" + "-raw-record", true)
//        fileHandleRemote = TestRecorder(this, "TK_Record", null, "phone_remote" + "-raw-record", true)

    }

    override fun initObserve() {
        observeLiveData(mViewModel.livePhoneLanguage, ::phoneLangUpdate)
        observeLiveData(mViewModel.liveMsgList, ::processMsgList)
        observeLiveData(mViewModel.isShowOriginal, ::processShowOriginal)
        observeLiveData(mViewModel.liveFontEnum, ::updateFontSize)
        observeLiveData(mViewModel.callTime, ::processCallTime)
    }

    private fun processCallTime(text: String) {
        mBinding.tvTimeTitle.text = text
    }

    private fun updateFontSize(fontSize: SettingEnum.FontSize) {
        val fontSp = fontSize.fontValue
        mMsgAdapter.setFont(fontSp)
    }

    private fun processShowOriginal(isShowOriginal: Boolean) {
        mMsgAdapter.setShowOriginal(isShowOriginal)
    }

    private fun phoneLangUpdate(languageJsonBeanChild: LanguageJsonBeanChild?) {
        mBinding.tvMeetingLang.text = languageJsonBeanChild?.language
        oldCode = languageJsonBeanChild?.code.toString()
    }

    private fun processMsgList(msgListWrapper: MsgListWrapper?) {
        lifecycleScope.launch(Dispatchers.Main) {
            if (msgListWrapper?.direction == MsgDirection.Right) {
                if (!isRemoved) {
                    isRemoved = true
                    mMsgAdapter.removeAllFooterView()
                }
            }

            val oldSize = msgList.size
            msgList.clear()
            msgList.addAll(msgListWrapper?.data!!)
            mMsgAdapter.setList(msgList)

            if (oldSize <= msgList.size) { //代表的是消息插入、消息更新
                hasNewMsgIn = true // 代表有新消息了

                if (RecycleViewUtil.isRecyclerViewScrolling(mBinding.vRecycleView)) {
                    return@launch
                }

                if (remainSecond == 0) {
                    RecycleViewUtil.scrollToBottom(mBinding.vRecycleView)
                }
            }
        }
    }

    override fun initRequestData() {
        mViewModel.startCallTimer()

        SensorsUtil.trackEvent(
            SensorsCustomEvent.X1_EnterXXMode.name, hashMapOf(
                "ModelType" to "远程语音"
            )
        )
    }

    override fun initListener() {
        initBleListener()
        initCallListener()
    }

    private fun initCallListener() {
        callStatusListener = { state, _, _, lastReason, _, _ ->
            runOnUiThread {
                if (state == PJSIP_INV_STATE_DISCONNECTED) {

                    logD("电话断开原因 $lastReason", TAG)

                    if (oldCode != TransManager.getLastlyPhoneUseLanguage()) {
                        // 切换语言后，要重新创建账号才能更换语言
//                        EventBusUtils.postEvent(SipRegisterEvent())
                    }

                    Log.d("qweiqiweriqwerqwer", "111")
                    //通话断开
                    finish()
                }
            }
        }

        messageListener = {
            runOnUiThread {
                val obj = JSONObject(it)
                when (obj.optInt("type")) {
                    333 -> {
                        val res = GsonUtil.fromJson(it, ReqEntity::class.java)

                        mViewModel.setOtherLang(res.data)
                        showDebugToast("${mViewModel.getSelfLang()} --- ${mViewModel.getOtherLang()}")
                        mViewModel.updateLan(mViewModel.getSelfLang(), mViewModel.getOtherLang())
                    }
                }
            }
        }

        ConController.addCallStateBackListener(callStatusListener)
        ConController.addCallMessageBackListener(messageListener)
    }

    override fun ActivityPhoneMainBinding.initView() {
        val audioManager = getSystemService(AUDIO_SERVICE) as AudioManager
        audioManager.mode = AudioManager.MODE_NORMAL
        audioManager.isSpeakerphoneOn = true
        val maxVolume = audioManager.getStreamMaxVolume(AudioManager.STREAM_VOICE_CALL)
        audioManager.setStreamVolume(
            AudioManager.STREAM_VOICE_CALL, maxVolume, AudioManager.FLAG_ALLOW_RINGER_MODES
        )

        mViewModel.updatePhoneLanguage()
        mViewModel.setOtherLang(otherLang)

        mViewModel.addCallBacks()

        mMsgAdapter = PhoneMsgAdapter(msgList)
        mMsgAdapter.setFooterView(getFootView())
        mLayoutManager = LinearLayoutManager(this@PhoneActivityMain)
        vRecycleView.layoutManager = mLayoutManager
        vRecycleView.adapter = mMsgAdapter

//        CallManager.startAudio()

        setLedLevel(this@PhoneActivityMain, 8)

        setVolume()

        val intentFilter = IntentFilter()
        intentFilter.addAction("android.media.VOLUME_CHANGED_ACTION")
        registerReceiver(volumeReceiver, intentFilter)

        BatteryReceiver.addChangeListener(mBatteryListener)
    }

    private fun getFootView(): View {
        val footer: View = LayoutInflater.from(this).inflate(R.layout.item_phone_msg_right, null)
        val tvUp = footer.findViewById<TextView>(R.id.vTextUp)
        footer.findViewById<TextView>(R.id.vTextBottom).gone()
        footer.findViewById<TextView>(R.id.v_play_btn).gone()
        tvUp.text = TransStringUtil.getPleaseSpeakString(mViewModel.getSelfLang())
            .replace("XXX", TransStringUtil.getLanguageTail(mViewModel.getSelfLang()))
        return footer
    }

    private fun getBreakTimeValue(breakTimeEnum: SettingEnum.BreakTime): Float {
        val breakTimeList = mutableListOf(0.6f, 0.8f, 1.0f, 2.0f, 3.0f) // 单位秒
        return breakTimeList[SettingEnum.BreakTime.values().indexOf(breakTimeEnum)]
    }

    @Subscribe(threadMode = ThreadMode.POSTING)
    fun onHandleEvent(e: Map<String?, Any?>) {
        if (!onCreate) return
        if (!e.containsKey(BleRecordEventName)) return
        val args = e[BleRecordEventName] as Array<*>?
        val data = args!![1] as ByteArray

        val headerLen = 4
        val encodedBuffer = ByteArray(data.size - headerLen)
        System.arraycopy(data, headerLen, encodedBuffer, 0, encodedBuffer.size)
        val decodedBuffer = ByteArray(640)
        opusCodec.decode(encodedBuffer, decodedBuffer, 320)
        sendFrame(decodedBuffer)
//        fileHandle?.write(decodedBuffer)

//        var bytesRead: Int
//        if (fileInputStream.read(buffer).also { bytesRead = it } != -1) {
//            sendFrame(buffer)
//            fileHandle?.write(buffer)
//        } else {
//            fileInputStream.close()
//            fileInputStream = FileInputStream(filePath)
//        }
    }

    @Subscribe(threadMode = ThreadMode.POSTING)
    fun onHandleEvent(audioData: ByteArray) {
        if (!onCreate) return
//        fileHandleRemote?.write(audioData)
    }

    private fun initBleListener() {
        bleListener = object : BleUtil.Listener {
            override fun dispatchEvent(type: BleUtil.BleEventName, perip: RawBlePeripheral?) {
                if(perip !is WT2BlePeripheral) return
                when (type) {

                    BleUtil.BleEventName.BleConnectStandby -> {
                        logD("模式内连接耳机", TAG)

                        //因为homeactivity筛选本机的耳机进行连接，这里只是监听重新连接然后发送命令
//                        resetBleCmds()
                        perip.writeButtonEnabled(true)
                        DeviceTool.connectDevice(this::class.java.name, perip) {
                            lifecycleScope.launch {
//                                CallManager.startAudio()
                                val blePeripherals = mutableListOf(perip)
                                delay(500)
                                BleUtil.shared.sendBleStopCmds(blePeripherals.toTypedArray(), true)
                                delay(500)
                                BleUtil.shared.sendBleStartCmds(blePeripherals.toTypedArray(), true)
                            }
                        }
                    }

                    BleUtil.BleEventName.BleDisconnectedPeripheral -> {
                        if (BleUtil.shared.connectedPeripherals.size == 0) {
                            EventBusUtils.postEvent(ShowDeviceDialogListEvent(TranslateMode.PHONE))
//                            CallManager.stopAudio()
                        }

                        DeviceTool.disconnectDevice(this::class.java.name) {
                            val blePeripherals = BleUtil.shared.connectedPeripherals
                            BleUtil.shared.sendBleStartCmds(blePeripherals.toTypedArray(), true)
                        }
                    }

                    else -> {}
                }
            }

            override fun onBluetoothStateUpdate(state: Int) {}
        }

        BleUtil.shared.addListener(bleListener)

        mBinding.imgHungUp.setOnClickListener {
            CallManager.hangupCall()
//            if (oldCode != TransManager.getLastlyPhoneUseLanguage()) {
//                EventBusUtils.postEvent(SipRegisterEvent())
//            }

            Log.d("qweiqiweriqwerqwer", "222")
            finish()
        }

        mBinding.llLang.setOnClickListener {
            isClicked = true
            startKtxActivity<ChooseLangActivity>()
        }

        mBinding.ivSetting.setOnClickListener {
            isClicked = true
            startTransSettingActivity(
                HomeServiceImplWrap.getUserModel(), mViewModel.modeUtil
            )
        }

        mBinding.vTopDevice.setOnClickListener {
            isClicked = true
        }

        mMsgAdapter.apply {

            addChildClickViewIds(R.id.v_play_btn)

            setOnItemChildClickListener { adapter, view, position ->
                //判断连接的耳机是否大于0
                if (BleUtil.shared.connectedPeripherals.size == 0) {
                    //没有连接设备，弹框
                    EventBusUtils.postEvent(ShowDeviceDialogListEvent(TranslateMode.PHONE))
                    return@setOnItemChildClickListener
                }

                isClicked = true

                if (view is LottiePlayAudioView) {
                    val msgBean = (adapter as PhoneMsgAdapter).data[position]
                    if (view.playState == LottiePlayAudioView.PlayState.Playing) {
                        mViewModel.stopPlayText(msgBean)
                        mViewModel.updateMsgBean(msgBean.apply { isPlaying = false })
                        adapter.notifyDataSetChanged()
                    } else {
                        //没有正在播放
                        SpeakManager.shareInstance().stop() //先停止所有播放
                        mViewModel.playText(msgBean, startCallBack = {
                            mViewModel.updateMsgBean(msgBean.apply { isPlaying = true })
                            adapter.notifyDataSetChanged()
                        }, finishCallback = {
                            lifecycleScope.launch(Dispatchers.Main) {
                                if (msgBean.session == it) {
                                    mViewModel.updateMsgBean(msgBean.apply { isPlaying = false })
                                    adapter.notifyDataSetChanged()
                                }
                            }
                        })
                    }
                }
            }

        }

        mBinding.vRecycleView.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                if (recyclerView.scrollState == RecyclerView.SCROLL_STATE_DRAGGING) {
                    reTimingRemain()
                }
            }
        })

        onBackPressedDispatcher.addCallback(this@PhoneActivityMain,
            object : OnBackPressedCallback(true) {
                override fun handleOnBackPressed() {
                    processBack()
                }
            })

    }

    private fun processBack() {
        DialogFactory.createConfirmCancelDialog(this@PhoneActivityMain,
            content = getString(com.timekettle.upup.comm.R.string.translate_exit_cur_mode_tip),
            confirmCall = {
                CallManager.hangupCall()
//                if (oldCode != TransManager.getLastlyPhoneUseLanguage()) {
//                    EventBusUtils.postEvent(SipRegisterEvent())
//                }

                finish()
            }).show()
    }

    //只要手指按下/手指正在滚动，就触发这个计时
    private fun reTimingRemain() {
        hasNewMsgIn = false
        jobNeedScrollToEnd?.cancel()
        jobNeedScrollToEnd = countDownCoroutines(5, lifecycleScope, onTick = { second ->
            remainSecond = second
            if (second == 0 && hasNewMsgIn) {
                hasNewMsgIn = false
                RecycleViewUtil.scrollToBottom(mBinding.vRecycleView)
            }
        })
    }

    override fun onDestroy() {
        ConController.removeCallStateBackListener(callStatusListener)
        ConController.removeCallMessageBackListener(messageListener)
        BleUtil.shared.removeListener(bleListener)
        mViewModel.removeCallBacks()
        mViewModel.stopCallTimer()
        onCreate = false
        HomeServiceImplWrap.setPhoneMode(false)
        HomeServiceImplWrap.setInMode(false)
        unregisterReceiver(volumeReceiver)
        BatteryReceiver.removeChangeListener(mBatteryListener)
        setLedLevel(this@PhoneActivityMain, 0)

        SensorsUtil.trackEvent(
            SensorsCustomEvent.X1_ExitXXMode.name, hashMapOf(
                "ModelType" to "远程语音"
            )
        )

        val self = TransLanguageTool.getFullLanguageName(mViewModel.getSelfLang())
        SensorsUtil.trackEvent(
            SensorsCustomEvent.X1_LanguageSelectionVoiceCall.name, hashMapOf(
                "SelectLanguage" to self
            )
        )
        super.onDestroy()
    }

    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        when (ev?.action) {
            MotionEvent.ACTION_DOWN -> {
                x = ev.x
                y = ev.y
            }

            MotionEvent.ACTION_UP -> {
                if (ev.x == x && ev.y == y) {
                    exchangePureMode()
                }
            }

            else -> {}
        }
        return super.dispatchTouchEvent(ev)
    }

    override fun onResume() {
        super.onResume()

        mViewModel.enterMode(this@PhoneActivityMain)
        mViewModel.startMode(account)

        val setting = TransManager.getModelSetting(HomeServiceImplWrap.getUserModel())
        mViewModel.liveFontEnum.value = setting.fontSize
        mViewModel.isShowOriginal.value = setting.isShowOriginal
        mViewModel.modeUtil.setBreakTime((getBreakTimeValue(setting.breakTime) * 1000).toInt())
        AiSpeechManager.shareInstance().audioChannels.forEach {
            SpeakManager.shareInstance().update(it.speakerType, setting.ttsSpeed.value)
        }
        mViewModel.modeUtil.setVoice(setting.ttsVoiceIsManSelf, setting.ttsVoiceIsManOther)

        startPureJob()

        BleUtil.shared.setTouchEventCallback { peripheral, _ ->
            // 触控点击的回调
            val blePeripherals = BleUtil.shared.connectedPeripherals
            DeviceTool.asWSeries(peripheral)?.let {
                TransUiUtil.showNowRecordToast(it.role == RawBlePeripheral.Role.Left)
            }
            if (blePeripherals.size == 2) {
                DeviceTool.asWSeries(peripheral)?.let {
                    if (it.macSuffix4 != HomeServiceImplWrap.getFirstDeviceMac()) {
                        HomeServiceImplWrap.saveFirstDeviceMac(it.macSuffix4)
                        lifecycleScope.launch {
                            BleUtil.shared.sendBleStopCmds(blePeripherals.toTypedArray(), true)
                            delay(500)
                            BleUtil.shared.sendBleStartCmds(arrayOf(peripheral), true)
                        }
                    }
                }
            }
        }

        val currentLanguageCode = TransManager.getLastlyPhoneUseLanguage()
        if (this::oldCode.isInitialized && currentLanguageCode != oldCode) {
            oldCode = currentLanguageCode
            mBinding.tvMeetingLang.text = TransLanguageTool.getLanguageNameByCode(currentLanguageCode)
            isRemoved = false
            mViewModel.setSelfLang(currentLanguageCode)
            mMsgAdapter.setFooterView(getFootView())
            showDebugToast("${mViewModel.getSelfLang()} --- ${mViewModel.getOtherLang()}")
            mViewModel.updateLan(currentLanguageCode, mViewModel.getOtherLang())
            CallManager.sendSip(ReqEntity(333, mViewModel.getSelfLang()))
        }
    }

    override fun onPause() {
        super.onPause()
        mViewModel.stopMode()
        mViewModel.exitMode()
        stopPureJob()
    }

    private val runnable = {
        if (!isClicked) {
            if (mBinding.llTop.isVisible) {
                enterPureMode()
            } else {
                exitPureMode()
            }
        } else {
            isClicked = false
        }
    }

    private fun exchangePureMode() {
        mBinding.llTop.removeCallbacks(runnable)
        mBinding.llTop.postDelayed(runnable, 200)
    }

    // 进入纯净模式
    private fun enterPureMode() {
        if (mBinding.llTop.isInvisible || mBinding.vTopDevice.isInvisible) return
        hideControlBar()
        mBinding.llTop.animateAlpha0()
        mBinding.imgHungUp.animateAlpha0()
    }

    // 退出纯净模式
    private fun exitPureMode() {
        showTopBar()
        mBinding.llTop.animateAlpha1()
        mBinding.imgHungUp.animateAlpha1()
        startPureJob()
    }

    //开启纯净模式倒计时
    private fun startPureJob(totalSecond: Int = 5) {
        jobPure?.cancel()
        jobPure = null
        jobPure = countDownCoroutines(totalSecond, lifecycleScope, onTick = { second ->
            if (second == 0) {
                enterPureMode()
                jobPure = null
            }
        })
    }

    private fun stopPureJob() {
        jobPure?.cancel()
        jobPure = null
    }

    private fun showTopBar() {
        val beginValue = -(mBinding.llTop.layoutParams as ConstraintLayout.LayoutParams).height
        val endValue = 0
        val animator = ValueAnimator.ofInt(beginValue, endValue)
        animator.addUpdateListener {
            mBinding.vRecycleView.updateLayoutParams<ConstraintLayout.LayoutParams> {
                topMargin = it.animatedValue as Int
//                mBinding.llContent.requestLayout()
//                mBinding.llContent.invalidate()
//                logD("showtopMargin"+topMargin)
            }
        }
        animator.duration = 300L
        animator.start()
        mBinding.llTop.animate().translationY(-0f).alpha(1f).setDuration(300L).start()
        // 底部的控制按钮，向上移动
        val bottomTargetY = -ConvertUtils.dp2px(50f) // 向下移动100个像素
        val animatorBottom = ValueAnimator.ofInt(bottomTargetY, ConvertUtils.dp2px(6f))
        animatorBottom.apply {
            addUpdateListener {
                //根据animatedValue（就是begin和end的差值）的变化，上边距也跟着变化
                mBinding.imgHungUp.updateLayoutParams<ConstraintLayout.LayoutParams> {
                    bottomMargin = it.animatedValue as Int
                }
            }
            duration = 300L
            start()
        }
    }

    private fun hideControlBar() {
        lifecycleScope.launchWhenResumed {
            val beginValue = 0
            val endValue = -(mBinding.llTop.layoutParams as ConstraintLayout.LayoutParams).height
            val animator = ValueAnimator.ofInt(beginValue, endValue)
            animator.addUpdateListener {
                //根据animatedValue（就是begin和end的差值）的变化，上边距也跟着变化
                mBinding.vRecycleView.updateLayoutParams<ConstraintLayout.LayoutParams> {
                    topMargin = it.animatedValue as Int
                }
            }
            animator.duration = 350L
            animator.start()

            // 底部的控制按钮，向下移动
            val bottomTargetY = -ConvertUtils.dp2px(50f) // 向下移动100个像素
            val animatorBottom = ValueAnimator.ofInt(ConvertUtils.dp2px(6f), bottomTargetY)
            animatorBottom.apply {
                addUpdateListener {
                    //根据animatedValue（就是begin和end的差值）的变化，上边距也跟着变化
                    mBinding.imgHungUp.updateLayoutParams<ConstraintLayout.LayoutParams> {
                        bottomMargin = it.animatedValue as Int
                    }
                }
                duration = 350L
                start()
            }

            mBinding.llTop.animate().translationY(-mBinding.llTop.height.toFloat()).alpha(0f)
                .setDuration(300L).start()
        }

    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        if (keyCode == 24 || keyCode == 25) {
            return super.onKeyDown(keyCode, event)
        }
        return true
    }

    override fun onKeyUp(keyCode: Int, event: KeyEvent?): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            onBackPressed()
        }
        return true
    }

    companion object {
        private const val TAG = "PhoneActivityMain"
        private const val PJSIP_INV_STATE_DISCONNECTED = 6
    }

}