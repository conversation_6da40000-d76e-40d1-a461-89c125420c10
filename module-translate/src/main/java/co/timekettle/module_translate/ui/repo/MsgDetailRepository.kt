package co.timekettle.module_translate.ui.repo

import co.timekettle.module_translate.ui.dao.HistoryDao
import com.timekettle.upup.base.mvvm.m.BaseRepository
import com.timekettle.upup.base.utils.SpUtils
import com.timekettle.upup.comm.bean.MsgBean
import com.timekettle.upup.comm.constant.SpKey
import com.timekettle.upup.comm.dao.MsgBeanDao
import java.util.Date
import javax.inject.Inject

class MsgDetailRepository @Inject constructor(
    private val msgBeanDao: MsgBeanDao
) : BaseRepository() {
    suspend fun countByHistoryDate(date: Date): Int {
        return msgBeanDao.countByHistoryDate(date)
    }

    suspend fun queryMsgDetailByHistoryDate(date: Date): List<MsgBean> {
        return msgBeanDao.queryMsgDetailByHistoryDate(date)
    }

    suspend fun queryMsgDetailByPhoneDate(date: Date): List<MsgBean> {
        return msgBeanDao.queryMsgDetailByPhoneDate(date)
    }

    suspend fun insertOrUpdateMsgBean(msgBean: MsgBean) {
        if (!SpUtils.getBoolean(SpKey.IS_SAVE_RECORD, true)) {
            return
        }
        msgBeanDao.insert(msgBean)
    }
}