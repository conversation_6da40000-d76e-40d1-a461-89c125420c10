package co.timekettle.module_translate.ui.activity

import android.animation.Animator
import android.animation.Animator.AnimatorListener
import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.app.Dialog
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.hardware.usb.UsbManager
import android.media.AudioAttributes
import android.media.AudioFormat
import android.media.AudioRecord
import android.media.AudioTrack
import android.media.MediaRecorder
import android.provider.Settings
import android.text.Layout
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import android.view.animation.AccelerateDecelerateInterpolator
import android.widget.ImageView
import android.widget.TextView
import androidx.activity.OnBackPressedCallback
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import androidx.core.view.updateLayoutParams
import androidx.interpolator.view.animation.FastOutSlowInInterpolator
import androidx.lifecycle.lifecycleScope
import co.timekettle.agora.SpeechManager
import co.timekettle.agora.common.enums.ErrorCode
import co.timekettle.agora.net.model.ShareInfoRes
import co.timekettle.agora.speech.Config
import co.timekettle.agora.speech.RtcState
import co.timekettle.btkit.BleCmdContant
import co.timekettle.btkit.BleUtil
import co.timekettle.btkit.BytesTrans
import co.timekettle.btkit.bean.RawBlePeripheral
import co.timekettle.module_translate.bean.LanguageJsonBeanChild
import co.timekettle.module_translate.bean.MontageEntity
import co.timekettle.module_translate.bean.OfflineUiEvent
import co.timekettle.module_translate.bean.SettingEnum
import co.timekettle.module_translate.bean.SpeechState
import co.timekettle.module_translate.constant.Constant
import co.timekettle.module_translate.constant.IntentKey
import co.timekettle.module_translate.databinding.SpeechActivityMainBinding
import co.timekettle.module_translate.tools.TransLanguageTool
import co.timekettle.module_translate.tools.TransManager
import co.timekettle.module_translate.tools.TransStringUtil
import co.timekettle.module_translate.ui.adapter.MontageAdapter
import co.timekettle.module_translate.ui.ktx.startSpeechSettingActivity
import co.timekettle.module_translate.ui.pop.TranslatePop
import co.timekettle.module_translate.ui.util.QrCodeUtil
import co.timekettle.module_translate.ui.util.RecycleViewUtil
import co.timekettle.module_translate.ui.util.TransUiUtil
import co.timekettle.module_translate.ui.vm.SpeechViewModel
import co.timekettle.opus.OpusCodec
import co.timekettle.speech.AgcProcessor
import co.timekettle.speech.ISpeechConstant
import co.timekettle.speech.TestRecorder
import com.alibaba.android.arouter.facade.annotation.Route
import com.blankj.utilcode.util.ConvertUtils
import com.blankj.utilcode.util.NetworkUtils
import com.timekettle.upup.base.BaseApp
import com.timekettle.upup.base.ktx.animateAlpha0
import com.timekettle.upup.base.ktx.animateAlpha1
import com.timekettle.upup.base.ktx.gone
import com.timekettle.upup.base.ktx.invisible
import com.timekettle.upup.base.ktx.observeLiveData
import com.timekettle.upup.base.ktx.openActivity1
import com.timekettle.upup.base.ktx.visible
import com.timekettle.upup.base.utils.Debouncer
import com.timekettle.upup.base.utils.EventBusRegister
import com.timekettle.upup.base.utils.EventBusUtils
import com.timekettle.upup.base.utils.SpUtils
import com.timekettle.upup.base.utils.countDownCoroutines
import com.timekettle.upup.base.utils.logD
import com.timekettle.upup.base.utils.showToast
import com.timekettle.upup.comm.BuildConfig
import com.timekettle.upup.comm.R
import com.timekettle.upup.comm.base.BaseActivity
import com.timekettle.upup.comm.bean.MsgDirection
import com.timekettle.upup.comm.bean.MsgNetState
import com.timekettle.upup.comm.bean.MsgSpeech
import com.timekettle.upup.comm.constant.LanDirection
import com.timekettle.upup.comm.constant.RouteUrl
import com.timekettle.upup.comm.constant.SpKey
import com.timekettle.upup.comm.constant.TranslateMode
import com.timekettle.upup.comm.dialog.DialogFactory
import com.timekettle.upup.comm.ktx.setLedLevel
import com.timekettle.upup.comm.model.SensorsCustomEvent
import com.timekettle.upup.comm.model.ShowDeviceDialogListEvent
import com.timekettle.upup.comm.receiver.BatteryReceiver
import com.timekettle.upup.comm.service.home.HomeServiceImplWrap
import com.timekettle.upup.comm.tools.DeviceTool
import com.timekettle.upup.comm.utils.SensorsUtil
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.nio.ByteBuffer

/**
 * 演讲翻译页面
 */
@EventBusRegister
@AndroidEntryPoint
@Route(path = RouteUrl.Translate.SpeechActivityMain)
class SpeechActivityMain : BaseActivity<SpeechActivityMainBinding, SpeechViewModel>() {

    override val mViewModel: SpeechViewModel by viewModels()
    private var bleListener: BleUtil.Listener? = null
    private val deBouncer = Debouncer(200)
    private var opusCodec = OpusCodec(16000, 1)
    private var agc: AgcProcessor? = null
    private var audioRecord: AudioRecord? = null
    private var audioTrack: AudioTrack? = null
    private var recordJob: Job? = null
    private var playJob: Job? = null
    private var isRecording = false
    private var isPlaying = false
    private var jobPure: Job? = null
    private var x = 0f
    private var y = 0f
    private var isPeripheralAccess = false
    private var isPlayTts = false
    private var bleFileHandle: TestRecorder? = null
    private var micFileHandle: TestRecorder? = null
    private var playFileHandle: TestRecorder? = null
    private var shareDialog: Dialog? = null
    private var isTouchUp = false
    private var isTouchDown = false
    private var statTime: Long = 0
    private lateinit var upAdapter: MontageAdapter
    private lateinit var downAdapter: MontageAdapter
    private var upList = mutableListOf<MontageEntity>()
    private var downList = mutableListOf<MontageEntity>()
    private var isMic = false
    private var isBaseCharging = false

    private val mBatteryListener = { action: String? ->
        if (action == Intent.ACTION_POWER_CONNECTED && BatteryReceiver.isBaseCharging()) {
            isBaseCharging = true
            window.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        } else if (action == Intent.ACTION_POWER_DISCONNECTED) {
            isBaseCharging = false
            window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        }
    }

    private fun animateDialogToTopRightAndDismiss() {
        val decorView = shareDialog?.window?.decorView ?: return
        decorView.animate()
            .translationX(400f)
            .translationY(-200f)
            .alpha(0f)
            .scaleX(0f)
            .scaleY(0f)
            .setDuration(400)
            .setInterpolator(AccelerateDecelerateInterpolator())
            .withStartAction { shareDialog?.setCancelable(false) }
            .withEndAction { shareDialog?.dismiss() }
            .start()
    }

    override fun initObserve() {
        observeLiveData(mViewModel.liveOfflineUiEvent, ::processOfflineUiEvent)
        observeLiveData(mViewModel.liveIsPause, ::processPauseOrResume)
        observeLiveData(mViewModel.liveSelfOtherLanguage, ::processLanguageUpdate)
        observeLiveData(mViewModel.liveShowTranslation, ::processTranslationShow)
        observeLiveData(mViewModel.liveLan, ::processLan)
        observeLiveData(mViewModel.liveFontEnum, ::processFontEnum)
        observeLiveData(mViewModel.livePlayTTS, ::processPlayTTS)
        observeLiveData(mViewModel.liveRoomInfo, ::processRoomInfo)
        observeLiveData(mViewModel.liveDialogLoading, ::processDialogLoading)
        observeLiveData(mViewModel.liveMsgRecognize, ::processMsgRecognize)
        observeLiveData(mViewModel.liveMsgTranslate, ::processMsgTranslate)
        observeLiveData(mViewModel.liveError, ::processError)
        observeLiveData(mViewModel.liveNetState, ::processNetState)
        observeLiveData(mViewModel.liveAudioState, ::processAudioState)
        observeLiveData(mViewModel.liveCloseRoom, ::processCloseRoom)
    }

    private fun processCloseRoom(data: String) {
        DialogFactory.createKnowDialog(this@SpeechActivityMain,
            content = getString(R.string.trans_speech_end),
            knowCall = {
                finish()
            }).show()
    }

    private fun processAudioState(state: RtcState) {
        mBinding.tvAudioQuality.text = "发送音频: ${state.txAudioBytes} bytes"
    }

    private fun processNetState(state: MsgNetState) {
        mBinding.tvNetworkQuality.text = "网络状态: tx(${state.txQuality}) rx(${state.rxQuality})"
    }

    private fun processError(code: Int) {
        if (code == ErrorCode.USER_OFFLINE_ERROR.code) {
//            showToast("用户离线")
        } else {
            showToast(getString(R.string.trans_try_again))
        }
    }

    private fun processOfflineUiEvent(event: OfflineUiEvent) {
        when (event) {
            OfflineUiEvent.YouCanOpenOffline -> {
                DialogFactory.createConfirmCancelDialog(this@SpeechActivityMain,
                    titleText = BaseApp.context.getString(R.string.common_alert_tip),
                    BaseApp.context.getString(R.string.common_network_error_check_it),
                    confirmText = BaseApp.context.getString(R.string.common_cancel),
                    confirmCall = {},
                    cancelText = BaseApp.context.getString(R.string.common_go_setting),
                    cancelCall = {
                        wifiForResult.launch(Intent(Settings.ACTION_WIFI_SETTINGS))
                    }).show()
            }

            OfflineUiEvent.NetWorkError -> {
                showToast(BaseApp.context.getString(R.string.common_network_error_check_it))
            }
        }
    }

    private val wifiForResult =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result: ActivityResult ->
            mViewModel.checkWifiAgain()
        }

    private fun processLan(code: String) {
        if (translateText.isEmpty()) {
            mBinding.tvDownTip.text = TransStringUtil.getSpeechString(code)
        }

        SpeechManager.subTtsByCode(code)
    }

    private fun processLanguageUpdate(value: Array<LanguageJsonBeanChild>) {
        if (recognizeText.isEmpty()) {
            mBinding.tvUpTip.text = TransStringUtil.getSpeechString(value[0].code)
        }

        mBinding.tvSelf.text = value[0].language
        if (value.size <= 2) {
            mBinding.tvOther.text = value[1].language
        } else {
            val others = value.drop(1).toTypedArray()
            var text = ""
            for (i in others.indices) {
                text += if (i == others.size - 1) others[i].language else "${others[i].language}/"
            }
            mBinding.tvOther.text = text
        }
    }

    private fun processRoomInfo(info: ShareInfoRes) {
        SpUtils.put(SpKey.ROOM_NUMBER, info.room_no)
        SpUtils.put(SpKey.ROOM_QR_CODE, info.join_url)
        mViewModel.updateRoomNo(info.room_no)
        showRoomInfo()
    }

    private fun processDialogLoading(loading: Boolean) {
        if (loading) shareDialog?.findViewById<ConstraintLayout>(R.id.vLoadingTv)?.visible()
        else shareDialog?.findViewById<ConstraintLayout>(R.id.vLoadingTv)?.gone()
    }

    private fun showRoomInfo() {
        val url = SpUtils.getString(SpKey.ROOM_QR_CODE, "")
        QrCodeUtil.generateQRCode(url.ifEmpty { "invalid url" })?.let {
            if (shareDialog?.isShowing == true) {
                val id = SpUtils.getString(SpKey.ROOM_NUMBER, "")
                val phoneId = shareDialog?.findViewById<TextView>(R.id.tv_id)
                val pcId = shareDialog?.findViewById<TextView>(R.id.tv_pc_id)
                val qrCode = shareDialog?.findViewById<ImageView>(R.id.img_code)
                phoneId?.text = "ID: $id"
                pcId?.text = "ID: $id"
                qrCode?.setImageBitmap(it)
                return
            }
            shareDialog = DialogFactory.createShareDialog(
                this@SpeechActivityMain,
                QrCodeUtil.generateQRCode(SpUtils.getString(SpKey.ROOM_QR_CODE, "").ifEmpty { "invalid url" })!!,
                SpUtils.getString(SpKey.ROOM_NUMBER, ""),
                confirmCall = {
                    animateDialogToTopRightAndDismiss()
                    mBinding.llLottieQrcode.visible()
                    mBinding.llLottieQrcode.playAnimation()
                }
            )
            mBinding.llLottieQrcode.addAnimatorListener(object : AnimatorListener{
                override fun onAnimationStart(animation: Animator) { }

                override fun onAnimationEnd(animation: Animator) {
                    mBinding.llLottieQrcode.gone()
                    mBinding.vTitleQuestionIcon.visible()
                }

                override fun onAnimationCancel(animation: Animator) { }

                override fun onAnimationRepeat(animation: Animator) { }
            })
            mBinding.vTitleQuestionIcon.invisible()
            shareDialog?.setOnKeyListener { _, keyCode, event ->
                if (keyCode == KeyEvent.KEYCODE_BACK && event.action == MotionEvent.ACTION_UP) {
                    animateDialogToTopRightAndDismiss()
                    mBinding.llLottieQrcode.visible()
                    mBinding.llLottieQrcode.playAnimation()
                    return@setOnKeyListener true
                } else false
            }
            shareDialog?.show()
        }
    }

    private fun processPauseOrResume(isPause: Boolean) {
        lifecycleScope.launchWhenStarted {
            if (isPause) { // 如果当前是暂停/停止状态
                stopPureJob()
            }
        }
    }

    private var recognizeText = ""
    private var translateText = ""
    private var recognizeTempText = ""
    private var translateTempText = ""
    private var recognizeSessionId = ""
    private var translateSessionId = ""
    private var lastUpText = ""
    private var lastDownText = ""
    private var upEntity: MontageEntity? = null
    private var downEntity: MontageEntity? = null

    private fun processMsgRecognize(speech: MsgSpeech) {
        if (mBinding.tvUpTip.isVisible) {
            mBinding.tvUpTip.gone()
        }

        if (recognizeSessionId != speech.sessionId) { //解决如断网导致的没有COMPLETED，句子丢失的问题
            recognizeSessionId = speech.sessionId
            recognizeTempText = buildString {
                append(recognizeText)
            }
        }

        recognizeText = if (speech.locale.contains("zh")) {
            buildString {
                append(recognizeTempText)
                append(speech.text)
            }
        } else {
            buildString {
                append(recognizeTempText)
                append(" ")
                append(speech.text)
            }
        }

        if (upEntity == null) {
            upEntity = MontageEntity()
            upList.add(upEntity!!)

            if (upList.size > 1 && lastUpText.isNotEmpty()) { // 上条记录去掉最后一行
                val montageEntity = upList[upList.size - 2]
                val index = montageEntity.text.lastIndexOf(lastUpText)
                if (index > 0) {
                    montageEntity.text = montageEntity.text.substring(0, index)
                }
            }
        }

        mBinding.tvUp.text = recognizeText
        upEntity?.direction = MsgDirection.Right
        upEntity?.text = recognizeText
        upEntity?.currentText = speech.text
        upEntity?.isLast = speech.state == SpeechState.COMPLETED.state
        upAdapter.notifyDataSetChanged()

        if (!isTouchUp) {
            RecycleViewUtil.scrollToBottom(mBinding.rvUp)
        }

        if (speech.state == SpeechState.COMPLETED.state) {
            recognizeTempText = buildString {
                append(recognizeText)
            }

            if (recognizeTempText.length > MAX_LENGTH) {
                logD("准备换行 up length ${recognizeTempText.length}", TAG)
                upEntity?.isFinish = true
                upEntity = null

                val layout: Layout = mBinding.tvUp.layout
                val lastLine = layout.lineCount - 1
                val start = layout.getLineStart(lastLine)
                val end = layout.getLineEnd(lastLine)
                val lastLineText: String = mBinding.tvUp.text.subSequence(start, end).toString()
                logD("最后一行内容 up: $lastLineText", TAG)
                recognizeText = buildString {// 下一句加上上一句的最后一行
                    append(lastLineText)
                }
                lastUpText = buildString {
                    append(lastLineText)
                }
            }
        }
    }

    private fun processMsgTranslate(speech: MsgSpeech) {
        if (mBinding.tvDownTip.isVisible) {
            mBinding.tvDownTip.gone()
        }

        if (translateSessionId != speech.sessionId) { //解决如断网导致的没有COMPLETED，句子丢失的问题
            translateSessionId = speech.sessionId
            translateTempText = buildString {
                append(translateText)
            }
        }

        translateText = if (speech.locale.contains("zh")) {
            buildString {
                append(translateTempText)
                append(speech.text)
            }
        } else {
            buildString {
                append(translateTempText)
                append(" ")
                append(speech.text)
            }
        }

        if (downEntity == null) {
            downEntity = MontageEntity()
            downList.add(downEntity!!)

            if (downList.size > 1 && lastDownText.isNotEmpty()) {
                val montageEntity = downList[downList.size - 2]
                val index = montageEntity.text.lastIndexOf(lastDownText)
                if (index > 0) {
                    montageEntity.text = montageEntity.text.substring(0, index)
                }
            }
        }

        mBinding.tvDown.text = translateText
        downEntity?.direction = MsgDirection.Left
        downEntity?.text = translateText
        downAdapter.notifyDataSetChanged()

        if (!isTouchDown) {
            RecycleViewUtil.scrollToBottom(mBinding.rvDown)
        }

        if (speech.state == SpeechState.COMPLETED.state) {
            translateTempText = buildString {
                append(translateText)
            }

            if (translateTempText.length > MAX_LENGTH) {
                logD("准备换行 down length ${translateTempText.length}", TAG)
                downEntity?.isFinish = true
                downEntity = null

                val layout: Layout = mBinding.tvDown.layout
                val lastLine = layout.lineCount - 1
                val start = layout.getLineStart(lastLine)
                val end = layout.getLineEnd(lastLine)
                val lastLineText: String = mBinding.tvDown.text.subSequence(start, end).toString()
                logD( "最后一行内容 down: $lastLineText", TAG)
                translateText = buildString {
                    append(lastLineText)
                }
                lastDownText = buildString {
                    append(lastLineText)
                }
            }
        }
    }

    private fun processTranslationShow(isShow: Boolean) {
        if (isShow) {
            mBinding.rvDown.visible()
            mBinding.viewLine.visible()
            RecycleViewUtil.scrollToBottom(mBinding.rvDown)
        } else {
            mBinding.rvDown.gone()
            mBinding.viewLine.gone()
            mBinding.tvDownTip.gone()
            SpeechManager.subTtsByCode("")
        }

        RecycleViewUtil.scrollToBottom(mBinding.rvUp)
    }

    //更新字体大小
    private fun processFontEnum(fontSize: SettingEnum.FontSize) {
        val fontSp = fontSize.fontValue
        mBinding.tvUp.textSize = (fontSp + 6).toFloat()
        mBinding.tvDown.textSize = (fontSp + 6).toFloat()
        mBinding.tvUpTip.textSize = (fontSp + 6).toFloat()
        mBinding.tvDownTip.textSize = (fontSp + 6).toFloat()
        upAdapter.setFont(fontSp + 6)
        downAdapter.setFont(fontSp + 6)
    }

    //更新是否播放tts
    private fun processPlayTTS(isPlay: Boolean) {
        isPlayTts = isPlay
    }

    override fun initRequestData() {
        lifecycleScope.launchWhenResumed {
            mBinding.ivPauseButton.performClick()
        }

        mViewModel.getShareInfo()
        showRoomInfo()

        SensorsUtil.trackEvent(
            SensorsCustomEvent.X1_EnterXXMode.name, hashMapOf(
                "ModelType" to "演讲翻译"
            )
        )

        statTime = System.currentTimeMillis()
    }

    override fun SpeechActivityMainBinding.initView() {
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)

        HomeServiceImplWrap.setPhoneMode(true)
        HomeServiceImplWrap.setInMode(true)
        mViewModel.setProductAndMode()
        NetworkUtils.registerNetworkStatusChangedListener(netWorkListener)

        if (BuildConfig.DEBUG) {
            layoutNetState.visibility = View.VISIBLE
        }

        vTitleTv.text = getString(R.string.home_speech_translation)
        vTitleQuestionIcon.setImageResource(co.timekettle.module_translate.R.mipmap.mode_navbar_icon_qrcode)
        layoutSpeechChoose.visible()

        upAdapter = MontageAdapter(upList)
        upAdapter.setFooterView(getEmptySpeakView())
        rvUp.adapter = upAdapter

        downAdapter = MontageAdapter(downList)
        downAdapter.setFooterView(getEmptySpeakView())
        rvDown.adapter = downAdapter

        agc = AgcProcessor(ISpeechConstant.DefaultSampleRate, 320, 3.0f)
        if (SpUtils.getBoolean(SpKey.IS_RECORD_AUDIO_OPEN, false)) {
            bleFileHandle = TestRecorder(this@SpeechActivityMain, "TK_Record", null,  "speech-ble-record", true)
            micFileHandle = TestRecorder(this@SpeechActivityMain, "TK_Record", null,  "speech-mic-record", true)
            playFileHandle = TestRecorder(this@SpeechActivityMain, "TK_Record", null,  "speech-play-track", true)
        }

        startRecord()
        startPlay()
        registerReceiver()

        mViewModel.enterMode(Config.Builder(BaseApp.context).build())
        mViewModel.startMode()

        if (isUsbMicrophone(this@SpeechActivityMain)) {
            isPeripheralAccess = true
            showToast(getString(R.string.trans_usb_device_connected))
        }

        tvOther.post {
            tvOther.requestFocus()  // 强制获取焦点
            tvOther.isSelected = true
        }
    }

    private val netWorkListener = object : NetworkUtils.OnNetworkStatusChangedListener {
        override fun onDisconnected() {
            showToast(getString(R.string.common_network_disconnected))
        }

        override fun onConnected(networkType: NetworkUtils.NetworkType?) {
        }
    }

    private fun getEmptySpeakView(): View {
        return LayoutInflater.from(this)
            .inflate(co.timekettle.module_translate.R.layout.layout_footer_meet, null)
    }

    @SuppressLint("MissingPermission")
    private fun startRecord() {
        val minBufferSize = AudioRecord.getMinBufferSize(
            16000,
            AudioFormat.CHANNEL_IN_MONO,
            AudioFormat.ENCODING_PCM_16BIT
        )

        audioRecord = AudioRecord(
            MediaRecorder.AudioSource.MIC, 16000,
            AudioFormat.CHANNEL_IN_MONO, AudioFormat.ENCODING_PCM_16BIT, minBufferSize
        ).apply {
            startRecording()
        }

        val audioData = ByteArray(minBufferSize)

        isRecording = true
        recordJob = Job()

        lifecycleScope.launch(Dispatchers.IO + recordJob!!) {
            while (isRecording) {
                audioRecord?.let {
                    val readSize = it.read(audioData, 0, minBufferSize)
                    if (readSize < 0) {
                        return@launch
                    }

                    if (isPeripheralAccess && mViewModel.liveIsPause.value == false && !isBaseCharging) {
                        SpeechManager.pushData(audioData, 16000, 1)
                        micFileHandle?.write(audioData)
                    } else {
                        delay(20)
                    }
                }
            }
        }
    }

    private fun startPlay() {
        val bufferSize = AudioTrack.getMinBufferSize(
            16000,
            AudioFormat.CHANNEL_OUT_MONO,
            AudioFormat.ENCODING_PCM_16BIT
        )

        audioTrack = AudioTrack.Builder()
            .setAudioAttributes(
                AudioAttributes.Builder()
                    .setUsage(AudioAttributes.USAGE_MEDIA)
                    .setContentType(AudioAttributes.CONTENT_TYPE_MUSIC)
                    .build()
            )
            .setAudioFormat(
                AudioFormat.Builder()
                    .setSampleRate(16000)
                    .setChannelMask(AudioFormat.CHANNEL_OUT_MONO)
                    .setEncoding(AudioFormat.ENCODING_PCM_16BIT)
                    .build()
            )
            .setTransferMode(AudioTrack.MODE_STREAM)
            .setBufferSizeInBytes(bufferSize)
            .build()

//        audioTrack?.let {
//            if (it.state != AudioTrack.STATE_UNINITIALIZED && it.playState != AudioTrack.PLAYSTATE_PLAYING) {
//                it.play()
//            }
//        }

        audioTrack?.play()

        isPlaying = true
        playJob = Job()
        lifecycleScope.launch(Dispatchers.IO + playJob!!) {
            while (isPlaying) {
                val lengthInByte = 160 * 2
                val frame = ByteBuffer.allocateDirect(lengthInByte)
                SpeechManager.pullAudioFrame(frame, lengthInByte)
                val data = ByteArray(frame.remaining())
                frame[data, 0, data.size]
                if (isPlayTts && mViewModel.liveIsPause.value == false && !isBaseCharging) {
                    try { //java.lang.IllegalStateException: Unable to retrieve AudioTrack pointer for write()
                        audioTrack?.write(data, 0, lengthInByte)
                        playFileHandle?.write(data)
                    } catch (e: Exception) {
                        logD("播放异常:${e.message.toString()}", TAG)
                        e.printStackTrace()
                    }
                } else {
                    delay(20)
                }
            }
        }
    }

    private fun processBack() {
        DialogFactory.createConfirmCancelDialog(this@SpeechActivityMain,
            content = getString(R.string.trans_exit_speech),
            confirmCall = {
                finish()
            }).show()
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun initListener() {
        mBinding.vBackIv.setOnClickListener {
            onBackPressed()
        }

        mBinding.vTitleTv.setOnClickListener {
            onBackPressed()
        }

        mBinding.vTitleQuestionIcon.setOnClickListener {
            stopPureJob()
            mViewModel.getShareInfo()
            showRoomInfo()
        }

        mBinding.vSettingIcon.setOnClickListener {
            stopPureJob()
            startSpeechSettingActivity(mViewModel.mTranslateMode)
        }

        mBinding.layoutSpeechChoose.setOnClickListener {
            val param = IntentKey.LanguageType to LanDirection.Mine
            val param1 = IntentKey.LanguageNeedRequest to true
            openActivity1<ChooseLangWithoutVoiceActivity>(param, param1)
        }

        onBackPressedDispatcher.addCallback(this@SpeechActivityMain,
            object : OnBackPressedCallback(true) {
                override fun handleOnBackPressed() {
                    processBack()
                }
            })

        val transPop = TranslatePop(this@SpeechActivityMain, mViewModel)
        mBinding.imgTrans.setOnClickListener {
            transPop.showPopupWindow()
        }

        mBinding.ivContinueButton.setOnClickListener {
            if (!isPeripheralAccess && BleUtil.shared.connectedPeripherals.size == 0) {
                //没有连接设备，弹框
                EventBusUtils.postEvent(ShowDeviceDialogListEvent(TranslateMode.SPEECH))
                return@setOnClickListener
            }

            setLedLevel(this, 8)
            mBinding.ivContinueButton.gone()
            mBinding.ivPauseButton.visible()
            mViewModel.pauseOrResume()
            lifecycleScope.launchWhenStarted {
                startPureJob { enterPureMode() }
            }
        }

        mBinding.ivPauseButton.setOnClickListener {
            setLedLevel(this, 0)
            mBinding.ivPauseButton.gone()
            mBinding.ivContinueButton.visible()
            mViewModel.pauseOrResume()
        }

        mBinding.root.setOnClickListener { exchangePureMode() }

        mBinding.rvUp.setOnTouchListener { _, motionEvent ->

            when (motionEvent?.action) {
                MotionEvent.ACTION_DOWN -> {
                    isTouchUp = true
                    x = motionEvent.x
                    y = motionEvent.y
                }

                MotionEvent.ACTION_UP -> {
                    isTouchUp = false
                    if (motionEvent.x == x && motionEvent.y == y) {
                        exchangePureMode()
                    }
                }

                MotionEvent.ACTION_CANCEL -> {
                    isTouchUp = false
                }

                else -> {}
            }

            false
        }

        mBinding.rvDown.setOnTouchListener { _, motionEvent ->
            when (motionEvent?.action) {
                MotionEvent.ACTION_DOWN -> {
                    isTouchDown = true
                    x = motionEvent.x
                    y = motionEvent.y
                }

                MotionEvent.ACTION_UP -> {
                    isTouchDown = false
                    if (motionEvent.x == x && motionEvent.y == y) {
                        exchangePureMode()
                    }
                }

                MotionEvent.ACTION_CANCEL -> {
                    isTouchDown = false
                }

                else -> {}
            }

            false
        }

        bleListener = object : BleUtil.Listener {
            override fun dispatchEvent(type: BleUtil.BleEventName, perip: RawBlePeripheral?) {
                when (type) {

                    BleUtil.BleEventName.BleConnectStandby -> {
                        logD("模式内连接耳机", TAG)

                        BleUtil.shared.sendCmdToQueue(perip, BleCmdContant.AppCmdId.EnableButton)

                        DeviceTool.connectDevice(this::class.java.name,perip) {
                            lifecycleScope.launch {
                                val blePeripherals = mutableListOf(perip)
                                delay(500)
                                BleUtil.shared.sendBleStopCmds(blePeripherals.toTypedArray(), true)
                                delay(500)
                                BleUtil.shared.sendBleStartCmds(blePeripherals.toTypedArray(), true)
                            }
                        }
                    }

                    BleUtil.BleEventName.BleDisconnectedPeripheral -> {
                        if (!isPeripheralAccess && BleUtil.shared.connectedPeripherals.size == 0) {
                            EventBusUtils.postEvent(ShowDeviceDialogListEvent(TranslateMode.SPEECH))
                            if (mViewModel.liveIsPause.value == false) {
                                mBinding.ivPauseButton.performClick()
                                exitPureMode()
                            }
                        }

                        DeviceTool.disconnectDevice(this::class.java.name) {
                            val blePeripherals = BleUtil.shared.connectedPeripherals
                            BleUtil.shared.sendBleStartCmds(blePeripherals.toTypedArray(), true)
                        }
                    }

                    else -> {}
                }
            }

            override fun onBluetoothStateUpdate(state: Int) {}
        }

        BleUtil.shared.addListener(bleListener)
    }

    private fun startPureJob(totalSecond: Int = 5, todo: () -> Unit? = { enterPureMode() }) {
        jobPure?.cancel()
        jobPure = null
        jobPure = countDownCoroutines(totalSecond, lifecycleScope, onTick = { second ->
            if (second == 0) {
                enterPureMode()
                jobPure = null
            }
        })
    }

    private fun stopPureJob() {
        jobPure?.cancel()
        jobPure = null
    }

    private fun enterPureMode() {
        if (!mBinding.ivPauseButton.isVisible && !mBinding.ivContinueButton.isVisible) return
        hideControlBar()
        mBinding.ivPauseButton.animateAlpha0()
        mBinding.ivContinueButton.animateAlpha0()
        mBinding.llTopControl.animateAlpha0()
        mBinding.imgTrans.animateAlpha0()
    }

    /**
     * needAutoEnter：是否需要再退出之后，又自动进入纯净模式
     */
    private fun exitPureMode(needAutoEnter: Boolean = false) {
        showTopBar()
        mBinding.ivPauseButton.animateAlpha1()
        mBinding.ivContinueButton.animateAlpha1()
        mBinding.llTopControl.animateAlpha1()
        mBinding.imgTrans.animateAlpha1()
        if (needAutoEnter) {
            startPureJob(5)
        }
    }

    private fun showTopBar() {
        val beginValue = -(mBinding.llTopControl.layoutParams as ConstraintLayout.LayoutParams).height
        val endValue = 0
        val animator = ValueAnimator.ofInt(beginValue, endValue)
        animator.addUpdateListener {
            mBinding.llContent.updateLayoutParams<ConstraintLayout.LayoutParams> {
                topMargin = it.animatedValue as Int
            }
        }

        animator.duration = 300L
        animator.start()
        mBinding.llTopControl.animate().translationY(-0f).alpha(1f).setDuration(300L).start()
        // 底部的控制按钮，向上移动
        val bottomTargetY = -ConvertUtils.dp2px(50f) // 向下移动100个像素
        val animatorBottom = ValueAnimator.ofInt(bottomTargetY, ConvertUtils.dp2px(6f))
        animatorBottom.apply {
            addUpdateListener {
                mBinding.ivPauseButton.updateLayoutParams<ConstraintLayout.LayoutParams> {
                    bottomMargin = it.animatedValue as Int
                }
                mBinding.ivContinueButton.updateLayoutParams<ConstraintLayout.LayoutParams> {
                    bottomMargin = it.animatedValue as Int
                }
                mBinding.imgTrans.updateLayoutParams<ConstraintLayout.LayoutParams> {
                    bottomMargin = it.animatedValue as Int
                }
            }
            duration = 300L
            start()
        }
    }

    private fun hideControlBar() {
        lifecycleScope.launchWhenResumed {
            val beginValue = 0
            val endValue =
                -(mBinding.llTopControl.layoutParams as ConstraintLayout.LayoutParams).height
            val animator = ValueAnimator.ofInt(beginValue, endValue)
            animator.addUpdateListener {
                //根据animatedValue（就是begin和end的差值）的变化，上边距也跟着变化
                mBinding.llContent.updateLayoutParams<ConstraintLayout.LayoutParams> {
                    topMargin = it.animatedValue as Int
                }
            }
            animator.duration = 350L
            animator.start()

            // 底部的控制按钮，向下移动
            val bottomTargetY = -ConvertUtils.dp2px(50f) // 向下移动100个像素
            val animatorBottom = ValueAnimator.ofInt(ConvertUtils.dp2px(6f), bottomTargetY)
            animatorBottom.apply {
                addUpdateListener {
                    mBinding.ivPauseButton.updateLayoutParams<ConstraintLayout.LayoutParams> {
                        bottomMargin = it.animatedValue as Int
                    }
                    mBinding.ivContinueButton.updateLayoutParams<ConstraintLayout.LayoutParams> {
                        bottomMargin = it.animatedValue as Int
                    }
                    mBinding.imgTrans.updateLayoutParams<ConstraintLayout.LayoutParams> {
                        bottomMargin = it.animatedValue as Int
                    }
                }
                duration = 350L
                start()
            }

            mBinding.llTopControl.animate().translationY(-mBinding.llTopControl.height.toFloat())
                .alpha(0f).setDuration(300L).start()
        }
    }

    private fun exchangePureMode() {
        deBouncer.debounce {
            if (mBinding.ivPauseButton.isVisible || mBinding.ivContinueButton.isVisible) {
                enterPureMode()
            } else {
                exitPureMode(mViewModel.liveIsPause.value == false)
            }
        }
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        if (keyCode == 24 || keyCode == 25) {
            return super.onKeyDown(keyCode, event)
        }

        return true
    }

    override fun onKeyUp(keyCode: Int, event: KeyEvent?): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            onBackPressed()
        }

        return true
    }

    override fun onResume() {
        super.onResume()

//        mViewModel.enterMode(Config.Builder(BaseApp.context).build())
//        mViewModel.startMode()
        val setting = TransManager.getModelSetting(HomeServiceImplWrap.getUserModel())
        mViewModel.liveFontEnum.value = setting.fontSize
        mViewModel.livePlayTTS.value = setting.isOpenTts

        startPureJob(5)
        mViewModel.updateLanguage()

        RecycleViewUtil.scrollToBottom(mBinding.rvUp)
        RecycleViewUtil.scrollToBottom(mBinding.rvDown)

        BleUtil.shared.setTouchEventCallback { peripheral, _ ->
            if (mViewModel.liveIsPause.value == true || isPeripheralAccess) {
                return@setTouchEventCallback
            }

            // 触控点击的回调
            val blePeripherals = BleUtil.shared.connectedPeripherals
            DeviceTool.asWSeries(peripheral)?.let {
                TransUiUtil.showNowRecordToast(it.role == RawBlePeripheral.Role.Left)
            }
            if (blePeripherals.size == 2) {
                DeviceTool.asWSeries(peripheral)?.let {
                    if (it.macSuffix4 != HomeServiceImplWrap.getFirstDeviceMac()) {
                        HomeServiceImplWrap.saveFirstDeviceMac(it.macSuffix4)
                        lifecycleScope.launch {
                            BleUtil.shared.sendBleStopCmds(blePeripherals.toTypedArray(), true)
                            delay(500)
                            BleUtil.shared.sendBleStartCmds(arrayOf(peripheral), true)
                        }
                    }
                }
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.POSTING)
    fun onHandleEvent(e: Map<String?, Any?>) {
        if (!e.containsKey(Constant.BleRecordEventName)) return
        val args = e[Constant.BleRecordEventName] as Array<*>?
        val data = args!![1] as ByteArray

        val headerLen = 4
        val encodedBuffer = ByteArray(data.size - headerLen)
        System.arraycopy(data, headerLen, encodedBuffer, 0, encodedBuffer.size)
        val decodedBuffer = ByteArray(640)
        opusCodec.decode(encodedBuffer, decodedBuffer, 320)

//        if (!isPeripheralAccess && mViewModel.liveIsPause.value == false) {
//            SpeechManager.pushData(decodedBuffer, 16000, 1)
//        }

        val input = BytesTrans.getInstance().Bytes2Shorts(decodedBuffer)
        val output = ShortArray(input.size)
        agc?.let {
            it.processAgc(output, input)
            val bOutput = BytesTrans.getInstance().Shorts2Bytes(output)
            if (!isPeripheralAccess && mViewModel.liveIsPause.value == false && !isBaseCharging) {
                SpeechManager.pushData(bOutput, 16000, 1)
                bleFileHandle?.write(bOutput)
            }
        }

    }

    private fun registerReceiver() {
        val filter = IntentFilter()
        filter.addAction(UsbManager.ACTION_USB_DEVICE_ATTACHED)
        filter.addAction(UsbManager.ACTION_USB_DEVICE_DETACHED)
        this.registerReceiver(receiver, filter)

        BatteryReceiver.addChangeListener(mBatteryListener)
    }

    private fun unRegisterReceiver() {
        unregisterReceiver(receiver)

        BatteryReceiver.removeChangeListener(mBatteryListener)
    }

    fun isUsbMicrophone(context: Context): Boolean {
        val usbManager = context.getSystemService(Context.USB_SERVICE) as UsbManager
        val deviceList = usbManager.deviceList

        for (device in deviceList.values) {
            for (i in 0 until device.interfaceCount) {
                val usbInterface = device.getInterface(i)
                // 使用常量值 1 来表示音频类
                if (usbInterface.interfaceClass == 1) {
                    // 进一步检查接口子类是否为音频控制(1)或音频流(2)
                    if (usbInterface.interfaceSubclass == 1 || usbInterface.interfaceSubclass == 2) {
                        isMic = true
                        return true
                    }
                }
            }
        }

        isMic = false
        return false
    }

    // TODO: fix me 后续要测试只能播放不能录制的情况
    private val receiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            val action = intent.action
            if (UsbManager.ACTION_USB_DEVICE_ATTACHED == action) {
                if (isUsbMicrophone(this@SpeechActivityMain)) {
                    isPeripheralAccess = true
                    showToast(getString(R.string.trans_usb_device_connected))
                    if (mViewModel.liveIsPause.value == false) {
                        mBinding.ivPauseButton.performClick()
                        exitPureMode()
                    }
                }
            } else if (UsbManager.ACTION_USB_DEVICE_DETACHED == action) {
                if (isMic) {
                    isMic = false
                    isPeripheralAccess = false
                    showToast(getString(R.string.trans_usb_device_disconnected))
                    if (mViewModel.liveIsPause.value == false) {
                        mBinding.ivPauseButton.performClick()
                        exitPureMode()
                    }
                }
            }
        }
    }

    private fun stopRecord() {
        recordJob?.cancel()
        recordJob = null
        isRecording = false

        if (audioRecord != null) {
            audioRecord?.stop()
            audioRecord?.release()
            audioRecord = null
        }
    }

    private fun stopPlay() {
        playJob?.cancel()
        playJob = null
        isPlaying = false

        if (audioTrack != null) {
            audioTrack?.stop()
            audioTrack?.release()
            audioTrack = null
        }
    }

    override fun onPause() {
        super.onPause()
//        mViewModel.stopMode()
//        mViewModel.exitMode()
        BleUtil.shared.setTouchEventCallback(null)
    }

    override fun onDestroy() {
        mViewModel.stopMode()
        mViewModel.exitMode()
        bleFileHandle?.close()
        micFileHandle?.close()
        playFileHandle?.close()

        NetworkUtils.unregisterNetworkStatusChangedListener(netWorkListener)
        HomeServiceImplWrap.setPhoneMode(false)
        HomeServiceImplWrap.setInMode(false)
        BleUtil.shared.removeListener(bleListener)
        stopRecord()
        stopPlay()
        unRegisterReceiver()
        setLedLevel(this, 0)

        SensorsUtil.trackEvent(
            SensorsCustomEvent.X1_ExitXXMode.name, hashMapOf(
                "ModelType" to "演讲翻译"
            )
        )

        SensorsUtil.trackEvent(
            SensorsCustomEvent.X1_PresentionTranstionDuration.name, hashMapOf(
                "ModeDuration" to (System.currentTimeMillis() - statTime) / 1000
            )
        )

        val self = TransLanguageTool.getFullLanguageName(mViewModel.selfCode)
        val other = TransLanguageTool.getFullLanguageName(mViewModel.otherCode)
        SensorsUtil.trackEvent(
            SensorsCustomEvent.X1_LanguageSelectionPresentation.name, hashMapOf(
                "SelectLanguagePresenter" to self,
                "SelectLanguageAttendee" to other
            )
        )
        super.onDestroy()
    }

    companion object {
        const val TAG = "SpeechActivityMain"
        const val MAX_LENGTH = 500
    }

}