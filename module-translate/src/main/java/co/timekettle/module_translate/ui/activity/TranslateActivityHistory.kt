package co.timekettle.module_translate.ui.activity

import android.content.Intent
import android.view.LayoutInflater
import androidx.activity.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import co.timekettle.module_translate.R
import co.timekettle.module_translate.constant.IntentKey
import co.timekettle.module_translate.databinding.TranslateActivityHistoryBinding
import co.timekettle.module_translate.ui.adapter.HistoryQueryAdapter
import co.timekettle.module_translate.ui.ktx.putParcelableExtra
import com.alibaba.android.arouter.facade.annotation.Route
import com.gyf.immersionbar.BarHide
import com.gyf.immersionbar.ImmersionBar
import com.kyleduo.switchbutton.SwitchButton
import com.timekettle.upup.base.ktx.setClickEffect
import com.timekettle.upup.base.mvvm.vm.EmptyViewModel
import com.timekettle.upup.base.utils.SpUtils
import com.timekettle.upup.base.utils.logD
import com.timekettle.upup.comm.base.BaseActivity
import com.timekettle.upup.comm.constant.RouteUrl
import com.timekettle.upup.comm.constant.SpKey
import com.timekettle.upup.comm.constant.TranslateMode
import com.timekettle.upup.comm.model.SensorsCustomEvent
import com.timekettle.upup.comm.utils.SensorsUtil
import dagger.hilt.android.AndroidEntryPoint

// 翻译记录页面
@Route(path = RouteUrl.Translate.History)
@AndroidEntryPoint
class TranslateActivityHistory :
    BaseActivity<TranslateActivityHistoryBinding, EmptyViewModel>() {

    private var mItems = mutableListOf<TranslateMode>().apply {
        add(TranslateMode.SIMUL)
        add(TranslateMode.SPEAKER)
        add(TranslateMode.KEYPAD)
        if (SpUtils.getBoolean(SpKey.IS_SHOW_VIDEO, false)) {
            add(TranslateMode.VIDEO)
        }
        add(TranslateMode.SPEECH)
        add(TranslateMode.PHONE)
        add(TranslateMode.MEETING)
    }

    private var mAdapter = HistoryQueryAdapter(mItems)

    override val mViewModel: EmptyViewModel by viewModels()

    override fun TranslateActivityHistoryBinding.initView() {
        ImmersionBar.with(this@TranslateActivityHistory).hideBar(BarHide.FLAG_HIDE_NAVIGATION_BAR).init()
        mTitleTv?.text = getString(com.timekettle.upup.comm.R.string.setting_trans_record)
        mBinding.vRecycleView.layoutManager = LinearLayoutManager(this@TranslateActivityHistory)
        val header = LayoutInflater.from(this@TranslateActivityHistory)
            .inflate(R.layout.item_history_header, mBinding.vRecycleView, false)
        val btn = header.findViewById<SwitchButton>(R.id.switch_save_record)
        btn.isChecked = SpUtils.getBoolean(SpKey.IS_SAVE_RECORD, true)
        btn.setOnCheckedChangeListener { _, isChecked ->
            SpUtils.putBoolean(SpKey.IS_SAVE_RECORD, isChecked)
        }
        mAdapter.setHeaderView(header)

        mBinding.vRecycleView.apply {
            adapter = mAdapter
        }

        setClickEffect(mBinding.ivDelete, mBinding.ivExport)
    }

    override fun initObserve() {
    }

    override fun initRequestData() {
    }

    override fun initListener() {
        mAdapter.setOnItemClickListener { adapter, _, position ->
            when (val transMode = (adapter as HistoryQueryAdapter).data[position]) {
                TranslateMode.SPEAKER, TranslateMode.SIMUL, TranslateMode.KEYPAD, TranslateMode.VIDEO -> {
                    val intent = Intent(
                        this@TranslateActivityHistory,
                        TranslateActivityHistoryQuery::class.java
                    ).apply {
                        putParcelableExtra(IntentKey.TranslateMode, transMode)
                    }
                    startActivity(intent)

                    SensorsUtil.trackEvent(
                        SensorsCustomEvent.X1_ViewTranslationHistory.name, hashMapOf(
                            "ModelType" to when (transMode) {
                                TranslateMode.SIMUL -> "双人对话"
                                TranslateMode.SPEAKER -> "旁听翻译"
                                TranslateMode.KEYPAD -> "手持翻译"
                                TranslateMode.VIDEO -> "音视频翻译"
                                else -> ""
                            }
                        )
                    )
                }

                TranslateMode.PHONE->{
                    val intent = Intent(
                        this@TranslateActivityHistory,
                        CallRecordActivity::class.java
                    )
                    startActivity(intent)

                    SensorsUtil.trackEvent(
                        SensorsCustomEvent.X1_ViewTranslationHistory.name, hashMapOf(
                            "ModelType" to "远程语音"
                        )
                    )
                }

                TranslateMode.MEETING -> {
                    val intent = Intent(
                        this@TranslateActivityHistory,
                        MeetingActivityHistory::class.java
                    )
                    startActivity(intent)

                    SensorsUtil.trackEvent(
                        SensorsCustomEvent.X1_ViewTranslationHistory.name, hashMapOf(
                            "ModelType" to "多人会议"
                        )
                    )
                }

                TranslateMode.SPEECH -> {
                    val intent = Intent(
                        this@TranslateActivityHistory,
                        SpeechActivityHistory::class.java
                    ).apply {
                        putParcelableExtra(IntentKey.TranslateMode, transMode)
                    }
                    startActivity(intent)

                    SensorsUtil.trackEvent(
                        SensorsCustomEvent.X1_ViewTranslationHistory.name, hashMapOf(
                            "ModelType" to "演讲翻译"
                        )
                    )
                }

                else -> {}
            }
        }
    }

    companion object {
        const val TAG: String = "TranslateActivityHistory";
    }

}