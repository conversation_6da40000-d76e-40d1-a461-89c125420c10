package co.timekettle.module_translate.ui.activity

import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.media.AudioManager
import android.os.Build
import android.os.VibrationEffect
import android.os.Vibrator
import android.provider.Settings
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.ForegroundColorSpan
import android.util.Log
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import android.widget.ScrollView
import android.widget.TextView
import androidx.activity.OnBackPressedCallback
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import androidx.core.view.updateLayoutParams
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import co.timekettle.btkit.BleCmdContant
import co.timekettle.btkit.BleUtil
import co.timekettle.btkit.bean.RawBlePeripheral
import co.timekettle.btkit.bean.WT2BlePeripheral
import co.timekettle.module_translate.bean.MontageEntity
import co.timekettle.module_translate.bean.OfflineUiEvent
import co.timekettle.module_translate.bean.SettingEnum
import co.timekettle.module_translate.bean.SpeechState
import co.timekettle.module_translate.databinding.InterviewActivityMainBinding
import co.timekettle.module_translate.tools.TransLanguageTool
import co.timekettle.module_translate.tools.TransManager
import co.timekettle.module_translate.tools.TransStringUtil
import co.timekettle.module_translate.ui.adapter.MontageAdapter
import co.timekettle.module_translate.ui.adapter.MsgAdapterKey
import co.timekettle.module_translate.ui.ktx.startTransSettingActivity
import co.timekettle.module_translate.ui.util.RecycleViewUtil
import co.timekettle.module_translate.ui.util.TransUiUtil
import co.timekettle.module_translate.ui.vm.InterviewViewModel
import co.timekettle.speech.AiSpeechManager
import co.timekettle.speech.ISpeechConstant
import co.timekettle.speech.OfflineManager
import co.timekettle.speech.RecognizeManager
import co.timekettle.speech.SpeakManager
import com.alibaba.android.arouter.facade.annotation.Autowired
import com.alibaba.android.arouter.facade.annotation.Route
import com.blankj.utilcode.util.ConvertUtils
import com.blankj.utilcode.util.NetworkUtils
import com.blankj.utilcode.util.ToastUtils
import com.timekettle.upup.base.BaseApp
import com.timekettle.upup.base.ktx.animateAlpha0
import com.timekettle.upup.base.ktx.animateAlpha1
import com.timekettle.upup.base.ktx.gone
import com.timekettle.upup.base.ktx.observeLiveData
import com.timekettle.upup.base.ktx.visible
import com.timekettle.upup.base.utils.Debouncer
import com.timekettle.upup.base.utils.EventBusUtils
import com.timekettle.upup.base.utils.SpUtils
import com.timekettle.upup.base.utils.countDownCoroutines
import com.timekettle.upup.base.utils.logD
import com.timekettle.upup.base.utils.showToast
import com.timekettle.upup.comm.BuildConfig
import com.timekettle.upup.comm.R
import com.timekettle.upup.comm.base.BaseActivity
import com.timekettle.upup.comm.bean.MsgBean
import com.timekettle.upup.comm.bean.MsgDirection
import com.timekettle.upup.comm.bean.MsgInterview
import com.timekettle.upup.comm.bean.MsgListWrapper
import com.timekettle.upup.comm.bean.MsgWrapper
import com.timekettle.upup.comm.constant.RouteUrl
import com.timekettle.upup.comm.constant.SpKey
import com.timekettle.upup.comm.constant.TranslateMode
import com.timekettle.upup.comm.dialog.DialogFactory
import com.timekettle.upup.comm.ktx.isBleLeft
import com.timekettle.upup.comm.ktx.setLedLevel
import com.timekettle.upup.comm.model.SensorsCustomEvent
import com.timekettle.upup.comm.model.ShowDeviceDialogListEvent
import com.timekettle.upup.comm.receiver.BatteryReceiver
import com.timekettle.upup.comm.service.home.HomeServiceImplWrap
import com.timekettle.upup.comm.tools.DeviceTool
import com.timekettle.upup.comm.tools.DeviceTool.macIsLeftHeadset
import com.timekettle.upup.comm.utils.SensorsUtil
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.libpag.PAGFile
import java.math.BigDecimal

/**
 * 旁听翻译页面
 */
@AndroidEntryPoint
@Route(path = RouteUrl.Translate.InterviewActivityMain)
class InterviewActivityMain : BaseActivity<InterviewActivityMainBinding, InterviewViewModel>() {

    @JvmField
    @Autowired(name = "offlineSelfCode")
    var offlineSelfCode: String = ""

    @JvmField
    @Autowired(name = "offlineOtherCode")
    var offlineOtherCode: String = ""

    private lateinit var kMsgAdapter: MsgAdapterKey
    private var msgList = mutableListOf<MsgBean>()
    private var msgKList = mutableListOf<MsgBean>()
    private var mLayoutManager: LinearLayoutManager? = null
    private var jobNeedScrollToEnd: Job? = null  //  是否需要滚动到底部
    private var jobPure: Job? = null  //  是否需要纯净
    private var statTime: Long = 0
    override val mViewModel: InterviewViewModel by viewModels()
    private val deBouncer = Debouncer(200) // 设置延迟时间为 600 毫秒

    private var bleListener: BleUtil.Listener? = null
    private var x = 0f
    private var y = 0f
    private var isStart: Boolean = true
    private var recentVolume: Int = 0
    private val adjustVolume: Int = 9
    private var currentVolume = -1

    private var isTouchUp = false
    private var isTouchDown = false
    private lateinit var upAdapter: MontageAdapter
    private lateinit var downAdapter: MontageAdapter
    private var upList = mutableListOf<MontageEntity>()
    private var downList = mutableListOf<MontageEntity>()

    private val mBatteryListener = { action: String? ->
        if (action == Intent.ACTION_POWER_CONNECTED && BatteryReceiver.isBaseCharging()) {
            window.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        } else if (action == Intent.ACTION_POWER_DISCONNECTED) {
            window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        }
    }

    override fun initObserve() {
        observeLiveData(mViewModel.liveOfflineUiEvent, ::processOfflineUiEvent)
        observeLiveData(mViewModel.liveKMsgList, ::processKMsgList)
        observeLiveData(mViewModel.liveMsgBegin, ::processMsgBegin)
        observeLiveData(mViewModel.liveMsgEnd, ::processMsgEnd)
        observeLiveData(mViewModel.liveMsgSpeakStart, ::processMsgSpeakStart)
        observeLiveData(mViewModel.liveMsgSpeakStop, ::processMsgSpeakStop)
        observeLiveData(mViewModel.liveMsgRecognize, ::processMsgRecognize)
        observeLiveData(mViewModel.liveMsgTranslate, ::processMsgTranslate)
        observeLiveData(mViewModel.liveIsPause, ::processPauseOrResume)
        observeLiveData(mViewModel.liveShowPleaseSpeak, ::processShowPleaseSpeak)
        observeLiveData(mViewModel.liveFontEnum, ::processFontEnum)
        observeLiveData(mViewModel.isShowOriginal, ::processShowOriginal)
    }

    private fun processShowOriginal(isShowOriginal: Boolean) {
        if (isShowOriginal) {
            mBinding.rvDown.visible()
            mBinding.viewLine.visible()
        } else {
            mBinding.rvDown.gone()
            mBinding.viewLine.gone()
            mBinding.tvDownTip.gone()
        }
    }

    //更新字体大小
    private fun processFontEnum(fontSize: SettingEnum.FontSize) {
        val fontSp = fontSize.fontValue
        mBinding.tvUpTip.textSize = (fontSp + 6).toFloat()
        mBinding.tvDownTip.textSize = (fontSp + 6).toFloat()
        upAdapter.setFont(fontSp + 6)
        downAdapter.setFont(fontSp + 6)
    }

    override fun initRequestData() {
        lifecycleScope.launchWhenResumed {
            mBinding.imgSwitch.performClick()
        }

        SensorsUtil.trackEvent(
            SensorsCustomEvent.X1_EnterXXMode.name, hashMapOf(
                "ModelType" to "旁听模式"
            )
        )

        statTime = System.currentTimeMillis()
    }

    override fun InterviewActivityMainBinding.initView() {
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON) // 防止息屏
        HomeServiceImplWrap.setInMode(true)

        if (SpUtils.getBoolean(SpKey.HIDE_GUIDE, false)) {
            mBinding.layoutTip.gone()
            mBinding.pagTip.stop()
        }

        //初始化离线，否则用不了离线功能
        OfflineManager.getInstance().init(this@InterviewActivityMain)
        NetworkUtils.registerNetworkStatusChangedListener(netWorkListener)
        setSpeechNetWork()

        mViewModel.setProductAndMode(offlineSelfCode, offlineOtherCode)

        vTitleBar.vTitleTv.text = getString(R.string.main_side)
        tvUpTip.text = TransStringUtil.getSpeakString(mViewModel.selfCode)
        tvDownTip.text = TransStringUtil.getSpeakString(mViewModel.otherCode)

        tvTip.post {
            val layoutParams = tvTip.layoutParams as ConstraintLayout.LayoutParams
            layoutParams.rightMargin = -tvTip.width / 2 + ConvertUtils.dp2px(26f)
            tvTip.layoutParams = layoutParams
        }

        vTitleBar.vTitleQuestionIcon.gone()
        vSpeakTitleBar.vSettingIcon.gone()
        vSpeakTitleBar.vTitleQuestionIcon.gone()

        upAdapter = MontageAdapter(upList)
        upAdapter.setFooterView(getEmptySpeakView())
        rvUp.adapter = upAdapter

        downAdapter = MontageAdapter(downList)
        downAdapter.setFooterView(getEmptySpeakView())
        rvDown.adapter = downAdapter

        kMsgAdapter = MsgAdapterKey()
        mLayoutManager = LinearLayoutManager(this@InterviewActivityMain)

        headSetRecycleView.adapter = kMsgAdapter

        setLedLevel(this@InterviewActivityMain, 8)

        mBinding.pagRecording.apply {
            composition = PAGFile.Load(assets, "ani_listen_earbus_recording_bmp.pag")
            setRepeatCount(0)
        }

        mBinding.pagTip.apply {
            composition = PAGFile.Load(assets, "ani_listen_guide_hand_bmp.pag")
            setRepeatCount(0)
            play()
        }

        volumeControlStream = AudioManager.STREAM_MUSIC

        val audioManager = getSystemService(Context.AUDIO_SERVICE) as AudioManager
        val currentVolume = audioManager.getStreamVolume(AudioManager.STREAM_MUSIC)
        recentVolume = currentVolume

        audioManager.setStreamVolume(AudioManager.STREAM_MUSIC, adjustVolume, 0)

        val volumeKeyFilter = IntentFilter()
        volumeKeyFilter.addAction("android.media.VOLUME_CHANGED_ACTION")
        registerReceiver(volumeKeyReceiver, volumeKeyFilter)

        BatteryReceiver.addChangeListener(mBatteryListener)
    }

    private val volumeKeyReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent) {
            if (intent.action == "android.media.VOLUME_CHANGED_ACTION") {
                val volume = intent.getIntExtra("android.media.EXTRA_VOLUME_STREAM_VALUE", 0)
                if (currentVolume != volume) {
                    if (volume > currentVolume && volume > adjustVolume) {
                        ToastUtils.showShort(getString(R.string.trans_interview_volume))
                    }
                    currentVolume = volume
                }
            }
        }
    }

    private fun processBack() {
        DialogFactory.createConfirmCancelDialog(this@InterviewActivityMain,
            content = getString(R.string.translate_exit_cur_mode_tip),
            confirmCall = {
                finish()
            }).show()
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun initListener() {
        mBinding.vTitleBar.vBackIv.setOnClickListener {
            finish()
        }

        mBinding.vTitleBar.vTitleTv.setOnClickListener {
            finish()
        }

        onBackPressedDispatcher.addCallback(this@InterviewActivityMain,
            object : OnBackPressedCallback(true) {
                override fun handleOnBackPressed() {
                    processBack()
                }
            })

        mBinding.imgSwitch.setOnClickListener {
            if (isStart) {
                mBinding.imgSwitch.setImageResource(co.timekettle.module_translate.R.mipmap.mode_but_icon_pause)
                lifecycleScope.launchWhenStarted {
                    startPureJob { enterPureMode() }
                }
            } else {
                mBinding.imgSwitch.setImageResource(co.timekettle.module_translate.R.mipmap.mode_but_icon_start)
            }

            mViewModel.pauseOrResume()

            isStart = !isStart
        }

        mBinding.tvLeft.setOnClickListener {
            if (!mViewModel.isLeft) {
                SensorsUtil.trackEvent(SensorsCustomEvent.X1_ListenAndPlayClickListen.name, null)
            }

            showLeft()
        }

        mBinding.tvRight.setOnClickListener {
            if (!mViewModel.isRight) {
                SensorsUtil.trackEvent(SensorsCustomEvent.X1_ListenAndPlayClickSpeak.name, null)
            }

            showRight()
        }

        mBinding.btnNoMore.setOnClickListener {
            SpUtils.put(SpKey.HIDE_GUIDE, true)
            mBinding.layoutTip.gone()
            mBinding.pagTip.stop()
        }

        mBinding.vSpeakTitleBar.vBackIv.setOnClickListener {
            finish()
        }

        mBinding.vTitleBar.vSettingIcon.setOnClickListener {
            if (mViewModel.isLeft) {
                //跳转设置界面
                stopPureJob()
                startTransSettingActivity(mViewModel.mTranslateMode, mViewModel.modeUtil)
            }
        }

        mBinding.root.setOnClickListener { exchangePureMode() }

        mBinding.headSetRecycleView.setOnTouchListener { _, motionEvent ->
            if (motionEvent?.action == MotionEvent.ACTION_DOWN) {
                exchangePureMode()
            }

            false
        }

        mBinding.rvUp.setOnTouchListener { _, motionEvent ->

            when (motionEvent?.action) {
                MotionEvent.ACTION_DOWN -> {
                    isTouchUp = true
                    x = motionEvent.x
                    y = motionEvent.y
                }

                MotionEvent.ACTION_UP -> {
                    isTouchUp = false
                    if (motionEvent.x == x && motionEvent.y == y) {
                        exchangePureMode()
                    }
                }

                MotionEvent.ACTION_CANCEL -> {
                    isTouchUp = false
                }

                else -> {}
            }

            false
        }

        mBinding.rvDown.setOnTouchListener { _, motionEvent ->
            when (motionEvent?.action) {
                MotionEvent.ACTION_DOWN -> {
                    isTouchDown = true
                    x = motionEvent.x
                    y = motionEvent.y
                }

                MotionEvent.ACTION_UP -> {
                    isTouchDown = false
                    if (motionEvent.x == x && motionEvent.y == y) {
                        exchangePureMode()
                    }
                }

                MotionEvent.ACTION_CANCEL -> {
                    isTouchDown = false
                }

                else -> {}
            }

            false
        }

        bleListener = object : BleUtil.Listener {
            override fun dispatchEvent(type: BleUtil.BleEventName, perip: RawBlePeripheral?) {
                when (type) {
                    BleUtil.BleEventName.BleConnectStandby -> {
                        logD("模式内连接耳机", TAG)
                        if( perip !is WT2BlePeripheral) return
                        //因为homeactivity筛选本机的耳机进行连接，这里只是监听重新连接然后发送命令
//                        resetBleCmds()
                        AiSpeechManager.shareInstance().isSynthesizeDisabled = false
                        perip.writeButtonEnabled(true)
                        lifecycleScope.launch {
                            val blePeripherals = mutableListOf(perip)
                            delay(500)
                            BleUtil.shared.sendBleStopCmds(blePeripherals.toTypedArray(), true)
                            delay(500)
                            BleUtil.shared.sendBleStartCmds(blePeripherals.toTypedArray(), true)
                        }
                    }

                    BleUtil.BleEventName.BleDisconnectedPeripheral -> {
                        if (BleUtil.shared.connectedPeripherals.size == 0) {
                            AiSpeechManager.shareInstance().isSynthesizeDisabled = true
                            toInterview()
                        }
                        if(BleUtil.shared.connectedPeripherals.size == 1){
                            val blePeripherals = BleUtil.shared.connectedPeripherals
                            if(mViewModel.isRight)  // 耳机在说话
                                mViewModel.enableHeadsetChannel(blePeripherals[0].isBleLeft())
//                            BleUtil.shared.sendBleStartCmds(blePeripherals.toTypedArray(), true)
                        }

                    }

                    else -> {}
                }
            }

            override fun onBluetoothStateUpdate(state: Int) {}
        }

        BleUtil.shared.addListener(bleListener)

        mBinding.layoutTip.apply {
            setOnClickListener {
                gone()
                mBinding.pagTip.stop()
            }
        }

    }

    private fun toSpeak() {
        if (!mBinding.layoutTab.isVisible) {
            exitPureMode(mViewModel.liveIsPause.value == false)
        }
        showRight()
    }

    private fun toInterview() {
        if (!mBinding.layoutTab.isVisible) {
            exitPureMode(mViewModel.liveIsPause.value == false)
        }
        showLeft()
    }

    @SuppressLint("UseCompatLoadingForDrawables")
    private fun showLeft() {
        if (!mViewModel.isLeft) {
            mBinding.tvLeft.setTextColor(getColor(R.color.color_43a5ff))
            mBinding.tvLeft.background =
                getDrawable(co.timekettle.module_translate.R.mipmap.mode_but_tab_switch)
            mBinding.tvRight.setTextColor(getColor(R.color.white))
            mBinding.tvRight.background = null
            mBinding.layoutHeadset.gone()
            mBinding.pagRecording.stop()
            kMsgAdapter.setFooterView(getEmptySpeakView())
            mViewModel.clearKList()

            if (mViewModel.liveIsPause.value == false) {
                AiSpeechManager.shareInstance().stopAllWorker() //先停止所有播放
                mViewModel.enableHostChannel()
            }

            mViewModel.isLeft = true
            mViewModel.isRight = false
        }
    }

    @SuppressLint("UseCompatLoadingForDrawables")
    private fun showRight() {
        if (!mViewModel.isRight) {

            if (BleUtil.shared.connectedPeripherals.size == 0) {
                EventBusUtils.postEvent(
                    ShowDeviceDialogListEvent(
                        TranslateMode.SPEAKER,
                        connectOneToDo = ::toSpeak
                    )
                )
                return
            }

            mBinding.tvRight.setTextColor(getColor(R.color.color_43a5ff))
            mBinding.tvRight.background =
                getDrawable(co.timekettle.module_translate.R.mipmap.mode_but_tab_switch)
            mBinding.tvLeft.setTextColor(getColor(R.color.white))
            mBinding.tvLeft.background = null
            mBinding.layoutHeadset.visible()
            mBinding.pagRecording.play()
            kMsgAdapter.setFooterView(getPleaseSpeakView())

            if (mViewModel.liveIsPause.value == false) {
                AiSpeechManager.shareInstance().stopAllWorker() //先停止所有播放
                val bles = BleUtil.shared.connectedPeripherals
                if (bles.size == 2) {
                    mViewModel.enableHeadsetChannel(macIsLeftHeadset(HomeServiceImplWrap.getFirstDeviceMac()))
                } else if (bles.size == 1) {
                    mViewModel.enableHeadsetChannel(bles[0].isBleLeft())
                }
            }

            mViewModel.isRight = true
            mViewModel.isLeft = false
        }
    }

    private fun exchangePureMode() {
        deBouncer.debounce {
            if (mBinding.layoutTab.isVisible) {
                enterPureMode()
            } else {
                exitPureMode(mViewModel.liveIsPause.value == false)
            }
        }
    }

    private val netWorkListener = object : NetworkUtils.OnNetworkStatusChangedListener {
        override fun onDisconnected() {
            if (!mViewModel.modeUtil.isSupportOffline()) {
                showToast(getString(R.string.common_network_disconnected))
            }
            setSpeechNetWork()
        }

        override fun onConnected(networkType: NetworkUtils.NetworkType?) {
            setSpeechNetWork()
        }
    }

    private var recognizeText = ""
    private var translateText = ""
    private var recognizeTempText = ""
    private var translateTempText = ""
    private var upEntity: MontageEntity? = null
    private var downEntity: MontageEntity? = null

    private fun processMsgSpeakStop(text: String) {
        try {
            upList.lastOrNull { it.text.contains(text) }?.let {
                it.showSpeak = false
                upAdapter.notifyDataSetChanged()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun processMsgSpeakStart(text: String) {
        try {
            upList.lastOrNull { it.text.contains(text) }?.let {
                it.showSpeak = true
                it.speakText = text
                upAdapter.notifyDataSetChanged()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun processMsgBegin(msgWrapper: MsgWrapper) {
        msgWrapper.data?.let {
            if (it.direction == MsgDirection.Right) {
                recognizeText += " ..."

                if (downEntity == null) {
                    downEntity = MontageEntity()
                    downEntity?.direction = MsgDirection.Right
                    downEntity?.let{ entity ->
                        downList.add(entity)
                    }
                }

                downEntity?.text = recognizeText
                downAdapter.notifyDataSetChanged()
            }
        }
    }

    private fun processMsgEnd(msgWrapper: MsgWrapper) {
        msgWrapper.data?.let {
            if (it.direction == MsgDirection.Right) {
                recognizeText = recognizeText.replace(" ...", "")

                downEntity?.text = recognizeText
                downAdapter.notifyDataSetChanged()
            }
        }
    }

    private fun processMsgRecognize(msg: MsgInterview) {
        msg.text?.let {
            if (mBinding.tvDownTip.isVisible) {
                mBinding.tvDownTip.gone()
            }

            if (msg.direction == MsgDirection.Right) {
                recognizeText = if (msg.code != null && msg.code!!.contains("zh")) {
                    buildString {
                        append(recognizeTempText)
                        append(it)
                    }
                } else {
                    buildString {
                        append(recognizeTempText)
                        append(" ")
                        append(it)
                    }
                }

                if (downEntity == null) { //这一步可以省略，因为在MsgBegin的时候创建了，此处只是为了保险起见加的
                    downEntity = MontageEntity()
                    downEntity?.direction = MsgDirection.Right
                    downList.add(downEntity!!)
                }

                downEntity?.text = recognizeText
                downEntity?.currentText = it
                downEntity?.isLast = msg.isLast
                downAdapter.notifyDataSetChanged()

                if (!isTouchDown) {
                    RecycleViewUtil.scrollToBottom(mBinding.rvDown)
                }

                if (msg.isLast) {
                    recognizeTempText = buildString {
                        append(recognizeText)
                    }

                    if (recognizeTempText.length > MAX_LENGTH) {
                        logD("准备换行 mic down length ${recognizeTempText.length}", TAG)
                        downEntity?.isFinish = true
                        downEntity = null

                        recognizeTempText = buildString {
                            append("")
                        }
                    }
                }
            } else if (msg.direction == MsgDirection.Left) {
                translateText = if (msg.code != null && msg.code!!.contains("zh")) {
                    buildString {
                        append(translateTempText)
                        append(it)
                    }
                } else {
                    buildString {
                        append(translateTempText)
                        append(" ")
                        append(it)
                    }
                }

                if (upEntity == null) {
                    upEntity = MontageEntity()
                    upEntity?.direction = MsgDirection.Left
                    upList.add(upEntity!!)
                }

                upEntity?.text = translateText
                upEntity?.isLast = msg.isLast
                upAdapter.notifyDataSetChanged()

                if (!isTouchUp) {
                    RecycleViewUtil.scrollToBottom(mBinding.rvUp)
                }

                if (msg.isLast) {
                    translateTempText = buildString {
                        append(translateText)
                    }

                    if (translateTempText.length > MAX_LENGTH) {
                        logD("准备换行 headset up length ${translateTempText.length}", TAG)
                        upEntity?.isFinish = true
                        upEntity = null

                        translateTempText = buildString {
                            append("")
                        }
                    }
                }
            }
        }
    }

    private fun processMsgTranslate(msg: MsgInterview) {
        msg.text?.let {
            if (mBinding.tvUpTip.isVisible) {
                mBinding.tvUpTip.gone()
            }

            if (msg.direction == MsgDirection.Right) {
                translateText = if (msg.code != null && msg.code!!.contains("zh")) {
                    buildString {
                        append(translateTempText)
                        append(it)
                    }
                } else {
                    buildString {
                        append(translateTempText)
                        append(" ")
                        append(it)
                    }
                }

                if (upEntity == null) {
                    upEntity = MontageEntity()
                    upEntity?.direction = MsgDirection.Left
                    upList.add(upEntity!!)
                }

                upEntity?.text = translateText
                upEntity?.isLast = msg.isLast
                upAdapter.notifyDataSetChanged()

                if (!isTouchUp) {
                    RecycleViewUtil.scrollToBottom(mBinding.rvUp)
                }

                if (msg.isLast) {
                    translateTempText = buildString {
                        append(translateText)
                    }

                    if (translateTempText.length > MAX_LENGTH) {
                        logD("准备换行 mic up length ${translateTempText.length}", TAG)
                        upEntity?.isFinish = true
                        upEntity = null

                        translateTempText = buildString {
                            append("")
                        }
                    }
                }
            } else if (msg.direction == MsgDirection.Left) {
                recognizeText = if (msg.code != null && msg.code!!.contains("zh")) {
                    buildString {
                        append(recognizeTempText)
                        append(it)
                    }
                } else {
                    buildString {
                        append(recognizeTempText)
                        append(" ")
                        append(it)
                    }
                }

                if (downEntity == null) {
                    downEntity = MontageEntity()
                    downEntity?.direction = MsgDirection.Right
                    downList.add(downEntity!!)
                }

                downEntity?.text = recognizeText
                downEntity?.currentText = it
                downEntity?.isLast = msg.isLast
                downAdapter.notifyDataSetChanged()

                if (!isTouchDown) {
                    RecycleViewUtil.scrollToBottom(mBinding.rvDown)
                }

                if (msg.isLast) {
                    recognizeTempText = buildString {
                        append(recognizeText)
                    }

                    if (recognizeTempText.length > MAX_LENGTH) {
                        logD("准备换行 headset down length ${recognizeTempText.length}", TAG)
                        downEntity?.isFinish = true
                        downEntity = null

                        recognizeTempText = buildString {
                            append("")
                        }
                    }
                }
            }
        }
    }

    private fun processKMsgList(listWrapper: MsgListWrapper) {
        runOnUiThread {
            msgKList.clear()
            msgKList.addAll(listWrapper.data)
            kMsgAdapter.setList(msgKList)
            mBinding.headSetRecycleView.scrollToPosition(kMsgAdapter.itemCount - 1)
        }
    }

    private fun processPauseOrResume(isPause: Boolean) {
        lifecycleScope.launchWhenStarted {
            if (isPause) { // 如果当前是暂停/停止状态
                stopPureJob()
                jobNeedScrollToEnd?.cancel()
            } else {
//                startPureJob(5) { enterPureMode() }
            }
        }
    }

    //开启纯净模式倒计时
    private fun startPureJob(totalSecond: Int = 5, todo: () -> Unit? = { enterPureMode() }) {
        jobPure?.cancel()
        jobPure = null
        jobPure = countDownCoroutines(totalSecond, lifecycleScope, onTick = { second ->
            if (second == 0) {
                enterPureMode()
                jobPure = null
            }
        })
    }

    private fun stopPureJob() {
        jobPure?.cancel()
        jobPure = null
    }

    // 进入纯净模式
    private fun enterPureMode() {
        if (!mBinding.layoutTab.isVisible) return
        hideControlBar()
        mBinding.layoutTab.animateAlpha0()
        mBinding.llTopControl.animateAlpha0()
    }

    // 退出纯净模式
    // needAutoEnter：是否需要再退出之后，又自动进入纯净模式
    private fun exitPureMode(needAutoEnter: Boolean = false) {
        showTopBar()
        mBinding.layoutTab.animateAlpha1()
        mBinding.llTopControl.animateAlpha1()
        if (needAutoEnter) {
            startPureJob(5)
        }
    }

    private fun showTopBar() {
        val beginValue =
            -(mBinding.llTopControl.layoutParams as ConstraintLayout.LayoutParams).height

        val endValue = 0
        val animator = ValueAnimator.ofInt(beginValue, endValue)
        animator.addUpdateListener {
            mBinding.llContent.updateLayoutParams<ConstraintLayout.LayoutParams> {
                topMargin = it.animatedValue as Int
            }
        }

        animator.duration = 300L
        animator.start()
        mBinding.llTopControl.animate().translationY(-0f).alpha(1f).setDuration(300L).start()
        // 底部的控制按钮，向上移动
        val bottomTargetY = -ConvertUtils.dp2px(50f) // 向下移动100个像素
        val animatorBottom = ValueAnimator.ofInt(bottomTargetY, ConvertUtils.dp2px(6f))
        animatorBottom.apply {
            addUpdateListener {
                //根据animatedValue（就是begin和end的差值）的变化，上边距也跟着变化
                mBinding.layoutTab.updateLayoutParams<ConstraintLayout.LayoutParams> {
                    bottomMargin = it.animatedValue as Int
                }
            }
            duration = 300L
            start()
        }
    }

    private fun hideControlBar() {
        lifecycleScope.launchWhenResumed {
            val beginValue = 0
            val endValue =
                -(mBinding.llTopControl.layoutParams as ConstraintLayout.LayoutParams).height
            val animator = ValueAnimator.ofInt(beginValue, endValue)
            animator.addUpdateListener {
                //根据animatedValue（就是begin和end的差值）的变化，上边距也跟着变化
                mBinding.llContent.updateLayoutParams<ConstraintLayout.LayoutParams> {
                    topMargin = it.animatedValue as Int
                }
            }
            animator.duration = 350L
            animator.start()

            // 底部的控制按钮，向下移动
            val bottomTargetY = -ConvertUtils.dp2px(50f) // 向下移动100个像素
            val animatorBottom = ValueAnimator.ofInt(ConvertUtils.dp2px(6f), bottomTargetY)
            animatorBottom.apply {
                addUpdateListener {
                    //根据animatedValue（就是begin和end的差值）的变化，上边距也跟着变化

                    mBinding.layoutTab.updateLayoutParams<ConstraintLayout.LayoutParams> {
                        bottomMargin = it.animatedValue as Int
                    }
                }
                duration = 350L
                start()
            }

            mBinding.llTopControl.animate().translationY(-mBinding.llTopControl.height.toFloat())
                .alpha(0f).setDuration(300L).start()
        }

    }

    //    //显示请说话的
    private fun processShowPleaseSpeak(isShow: Boolean) {
        lifecycleScope.launchWhenStarted {
            if (isShow) {
                delay(50)
                if (mLayoutManager?.findLastVisibleItemPosition() == msgList.size - 1 && msgList.isNotEmpty()) {
                    if (mViewModel.liveIsPause.value == true) return@launchWhenStarted
                }
            } else {
                kMsgAdapter.setFooterView(getEmptySpeakView())
            }
        }
    }

    private fun getPleaseSpeakView(): View {
        val footer: View = LayoutInflater.from(this)
            .inflate(co.timekettle.module_translate.R.layout.layout_footer_interview, null)
        val tvTab = footer.findViewById<TextView>(co.timekettle.module_translate.R.id.tv_tap)
        tvTab.text = getString(R.string.translate_speak_with_headset)
        return footer
    }

    private fun getEmptySpeakView(): View {
        return LayoutInflater.from(this)
            .inflate(co.timekettle.module_translate.R.layout.layout_footer_empty, null)
    }

    private fun setSpeechNetWork() {
        AiSpeechManager.shareInstance().isOnlyOffline = !NetworkUtils.isConnected() ||
                TransManager.getModelSetting(HomeServiceImplWrap.getUserModel()).isOpenOffline
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            if (isKeyCode && event!!.eventTime - event.downTime > 0) {
                isKeyCode = false

                vibrate()

                if (mBinding.layoutTip.isVisible) {
                    mBinding.layoutTip.gone()
                    mBinding.pagTip.stop()
                }

                if (!mBinding.layoutTab.isVisible) {
                    exitPureMode(mViewModel.liveIsPause.value == false)
                }

                if (mViewModel.isLeft) {
                    if (BleUtil.shared.connectedPeripherals.size == 0) {
                        EventBusUtils.postEvent(
                            ShowDeviceDialogListEvent(
                                TranslateMode.SPEAKER,
                                connectOneToDo = ::toSpeak
                            )
                        )
                    } else {
                        showRight()
                    }
                } else if (mViewModel.isRight) {
                    showLeft()
                }
            }
        } else if (keyCode == 24 || keyCode == 25) {
            return super.onKeyDown(keyCode, event)
        }

        return true
    }

    override fun onKeyUp(keyCode: Int, event: KeyEvent?): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            if (!isKeyCode) {
                isKeyCode = true
            } else {
                if (mBinding.layoutTip.isVisible) {
                    mBinding.layoutTip.gone()
                    mBinding.pagTip.stop()
                } else {
                    onBackPressed()
                }
            }
        }

        return true
    }

    override fun onResume() {
        super.onResume()
        mViewModel.enterMode(this@InterviewActivityMain)
        mViewModel.startMode()

        val setting = TransManager.getModelSetting(HomeServiceImplWrap.getUserModel())
        if (SpUtils.getBoolean(SpKey.IS_DEBUG_STATUS, false) || BuildConfig.DEBUG) {
            AiSpeechManager.shareInstance().audioChannels.forEach {
                if (it.recorder == ISpeechConstant.RECORDER.PHONE.toString() &&
                    setting.minVadHostEnergy != "-1") {
                    it.minVadEnergy =
                        BigDecimal(100000).div(setting.minVadHostEnergy.toBigDecimal()).toLong()
                } else if (it.recorder == ISpeechConstant.RECORDER.BLE.toString() &&
                    setting.minVadHeadsetEnergy != "-1") {
                    it.minVadEnergy =
                        BigDecimal(100000).div(setting.minVadHeadsetEnergy.toBigDecimal()).toLong()
                }
            }
        }
        mViewModel.liveFontEnum.value = setting.fontSize
        mViewModel.isShowOriginal.value = setting.isShowOriginal
        mViewModel.modeUtil.setBreakTime((getBreakTimeValue(setting.breakTime) * 1000).toInt())
        AiSpeechManager.shareInstance().audioChannels.forEach {
            SpeakManager.shareInstance().update(it.speakerType, setting.ttsSpeed.value)
        }
        mViewModel.modeUtil.setVoice(setting.ttsVoiceIsManSelf, setting.ttsVoiceIsManOther)

        if (mViewModel.liveIsPause.value == false) {
            if (mViewModel.isLeft) {
                mViewModel.enableHostChannel()
            } else if (mViewModel.isRight) {

                val bles = BleUtil.shared.connectedPeripherals
                if (bles.size == 2) {
                    mViewModel.enableHeadsetChannel(macIsLeftHeadset(HomeServiceImplWrap.getFirstDeviceMac()))
                } else if (bles.size == 1) {
                    mViewModel.enableHeadsetChannel(bles[0].isBleLeft())
                }

//                mViewModel.enableHeadsetChannel()
            }
        }

        startPureJob(5)

        RecycleViewUtil.scrollToBottom(mBinding.rvUp)
        RecycleViewUtil.scrollToBottom(mBinding.rvDown)

        BleUtil.shared.setTouchEventCallback { peripheral, _ ->
            if (mViewModel.isLeft) {
                return@setTouchEventCallback
            }

            if (mViewModel.liveIsPause.value == true) {
                return@setTouchEventCallback
            }

            // 触控点击的回调
            val blePeripherals = BleUtil.shared.connectedPeripherals
            DeviceTool.asWSeries(peripheral)?.let {
                TransUiUtil.showNowRecordToast(it.role == RawBlePeripheral.Role.Left)
                HomeServiceImplWrap.saveFirstDeviceMac(it.macSuffix4)
            }
            DeviceTool.asWSeries(peripheral)?.let {
                RecognizeManager.shareInstance().stopAllWorker()
                mViewModel.reset()
                if (mViewModel.isRight) {  // 当前在右边，耳机发言
                    mViewModel.enableHeadsetChannel(it.isBleLeft())
                }


//                    if (it.macSuffix4 != HomeServiceImplWrap.getFirstDeviceMac()) {
//                        Log.d("setTouchEventCallback", "macSuffix4 ${it.macSuffix4}")
//                        HomeServiceImplWrap.saveFirstDeviceMac(it.macSuffix4)
//                        RecognizeManager.shareInstance().stopAllWorker()
//                        mViewModel.reset()
//                        lifecycleScope.launch {
//                            BleUtil.shared.sendBleStopCmds(blePeripherals.toTypedArray(), true)
//                            delay(500)
//                            BleUtil.shared.sendBleStartCmds(arrayOf(peripheral), true)
//                        }
//                    }
            }
        }
    }

    private fun getBreakTimeValue(breakTimeEnum: SettingEnum.BreakTime): Float {
        val breakTimeList = mutableListOf(0.6f, 0.8f, 1.0f, 2.0f, 3.0f) // 单位秒
        return breakTimeList[SettingEnum.BreakTime.values().indexOf(breakTimeEnum)]
    }

    private fun vibrate() {
        val vibrator = getSystemService(Context.VIBRATOR_SERVICE) as Vibrator

        if (vibrator.hasVibrator()) {
            val duration = 100L // 震动的持续时间（毫秒）
            val amplitude = 255 // 震动的强度（0-255）

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                val vibrationEffect = VibrationEffect.createOneShot(duration, amplitude)
                vibrator.vibrate(vibrationEffect)
            } else {
                // 旧版本的 Android 只支持设置持续时间，不支持设置强度
                vibrator.vibrate(duration)
            }

        } else {
            // 设备不支持震动
        }
    }

    override fun onPause() {
        super.onPause()
        mViewModel.stopMode()
        mViewModel.exitMode()
    }

    override fun onDestroy() {
        HomeServiceImplWrap.setInMode(false)
        if (mViewModel.liveIsPause.value == false) {
            AiSpeechManager.shareInstance().stopAllWorker() //先停止所有播放
        }
        NetworkUtils.unregisterNetworkStatusChangedListener(netWorkListener)
        BleUtil.shared.removeListener(bleListener)
        setLedLevel(this, 0)
        val audioManager = getSystemService(Context.AUDIO_SERVICE) as AudioManager
        audioManager.setStreamVolume(AudioManager.STREAM_MUSIC, recentVolume, 0)

        unregisterReceiver(volumeKeyReceiver)

        BatteryReceiver.removeChangeListener(mBatteryListener)

        SensorsUtil.trackEvent(
            SensorsCustomEvent.X1_ExitXXMode.name, hashMapOf(
                "ModelType" to "旁听模式"
            )
        )

        SensorsUtil.trackEvent(
            SensorsCustomEvent.X1_ListenAndPlayUsageDuration.name, hashMapOf(
                "ModeDuration" to (System.currentTimeMillis() - statTime) / 1000
            )
        )

        val self = TransLanguageTool.getFullLanguageName(mViewModel.selfCode)
        val other = TransLanguageTool.getFullLanguageName(mViewModel.otherCode)
        SensorsUtil.trackEvent(
            SensorsCustomEvent.X1_LanguageSelectionListenAndPlay.name, hashMapOf(
                "SelectLanguage" to "$self-$other"
            )
        )
        super.onDestroy()
    }

    private fun processOfflineUiEvent(event: OfflineUiEvent) {
        when (event) {
            OfflineUiEvent.YouCanOpenOffline -> {
                DialogFactory.createConfirmCancelDialog(this@InterviewActivityMain,
                    titleText = BaseApp.context.getString(R.string.common_alert_tip),
                    BaseApp.context.getString(R.string.common_network_error_check_it),
                    confirmText = BaseApp.context.getString(R.string.common_cancel),
                    confirmCall = {},
                    cancelText = BaseApp.context.getString(R.string.common_go_setting),
                    cancelCall = {
                        wifiForResult.launch(Intent(Settings.ACTION_WIFI_SETTINGS))
                    }).show()
            }

            OfflineUiEvent.NetWorkError -> {
                showToast(BaseApp.context.getString(R.string.common_network_error_check_it))
            }
        }
    }

    private val wifiForResult =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result: ActivityResult ->
            mViewModel.checkWifiAgain()
        }

    companion object {
        const val TAG = "InterviewActivityMain"
        const val MAX_LENGTH = 5000
    }

}