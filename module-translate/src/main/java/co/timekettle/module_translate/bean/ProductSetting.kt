package co.timekettle.module_translate.bean

import android.os.Parcelable
import co.timekettle.module_translate.tools.TransLanguageTool
import com.timekettle.upup.comm.constant.TmkProductType
import com.timekettle.upup.comm.constant.TranslateMode
import kotlinx.parcelize.Parcelize

/**
 *
 * 产品设置的实体Bean
 * @author: licoba
 * @date: 2022/9/9
 */
@Parcelize
data class ProductSettings(
    var settings: MutableList<ProductSetting> = mutableListOf()  // 存储所有产品的设置项
) : Parcelable {
    // 根据模式获取到设置项
    fun getProductSetting(type: TranslateMode?): ProductSetting? {
        return settings.find {
            it.translateModel == type
        }
    }

    fun setProductSetting(setting: ProductSetting): ProductSettings {
        var index = -1
        for (i in settings.indices) {
            if (settings[i].translateModel == setting.translateModel) index = i
        }
        if (index == -1) {  // 找不到某个产品的相关配置，直接添加
            settings.add(setting)
        } else {
            settings[index] = setting
        }
        return this
    }

}

@Parcelize
data class ProductSetting(
//    var productType: TmkProductType = TmkProductType.UNKNOWN,
    var translateModel: TranslateMode? = TranslateMode.UNKNOWN,  // 设置了快速启动的模式
    var isOpenTts: Boolean = false,// 是否开启TTS
    var isOpenAITranslation: Boolean = false,// 是否开启大模型翻译
    var isShowOriginal: Boolean = true,// 是否显示原文
    var isShowOriginalOther: Boolean = true,// 对方是否能听到原声
    var isOpenOffline: Boolean = false,// 是否开启离线
    var ttsSpeed: SettingEnum.TtsSpeed = SettingEnum.TtsSpeed.Level2,  // 默认值 1.0倍
    var fontSize: SettingEnum.FontSize = SettingEnum.FontSize.Normal,
    var minVadHeadsetEnergy: String = "-1",
    var minVadHostEnergy: String = "-1",
    var environmentNoise: SettingEnum.EnvironmentNoise = SettingEnum.EnvironmentNoise.Normal,
    var breakTime: SettingEnum.BreakTime = SettingEnum.BreakTime.Normal,
    var isOpenGrabMic: Boolean = true, // 抢麦默认是开的
    var isOpenHeadsetLight: Boolean = true, // 开关是不是打开的
    var offlineCode: String = "",  // 离线的Code
    var volumeValues: MutableMap<String, Float> = hashMapOf(), // 所有设备的默认音量值，默认0.6
    var motherLanguageCode: String = if (translateModel == TranslateMode.SPEECH) "en-US" else TransLanguageTool.getDefaultSelfLan(), // 自己的上次使用的语言
    var foreignLanguageCode: String = if (translateModel == TranslateMode.SPEECH) "es-MX" else TransLanguageTool.getDefaultOtherLan(), // 对方的上次使用的语言
    var foreignLanguageCodeList: List<String> = listOf(), // 对方的其他语言对
    var ttsVoiceIsManSelf: Boolean = false, // SelfTTS的语音是否是男声，默认女声
    var ttsVoiceIsManOther: Boolean = false, // OtherTTS的语音是否是男声，默认女声
    var x1PlayContent: SettingEnum.X1PlayContent = SettingEnum.X1PlayContent.Translation, // X1播放内容，默认原文
) : Parcelable