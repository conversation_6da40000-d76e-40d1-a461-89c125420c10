package com.timekettle.upup.comm.tools

import com.timekettle.upup.base.utils.logD
import java.net.Socket
import java.security.Principal
import java.security.PrivateKey
import java.security.cert.X509Certificate
import javax.net.ssl.X509KeyManager

/**
 * author: weiconglee
 **/
class CustomPKCS12KeyManager : X509KeyManager {
    private var currentAlias: String = PKCS12Manager.PKCS_KEY_ALIAS
    private var delegate: X509KeyManager? = null
    private var lastCertificateExists: Boolean = false

    companion object {
        private const val TAG = "CustomPKCS12KeyManager"
    }

    private fun getDelegate(): X509KeyManager? {
        val certificateExists = PKCS12Manager.isCertificateExists()

        // 如果证书状态发生变化（从无到有），或者 delegate 为 null，则重新获取
        if (delegate == null || (!lastCertificateExists && certificateExists)) {
            logD("证书状态变化或首次获取 KeyManager，certificateExists: $certificateExists, lastCertificateExists: $lastCertificateExists", TAG)
            delegate = PKCS12Manager.getKeyManager()
            lastCertificateExists = certificateExists
            logD("KeyManager 获取${if (delegate != null) "成功" else "失败"}", TAG)
        }

        return delegate
    }

    override fun getClientAliases(keyType: String?, issuers: Array<out Principal>?): Array<String> {
        return arrayOf(currentAlias)
    }

    override fun chooseClientAlias(
        keyType: Array<out String>?, issuers: Array<out Principal>?, socket: Socket?
    ): String? {
        return getDelegate()?.chooseClientAlias(keyType, issuers, socket)
    }

    override fun getServerAliases(
        keyType: String?, issuers: Array<out Principal>?
    ): Array<String>? {
        return null
    }

    override fun chooseServerAlias(
        keyType: String?, issuers: Array<out Principal>?, socket: Socket?
    ): String? {
        return null
    }

    override fun getCertificateChain(alias: String?): Array<X509Certificate>? {
        return getDelegate()?.getCertificateChain(alias)
    }

    override fun getPrivateKey(alias: String?): PrivateKey? {
        return getDelegate()?.getPrivateKey(alias)
    }


}