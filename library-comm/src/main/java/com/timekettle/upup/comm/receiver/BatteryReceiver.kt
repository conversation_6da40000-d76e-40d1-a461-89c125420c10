package com.timekettle.upup.comm.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Context.WINDOW_SERVICE
import android.content.Intent
import android.graphics.PixelFormat
import android.nfc.NfcAdapter
import android.os.BatteryManager
import android.os.Build
import android.view.LayoutInflater
import android.view.ViewGroup
import android.view.WindowManager
import androidx.annotation.RequiresApi
import com.blankj.utilcode.util.ActivityUtils
import com.timekettle.upup.base.BaseApp
import com.timekettle.upup.base.utils.EventBusUtils
import com.timekettle.upup.base.utils.logD
import com.timekettle.upup.base.utils.logE
import com.timekettle.upup.comm.databinding.LayoutChargingBinding
import com.timekettle.upup.comm.widget.ChargingFloatView
import kotlinx.coroutines.*
import java.io.BufferedReader
import java.io.FileReader
import java.io.IOException
import java.lang.reflect.Method


/**
 * <AUTHOR>
 * @date 2023/10/17
 * @desc 充电广播接收器
 */
object BatteryReceiver : BroadcastReceiver() {
    private const val TAG = "BatteryReceiver"
    private var changeListeners: MutableList<((String?) -> Unit)?> = mutableListOf()
    private var mWindowManager: WindowManager? = null
    private var chargingView: ChargingFloatView? = null
    private val mNfcAdapter by lazy {
        NfcAdapter.getDefaultAdapter(BaseApp.context)
    }
    private val scope = MainScope()
    private var job: Job? = null

    // 添加一个监听
    fun addChangeListener(listener: (String?) -> Unit) {
        // 防止重复添加
        if (changeListeners.contains(listener)) {
            return
        }
        changeListeners.add(listener)
    }

    // 移除一个监听
    fun removeChangeListener(listener: (String?) -> Unit) {
        changeListeners.remove(listener)
    }

    /**
     * 是否是底座充电
     */
    fun isBaseCharging(): Boolean {
        val node = "/sys/devices/platform/charger/Charger_Type"
        val value = try {
            val file = FileReader(node)
            val reader = BufferedReader(file)
            reader.use {
                reader.readLine()
            }
        } catch (e: Exception) {
            e.printStackTrace()
            logE("读取文件节点失败！${e.message}", TAG)
        }
        logD("value = $value", TAG)
        return value == "3"
    }

    fun changeNFCEnabled(value: Boolean) {
        if (mNfcAdapter.isEnabled == false && value) {
            enableNfc(mNfcAdapter)
        } else if (mNfcAdapter.isEnabled && !value) {
            disableNfc(mNfcAdapter)
        }
    }

    private fun enableNfc(adapter: NfcAdapter) {
        logD("开启 NFC ", TAG)
        try {
            // 获取隐藏方法
            val enableMethod: Method = NfcAdapter::class.java.getDeclaredMethod("enable")
            enableMethod.isAccessible = true // 绕过私有权限检查
            enableMethod.invoke(adapter) // 调用方法
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun disableNfc(adapter: NfcAdapter) {
        logD("关闭 NFC ", TAG)
        try {
            // 获取隐藏方法
            val disableMethod: Method = NfcAdapter::class.java.getDeclaredMethod("disable")
            disableMethod.isAccessible = true // 绕过私有权限检查
            disableMethod.invoke(adapter) // 调用方法
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }


    @RequiresApi(Build.VERSION_CODES.O)
    override fun onReceive(context: Context, intent: Intent?) {
        changeListeners.forEach {
            it?.invoke(intent?.action)
        }
        chargingView?.percent = getElectricity()
        when (intent?.action) {
            Intent.ACTION_POWER_CONNECTED -> {
                logD("ACTION_POWER_CONNECTED 电源连接", TAG)
                if (isBaseCharging()) {
                    logD("底座充电", TAG)
                    changeNFCEnabled(false)
                }
                addChargingView(context)
            }

            Intent.ACTION_BATTERY_CHANGED -> {
//                logD("ACTION_BATTERY_CHANGED 电量改变", TAG)
            }

            Intent.ACTION_POWER_DISCONNECTED -> {
                logD("ACTION_POWER_DISCONNECTED 电源断开", TAG)
                job?.cancel()
                job = null
                changeNFCEnabled(true)
                removeChargingView()
            }


            Intent.ACTION_CONFIGURATION_CHANGED -> {
                logD("ACTION_POWER_DISCONNECTED 屏幕旋转了，取消充电动画", TAG)
                job?.cancel()
                job = null
                removeChargingView()
            }
        }
    }

    // 获取当前主机的电量百分比
    /**
     * 获取当前系统电量 %
     */
    private fun getElectricity(): Int {
        val manager = BaseApp.context.getSystemService(Context.BATTERY_SERVICE) as BatteryManager
        return manager.getIntProperty(BatteryManager.BATTERY_PROPERTY_CAPACITY)
    }

    private fun startCloseChargingViewTimer() {
        job = scope.launch {
            logD("移除充电动画", TAG)
            if (!isBaseCharging()) {
                delay(5_000)
                removeChargingView()
            }
        }
    }


    @RequiresApi(Build.VERSION_CODES.O)
    private fun addChargingView(context: Context) {
        removeChargingView()
        val percent = getElectricity()
        logD("添加充电动画，当前 Box 电量：$percent")
        if(ActivityUtils.getTopActivity() == null) {
            logD("顶部的Activity为空，无法弹窗")
            return
        }
        chargingView = ChargingFloatView(ActivityUtils.getTopActivity()).apply {
            this.percent = percent
        }
        mWindowManager = context.getSystemService(WINDOW_SERVICE) as WindowManager
        val layoutInflater =
            context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
        val mBinding = LayoutChargingBinding.inflate(layoutInflater)
        mBinding.root.setOnClickListener {
            mWindowManager!!.removeView(it)
        }
        val layoutParam = WindowManager.LayoutParams().apply {
            //设置大小 自适应
            width = ViewGroup.LayoutParams.MATCH_PARENT
            height = ViewGroup.LayoutParams.MATCH_PARENT
            flags =
                WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL or WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE
            type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            format = PixelFormat.RGBA_8888
            windowAnimations = android.R.style.Animation_Translucent
        }
        mWindowManager!!.addView(chargingView, layoutParam)
        startCloseChargingViewTimer()
    }

    private fun removeChargingView() {
        chargingView?.let {
            mWindowManager!!.removeView(it)
            chargingView = null
        }
    }
}