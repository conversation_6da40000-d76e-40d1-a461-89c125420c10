package com.timekettle.upup.comm.tools

import android.annotation.SuppressLint
import com.timekettle.upup.base.utils.logD
import java.security.cert.X509Certificate
import javax.net.ssl.X509TrustManager

/**
 * author: weiconglee
 **/
@SuppressLint("CustomX509TrustManager")
class CustomPKCS12TrustManager : X509TrustManager {
    private var delegate: X509TrustManager? = null
    private var lastCertificateExists: Boolean = false

    companion object {
        private const val TAG = "CustomPKCS12TrustManager"
    }

    private fun getDelegate(): X509TrustManager? {
        val certificateExists = PKCS12Manager.isCertificateExists()

        // 如果证书状态发生变化（从无到有），或者 delegate 为 null，则重新获取
        if (delegate == null || (!lastCertificateExists && certificateExists)) {
            logD("证书状态变化或首次获取 TrustManager，certificateExists: $certificateExists, lastCertificateExists: $lastCertificateExists", TAG)
            delegate = PKCS12Manager.getTrustManager()
            lastCertificateExists = certificateExists
            logD("TrustManager 获取${if (delegate != null) "成功" else "失败"}", TAG)
        }

        return delegate
    }

    override fun checkClientTrusted(chain: Array<out X509Certificate>?, authType: String?) {
        getDelegate()?.checkClientTrusted(chain, authType)
    }

    override fun checkServerTrusted(chain: Array<out X509Certificate>?, authType: String?) {
        getDelegate()?.checkServerTrusted(chain, authType)
    }

    override fun getAcceptedIssuers(): Array<X509Certificate> {
        return getDelegate()?.acceptedIssuers ?: arrayOf()
    }


}