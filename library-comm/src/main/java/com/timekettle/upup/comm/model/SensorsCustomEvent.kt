package com.timekettle.upup.comm.model

/**
 * <AUTHOR>
 * @date 2023/3/13 11:52
 * @email <EMAIL>
 * @desc 神策自定义事件
 */
enum class SensorsCustomEvent(var chineseName: String = "") {
    //系统语言
    X1_SystemLanguageSelection,
    //系统语言
    X1_IndustrySelect,
    //翻译模式
    X1_EnterXXMode,
    X1_ExitXXMode,
    //语言选择
    X1_LanguageSelectionOneOnOne,
    X1_LanguageSelectionListenAndPlay,
    X1_LanguageSelectionMideaTranslation,
    X1_LanguageSelectionAskAndGo,
    X1_LanguageSelectionVoiceCall,
    X1_LanguageSelectionMultiPerson,
    X1_LanguageSelectionPresentation,
    X1_PresentationAttendeeLangList,
    X1_PresentationAITranslationSwitch,
    //双人对话
    X1_OneOnOneUsageDuration,
    //旁听翻译
    X1_ListenAndPlayClickListen,
    X1_ListenAndPlayClickSpeak,
    X1_ListenAndPlayUsageDuration,
    //手持翻译
    X1_AskAndGoUsageDuration,
    //多人会议
    X1_CreateMeeting,
    X1_JoinMeeting,
    X1_NearbyMeetingJion,
    X1_CreateMeetingPasswordToggle,
    X1_CreateMeetingEnterButton,
    X1_ManuallyJoin,
    X1_EnterMeetingIDPageConfirmButton,
    X1_EnterPasswordIDPageConfirmButton,
    X1_ViewPersonalInformationPageLanguageSelectionButton,
    X1_ViewPersonalInformationPageUnmute,
    X1_ViewPersonalInformationPageEndButton,
    X1_EndMeetingPageEndMeetingButton,
    X1_EndMeetingPageLeaveMeetingButton,
    X1_EndMeetingPageCancelButton,
    X1_QuickMeetingButton,
    X1_QuickMeetingStartDoublePressPowerButton,
    X1_MultiPersonMeetingNotes,
    X1_InviteAttendessButton,
    //音视频
    X1_MediaTranstionDuration,
    X1_MediaTranstionBluetoothNext,
    X1_MediaTranstionLanguageSelectionPagClickStart,
    X1_MediaTranstionUseCase,
    //演讲翻译
    X1_PresentionTranstionDuration,
    X1_PresentionShareQRCodeButton,
    X1_PresentionSubtitleCastingButton,
    //离线翻译
    X1_OfflineTranslationSwitch,
    X1_OfflineTranslationClickToDownload,
    //真人式发音
    X1_ToneSelectionOneOnOne,
    X1_ToneSelectionListenAndPlay,
    X1_ToneSelection,
    X1_ToneSelectionAskAndGo,
    X1_ToneSelectionVoiceCall,
    X1_ToneSelectionMultiPerson,
    X1_ScreenisOn,
    //翻译设置
    X1_TranslationSettingsEntrance,
    X1_FontSizeSettingOption,
    X1_ShowOriginalTextSettingOption,
    X1_TranslationAudioSpeedSettingOption,
    X1_PauseDurationSettingOption,
    X1_PresentionTranslationReplay,
    //翻译记录
    X1_EnterTranslationHistory,
    X1_ViewTranslationHistory,
    X1_ExportTranslationHistoryButton,
    X1_GenerateTranslationHistoryFile,
    //设置
    X1_Settings,
    //联系我们
    X1_ContactUs,
    //上传日志
    X1_UploadlogEntrance,
    X1_ClickUploadlog,
    X1_DeviceAuthentication
}


