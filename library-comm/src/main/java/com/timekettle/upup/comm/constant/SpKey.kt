package com.timekettle.upup.comm.constant

/**
 * 本地存储的键 放在此类中
 * 注意：组件化项目中，如果只与当前业务模块相关的SPKey，应该放在对应业务模块的SPKey中，不应该放在common模块里
 * <AUTHOR>
 * @since 2022/04/15
 */
object SpKey {
    const val ACCESS_TOKEN = "ACCESS_TOKEN"
    const val REFRESH_TOKEN = "REFRESH_TOKEN"

    const val BASE_URL = "BASE_URL"
    const val BUSINESS_BASE_URL = "BUSINESS_BASE_URL"
    const val SA_URL = "SA_URL"
    const val CAPTAIN_BASE_URL = "CAPTAIN_BASE_URL"
    const val COMM_HAS_HISTORY = "COMM_HAS_HISTORY"  //是否产生了历史记录（)）
    const val IS_DEBUG_STATUS = "IS_DEBUG_STATUS"  // 是否开启调试面板
    const val IS_RECORD_AUDIO_OPEN = "IS_RECORD_AUDIO_OPEN"  // 是否保存录音文件
    const val TMK_ENGINE_HOST_IP = "TMK_ENGINE_HOST_IP"  // Tmk 引擎 ip
    const val TMK_SIP_HOST_IP = "TMK_SIP_HOST_IP"  // Tmk sip ip
    const val SHOW_TMK_ENGINE_HOST_IP_IN_CHAT = "SHOW_TMK_ENGINE_HOST_IP_IN_CHAT"  // 在对话气泡内，显示Tmk 引擎 ip
    const val CACHE_ALL_FILE = "CACHE_ALL_FILE"  // 缓存所有日志文件，测试用，还会影响上传策略

    const val ALREADY_READ_PRIVACY = "AlreadyReadPrivacy" // 用户是否已经阅读过隐私弹窗，并且同意了

    const val SSID="SSID" //wifi的ssid

    const val DEVICE_MAC_NUM = "DEVICE_MAC_NUM"


    const val NEED_CONN_TEST_MAC = "NEED_CONN_TEST_MAC" // 需要连接的Mac地址后缀
    const val IS_FIRST_POWER_ON = "IS_FIRST_POWER_ON" //是否是首次开机
    const val MODE_LIST = "MODE_LIST" //功能模块列表
    const val MODE_LIST_REMOVE_PHONE = "MODE_LIST_REMOVE_PHONE" //功能模块首次移除 PHONE 模块
    const val SHOULD_MOVE_GUIDE_SHOW = "SHOULD_MOVE_GUIDE_SHOW" //是否需要显示移动引导

    const val CATCH_HOSTS = "CATCH_HOSTS"
    const val HIDE_GUIDE = "HIDE_GUIDE" //是否隐藏引导页
    const val TTS_STATE = "TTS_STATE" //TTS状态（开启或关闭）
    const val DONGLE_VERSION = "DONGLE_VERSION"
    const val DONGLE_CONNECTED = "DONGLE_CONNECTED"
    const val IS_SHOW_VIDEO = "IS_SHOW_VIDEO"
    const val HIDE_VIDEO_GUIDE = "HIDE_VIDEO_GUIDE"
    const val ROOM_NUMBER = "ROOM_NUMBER"
    const val ROOM_QR_CODE = "ROOM_QR_CODE"
    const val LAST_DATE = "LAST_DATE"

    const val AGREE_POLICY = "AGREE_POLICY"
    const val AGREE_POLICY_TIME = "AGREE_POLICY_TIME"

    const val USER_INDUSTRY = "USER_INDUSTRY"
    const val USER_EMAIL = "USER_EMAIL"
    const val ALREADY_UPLOAD_USER_INFO = "ALREADY_UPLOAD_USER_INFO"

    const val FEEDBACK_EMAIL = "FEEDBACK_EMAIL"
    const val IS_SAVE_RECORD = "IS_SAVE_RECORD"
}