package com.timekettle.upup.comm.net.helper

import androidx.work.ExistingWorkPolicy
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.WorkManager
import com.timekettle.upup.base.BaseApp
import com.timekettle.upup.comm.utils.SignUtil
import com.timekettle.upup.comm.utils.DeviceUtil
import com.timekettle.upup.base.utils.SpUtils
import com.timekettle.upup.base.utils.logD
import com.timekettle.upup.base.utils.logE
import com.timekettle.upup.comm.base.BaseResponseV2
import com.timekettle.upup.comm.model.UserInfoRequest
import com.timekettle.upup.comm.constant.NetUrl
import com.timekettle.upup.comm.constant.SpKey
import com.timekettle.upup.comm.model.CertificateBean
import com.timekettle.upup.comm.model.TokenBean
import com.timekettle.upup.comm.net.ApiResult
import com.timekettle.upup.comm.net.BusinessApi
import com.timekettle.upup.comm.net.listener.SSLEventListener
import com.timekettle.upup.comm.tools.CustomPKCS12KeyManager
import com.timekettle.upup.comm.tools.CustomPKCS12TrustManager
import com.timekettle.upup.comm.worker.RefreshTokenWorker
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.withTimeoutOrNull
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.OkHttpClient
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.asRequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import okhttp3.internal.asFactory
import okhttp3.logging.HttpLoggingInterceptor
import org.json.JSONObject
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.io.File
import java.security.SecureRandom
import java.util.concurrent.TimeUnit
import javax.net.ssl.SSLContext

object BusinessManager {
    private const val TAG = "BusinessManager"
    val tokenFlow = MutableSharedFlow<String>(replay = 1)
    private val okHttpClient: OkHttpClient by lazy {
        val keyManager = CustomPKCS12KeyManager()
        val trustManager = CustomPKCS12TrustManager()
        val sslContext = SSLContext.getInstance("TLSv1.2")
        sslContext.init(arrayOf(keyManager), null, SecureRandom())
        OkHttpClient.Builder()
            .sslSocketFactory(sslContext.socketFactory, trustManager)
            .addInterceptor(HttpLoggingInterceptor().setLevel(HttpLoggingInterceptor.Level.BODY))
//            .eventListenerFactory(SSLEventListener().asFactory()) // 全局监听
            .callTimeout(10, TimeUnit.SECONDS)
            .build()
    }

    private val instance: Retrofit by lazy {
        Retrofit.Builder()
            .baseUrl(SpUtils.getString(SpKey.BASE_URL, NetUrl.RELEASE_URL))
            .client(okHttpClient)
            .addConverterFactory(GsonConverterFactory.create())
            .build()
    }

    private val api by lazy {
        instance.create(BusinessApi::class.java)
    }

    suspend fun fetchAvailableToken(): TokenBean? {
        val baseUrl = SpUtils.getString(SpKey.BASE_URL, NetUrl.RELEASE_URL)
        val tokenUrl = if (baseUrl == NetUrl.RELEASE_URL) NetUrl.TOKEN_ADDRESS_RELEASE else NetUrl.TOKEN_ADDRESS_TEST
        logD("token url = $tokenUrl", TAG)
        return try {
//            val timestamp = System.currentTimeMillis() / 1000
//            val sn = DeviceUtil.getSerialNumber()
//            api.getToken("x1",
//                genSign(timestamp.toString(), sn),
//                sn,
//                timestamp.toString(),
//                JSONObject().toString()
//                    .toRequestBody("application/json; charset=utf-8".toMediaTypeOrNull())
//            ).data
            api.getTokenV2(tokenUrl).data
        } catch (e: Exception) {
            logE("fetchAvailableToken error: ${e.message}", TAG)
            null
        }
    }

    fun savePrivacyInfo(params: RequestBody) = withValidToken {
        api.savePrivacyInfo("Bearer $it", params)
    }

    fun getDfuNotify(appVersion: String) = withValidToken {
        val res = api.getDfuNotify("Bearer $it", appVersion)
        logD("获取 dfu 通知消息 $res", TAG)
        res.data
    }

    fun checkSN() = withValidToken {
        val res = api.checkSN("Bearer $it")
        logD("检查 SN 是否显示入口 $res", TAG)
        res.data
    }

    fun getMeetingIdList() = withValidToken {
        val res = api.getMeetingIpList("Bearer $it")
        logD("获取会议 ip 列表 $res", TAG)
        res.data
    }

    fun saveUserInfo(email: String, industry: Int) = withValidToken {
        val res = api.saveUserInfo("Bearer $it", UserInfoRequest(email, if (industry == 0) null else industry))
        logD("保存用户信息 $res", TAG)
        res
    }

    fun getS3UploadUrl(fileName: String) = withValidToken {
        val res = api.getS3UploadUrl("Bearer $it", mapOf("file_name" to fileName))
        logD("获取 S3 上传地址 $res", TAG)
        res
    }

    fun uploadFile(file: File, url: String) = flow {
        // 确保文件是 ZIP 文件
        val zipContentType = "application/zip"
        val requestFileBody = file.asRequestBody(zipContentType.toMediaTypeOrNull())
        api.uploadFile(url, requestFileBody, zipContentType).run {
            emit(this)
        }
    }.flowOn(Dispatchers.IO)

    fun saveUploadFeedback(email: String, fileName: String, loggingTime: String) = withValidToken {
        val res = api.saveUploadFeedback(
            "Bearer $it",
            mapOf("email" to email, "file_name" to fileName, "logging_time" to loggingTime)
        )
        logD("提交上传反馈 $res", TAG)
        res
    }

    fun getUserEmail() = withValidToken {
        val res = api.getUserEmail("Bearer $it")
        logD("获取用户email $res", TAG)
        res.data
    }

    fun deleteUserEmail() = withValidToken {
        val res = api.deleteUserEmail("Bearer $it")
        logD("删除用户email $res", TAG)
        res
    }

    fun getCertificate(url: String, csr: String) = flow {
        emit(ApiResult.Loading(true))
        val requestBody = hashMapOf("csr" to csr,)
        val response = api.getCertificate(url, requestBody)
        if (response.isSuccessful) {
            val baseResponse = response.body() as BaseResponseV2<CertificateBean?>
            if (baseResponse.code == 0) {
                emit(ApiResult.Success(baseResponse.data))
            } else {
                logE("getCertificate error = ${baseResponse.message}", TAG)
                emit(ApiResult.AuthError(baseResponse.message ?: "", baseResponse.code.toString()))
            }
        } else {
            val errorMsg = response.errorBody()?.string() ?: ""
            response.errorBody()?.close()
            emit(ApiResult.Error(errorMsg))
        }
    }.flowOn(Dispatchers.IO)
        .catch {
            it.printStackTrace()
            emit(ApiResult.Error(it.message ?: ""))
        }

    /**
    * 通用 Flow 请求构造器（自动处理 Token 等待 + IO 线程切换）
    * @param timeoutMillis 等待 Token 的超时时间（默认 20 秒）
    * @param requestBlock 实际请求逻辑（接收 token 作为参数）
    */
    private fun <T> withValidToken(
        timeoutMillis: Long = 20_000,
        requestBlock: suspend (token: String) -> T
    ): Flow<T> = flow {
        val validToken = withTimeoutOrNull(timeoutMillis) {
            tokenFlow.filter { it.isNotEmpty() }.first()
        } ?: throw RuntimeException("Wait for token timeout")

        emit(requestBlock(validToken))
    }.flowOn(Dispatchers.IO)

    /**
     * 提交刷新 Token 的 Worker
     * @param delaySeconds 延迟执行时间（秒）
     */
    fun submitTokenWorker(delaySeconds: Long) {
        val tokenRequest = OneTimeWorkRequestBuilder<RefreshTokenWorker>()
            .setInitialDelay(delaySeconds, TimeUnit.SECONDS)
            .build()
        WorkManager.getInstance(BaseApp.context).enqueueUniqueWork(
            RefreshTokenWorker.WORK_NAME,
            ExistingWorkPolicy.REPLACE,
            tokenRequest
        )
    }

    private fun genSign(timestamp: String, sn: String): String {
        val headerMap = mutableMapOf<String, String>()
        headerMap["sncode"] = sn
        headerMap["timestamp"] = timestamp
        val signContent = SignUtil.toSignedContent(headerMap)
        if (instance.baseUrl().toString().removeSuffix("/") == NetUrl.BUSINESS_RELEASE_URL ||
            instance.baseUrl().toString().removeSuffix("/") == NetUrl.BUSINESS_AMERICA_RELEASE_URL) {
            return SignUtil.sign(signContent, SignUtil.PRIVATE_KEY_RELEASE)
        }
        // 移除开始和结束的标记，以及换行
        return SignUtil.sign(signContent, SignUtil.PRIVATE_KEY_TEST)
    }
}