package com.timekettle.module_home.ui.adapter

import android.view.View
import android.widget.ImageView
import android.widget.TextView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.timekettle.module_home.R
import com.timekettle.upup.base.BaseApp
import com.timekettle.upup.comm.constant.TranslateMode
import com.timekettle.upup.comm.utils.LanguageUtil

data class ChooseModelItem(val mode: TranslateMode, var isHidden: Boolean = false)

class ChooseModelAdapter(
    private val fullList: MutableList<ChooseModelItem>,
    private val onListChanged: ((List<ChooseModelItem>) -> Unit)? = null
) : BaseQuickAdapter<ChooseModelItem, MyViewHolder>(R.layout.item_model_choose, fullList.filter { !it.isHidden }.toMutableList()) {

    var isEditMode: Boolean = false
        set(value) {
            field = value
            if (value) {
                fullList.sortWith(compareBy { it.isHidden })
                setList(fullList)
            } else {
                setList(fullList.filter { !it.isHidden })
            }
        }

    override fun convert(holder: MyViewHolder, item: ChooseModelItem) {

        val imgModel = holder.getView<ImageView>(R.id.vModelImg)

        when (item.mode) {
            TranslateMode.SIMUL -> imgModel.setImageResource(R.mipmap.home_mode_icon_simul)
            TranslateMode.SPEAKER -> imgModel.setImageResource(R.mipmap.home_mode_icon_listen)
            TranslateMode.KEYPAD -> imgModel.setImageResource(R.mipmap.home_mode_icon_ask)
            TranslateMode.VIDEO -> imgModel.setImageResource(R.mipmap.home_mode_icon_video)
            TranslateMode.SPEECH -> imgModel.setImageResource(R.mipmap.home_mode_icon_lecture)
            TranslateMode.PHONE -> imgModel.setImageResource(R.mipmap.home_mode_icon_call)
            TranslateMode.MEETING -> imgModel.setImageResource(R.mipmap.home_mode_icon_metting)
            TranslateMode.SETTING -> imgModel.setImageResource(R.mipmap.home_mode_icon_setting)
            else -> {}
        }
        val tv = holder.getView<TextView>(R.id.tv_name)
        tv.text = item.mode.modeName
        if (LanguageUtil.isRu()) {
            if (item.mode.modeName == BaseApp.context.getString(com.timekettle.upup.comm.R.string.home_meeting)) {
                tv.textSize = 11.5f
            } else tv.textSize = 14f
        } else {
            tv.textSize = 15f
        }

        val ivRemove = holder.getView<ImageView>(R.id.iv_edit_remove)
        val ivAdd = holder.getView<ImageView>(R.id.iv_edit_add)
        val vMask = holder.getView<View>(R.id.vMask)

        if (isEditMode) {
            if (item.isHidden) {
                ivRemove.visibility = View.GONE
                ivAdd.visibility = View.VISIBLE
                vMask.visibility = View.VISIBLE
            } else if (item.mode != TranslateMode.SETTING) {
                ivRemove.visibility = View.VISIBLE
                ivAdd.visibility = View.GONE
                vMask.visibility = View.GONE
            } else {
                ivRemove.visibility = View.GONE
                ivAdd.visibility = View.GONE
                vMask.visibility = View.GONE
            }
        } else {
            ivRemove.visibility = View.GONE
            ivAdd.visibility = View.GONE
            vMask.visibility = View.GONE
        }

        ivRemove.setOnClickListener {
            val currentItem = fullList.find { it.mode == item.mode } ?: return@setOnClickListener
            currentItem.isHidden = true

            fullList.remove(currentItem)
            fullList.add(currentItem)

            setList(fullList)
            onListChanged?.invoke(fullList)
        }

        ivAdd.setOnClickListener {
            val currentItem = fullList.find { it.mode == item.mode } ?: return@setOnClickListener
            currentItem.isHidden = false

            fullList.remove(currentItem)
            val firstHiddenIndex = fullList.indexOfFirst { it.isHidden }
            var insertionPoint = if (firstHiddenIndex == -1) fullList.size else firstHiddenIndex
            // 如果设置项在最后一个可见项，插入到设置项之前，否则插入到最后
            if (insertionPoint > 0 &&fullList[insertionPoint - 1].mode == TranslateMode.SETTING) {
                insertionPoint = insertionPoint - 1
            }
            fullList.add(insertionPoint, currentItem)

            setList(fullList)
            onListChanged?.invoke(fullList)
        }
    }
}

class MyViewHolder(itemView: View) : BaseViewHolder(itemView)