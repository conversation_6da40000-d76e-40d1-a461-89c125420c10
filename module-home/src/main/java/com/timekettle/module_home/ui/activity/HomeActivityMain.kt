package com.timekettle.module_home.ui.activity

import android.annotation.SuppressLint
import android.app.Activity
import android.app.Dialog
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothManager
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Canvas
import android.media.Ringtone
import android.media.RingtoneManager
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import android.util.Log
import android.view.MotionEvent
import android.view.View
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.annotation.RequiresApi
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.RecyclerView
import co.timekettle.agora.SpeechManager
import co.timekettle.btkit.BleUtil
import co.timekettle.btkit.bean.RawBlePeripheral
import co.timekettle.sip.call.CallManager
import co.timekettle.sip.call.sendSip
import co.timekettle.sip.entity.ReqEntity
import co.timekettle.sip.utils.GsonUtil
import com.alibaba.android.arouter.facade.annotation.Route
import com.alibaba.android.arouter.launcher.ARouter
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.AppUtils
import com.blankj.utilcode.util.DeviceUtils
import com.blankj.utilcode.util.GsonUtils
import com.blankj.utilcode.util.NetworkUtils
import com.google.gson.reflect.TypeToken
import com.hjq.permissions.OnPermissionCallback
import com.hjq.permissions.Permission
import com.hjq.permissions.XXPermissions
import com.timekettle.module_home.constants.HomeSpKey.INTERVIEW_NO_LONGER_PROMPT_MIN
import com.timekettle.module_home.constants.HomeSpKey.KEYPAD_NO_LONGER_PROMPT_MIN
import com.timekettle.module_home.constants.HomeSpKey.MEETING_NO_LONGER_PROMPT_MIN
import com.timekettle.module_home.constants.HomeSpKey.PHONE_NO_LONGER_PROMPT_MIN
import com.timekettle.module_home.constants.HomeSpKey.SIMUL_NO_LONGER_PROMPT_MIN
import com.timekettle.module_home.constants.HomeSpKey.SPEECH_NO_LONGER_PROMPT_MIN
import com.timekettle.module_home.constants.HomeSpKey.VIDEO_NO_LONGER_PROMPT_MIN
import com.timekettle.module_home.constants.IntentKey
import com.timekettle.module_home.databinding.HomeActivityMainBinding
import com.timekettle.module_home.ui.adapter.ChooseModelAdapter
import com.timekettle.module_home.ui.adapter.ChooseModelItem
import com.timekettle.module_home.ui.adapter.MyViewHolder
import com.timekettle.module_home.ui.vm.HomeViewModel
import com.timekettle.upup.base.BaseApp
import com.timekettle.upup.base.ktx.getAppViewModel
import com.timekettle.upup.base.ktx.gone
import com.timekettle.upup.base.ktx.observeLiveData
import com.timekettle.upup.base.ktx.startKtxActivity
import com.timekettle.upup.base.ktx.visible
import com.timekettle.upup.base.utils.EventBusRegister
import com.timekettle.upup.base.utils.EventBusUtils
import com.timekettle.upup.base.utils.SpUtils
import com.timekettle.upup.base.utils.logD
import com.timekettle.upup.base.utils.logE
import com.timekettle.upup.base.utils.showDebugToast
import com.timekettle.upup.base.utils.showToast
import com.timekettle.upup.base.utils.startActivityByRoute
import com.timekettle.upup.comm.BuildConfig
import com.timekettle.upup.comm.R
import com.timekettle.upup.comm.base.BaseActivity
import com.timekettle.upup.comm.bean.CallRecordEntity
import com.timekettle.upup.comm.bean.DownloadStatus
import com.timekettle.upup.comm.conference.Code
import com.timekettle.upup.comm.conference.ConController
import com.timekettle.upup.comm.conference.PhoneState
import com.timekettle.upup.comm.conference.addCallRegBackListener
import com.timekettle.upup.comm.conference.addCallStateBackListener
import com.timekettle.upup.comm.conference.removeCallRegBackListener
import com.timekettle.upup.comm.conference.removeCallStateBackListener
import com.timekettle.upup.comm.constant.CommIntentKey
import com.timekettle.upup.comm.constant.NetUrl
import com.timekettle.upup.comm.constant.RouteKey
import com.timekettle.upup.comm.constant.RouteUrl
import com.timekettle.upup.comm.constant.SpKey
import com.timekettle.upup.comm.constant.SpKey.DEVICE_MAC_NUM
import com.timekettle.upup.comm.constant.TranslateMode
import com.timekettle.upup.comm.dao.CallRecordDao
import com.timekettle.upup.comm.dialog.CountdownDialog
import com.timekettle.upup.comm.dialog.DeviceNeedConnectDialog
import com.timekettle.upup.comm.dialog.DialogFactory
import com.timekettle.upup.comm.ktx.setLedLevel
import com.timekettle.upup.comm.model.BrokenReason
import com.timekettle.upup.comm.model.CertificateUpdateEvent
import com.timekettle.upup.comm.model.CheckHeadsetUpdateEvent
import com.timekettle.upup.comm.model.DownloadMultipleFileEvent
import com.timekettle.upup.comm.model.MeetingEvent
import com.timekettle.upup.comm.model.OtaReadyEvent
import com.timekettle.upup.comm.model.PhoneStateEvent
import com.timekettle.upup.comm.model.SensorsCustomEvent
import com.timekettle.upup.comm.model.ShowDeviceDialogListEvent
import com.timekettle.upup.comm.model.UnZipTryAuthResultEvent
import com.timekettle.upup.comm.model.X1Status
import com.timekettle.upup.comm.service.home.HomeServiceImplWrap
import com.timekettle.upup.comm.service.setting.SettingServiceImplWrap
import com.timekettle.upup.comm.service.trans.TransServiceImplWrap
import com.timekettle.upup.comm.tools.AKSManager
import com.timekettle.upup.comm.tools.DeviceManager
import com.timekettle.upup.comm.tools.DeviceTool
import com.timekettle.upup.comm.tools.HallScanManager
import com.timekettle.upup.comm.tools.PKCS12Manager
import com.timekettle.upup.comm.utils.DeviceUtil
import com.timekettle.upup.comm.utils.DongleOtaUtil
import com.timekettle.upup.comm.utils.DongleVersionUtil
import com.timekettle.upup.comm.utils.HallUtil
import com.timekettle.upup.comm.utils.IntentHelper
import com.timekettle.upup.comm.utils.LanguageUtil.getLanguageDisplayName
import com.timekettle.upup.comm.utils.MyActivityUtil
import com.timekettle.upup.comm.utils.PhoneUtil
import com.timekettle.upup.comm.utils.SensorsUtil
import com.timekettle.upup.comm.viewmodel.VMTopDevice
import com.timekettle.upup.comm.widget.KeyboardUtil
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.libpag.PAGFile
import org.libpag.PAGScaleMode
import razerdp.basepopup.BasePopupWindow.OnDismissListener
import java.util.Collections
import java.util.Date
import androidx.core.view.isVisible

/**
 * <AUTHOR>
 * @since 2023/4/2
 * @desc 首页
 */
@EventBusRegister
@AndroidEntryPoint
@Route(path = RouteUrl.Home.HomeActivityMain)
class HomeActivityMain : BaseActivity<HomeActivityMainBinding, HomeViewModel>() {

    private val REQUIRED_PERMISSIONS = arrayOf(
        "android.permission.CAMERA",
        "android.permission.WRITE_EXTERNAL_STORAGE",
        "android.permission.READ_EXTERNAL_STORAGE",
        "android.permission.RECORD_AUDIO",
        "android.permission.ACCESS_COARSE_LOCATION",
        "android.permission.ACCESS_FINE_LOCATION"
    )

    private val REQUEST_CODE_PERMISSIONS = 1001
    private val vmTopDevice: VMTopDevice by lazy { getAppViewModel() }
    private var callRecordDao: CallRecordDao = TransServiceImplWrap.getCallRecordDao()
    override val mViewModel: HomeViewModel by viewModels()
    private var fullModeList: MutableList<ChooseModelItem> = mutableListOf()
    private lateinit var chooseModelAdapter: ChooseModelAdapter

    private var isInEditMode = false

    private var headsetOtaDialog: Dialog? = null
    private var x1NeedConnectDialog: DeviceNeedConnectDialog? = null
    private var regListener: ((Int, String) -> Unit)? = null
    private var callStatusListener: ((Int, Int, String, String, String, String) -> Unit)? = null
    private var myBleListener :BleUtil.Listener? = null
    private var account = ""
    private var confirmed = false
    private var callRing: Ringtone? = null
    private var hungUpRing: Ringtone? = null
    private var meetingId = ""
    private var password = ""
    private var lastScanTime: Long = 0
    private var lastLeftSoundTime: Long = 0
    private var lastRightSoundTime: Long = 0
    private val ScanLogInterval = 500L
    private val SoundLogInterval = 1000L

    private fun permissionsGranted(): Boolean {
        for (permission in REQUIRED_PERMISSIONS) {
            if (ContextCompat.checkSelfPermission(
                    this, permission
                ) !== PackageManager.PERMISSION_GRANTED
            ) {
                return false
            }
        }
        return true
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
//        initVMDevice()//初始化设备,此时已经注册了ble监听，扫描到串口读取的设备就会连接

        if (!permissionsGranted()) {
            ActivityCompat.requestPermissions(this, REQUIRED_PERMISSIONS, REQUEST_CODE_PERMISSIONS)
        }
    }

    override fun initObserve() {
        observeLiveData(mViewModel.showVideoLiveData, ::processShowVideo)
    }

    private fun processShowVideo(isShowVideo: Boolean) {
        if (isShowVideo) {
            try {
                val cacheListJson = SpUtils.getString(SpKey.MODE_LIST, "")
                logD("processShowVideo cacheListJson: $cacheListJson", TAG)

                if (cacheListJson.isNotEmpty()) {
                    val cacheList: MutableList<ChooseModelItem> = try {
                        val type = object : TypeToken<List<ChooseModelItem>>() {}.type
                        GsonUtil.fromJson(cacheListJson, type)
                    } catch (e: Exception) {
                        val type = object : TypeToken<List<TranslateMode>>() {}.type
                        val oldList: List<TranslateMode> = GsonUtil.fromJson(cacheListJson, type)
                        oldList.map { ChooseModelItem(it) }.toMutableList()
                    }
                    if (!cacheList.any { it.mode == TranslateMode.VIDEO}) {
                        // 防止越界异常
                        val index = if (cacheList.filter { !it.isHidden }.size > 4) {
                            4
                        } else {
                            cacheList.filter { !it.isHidden }.size
                        }
                        cacheList.add(index, ChooseModelItem(TranslateMode.VIDEO))

                        val listJson = GsonUtil.toJson(cacheList)
                        SpUtils.put(SpKey.MODE_LIST, listJson)
                        logD("processShowVideo cache listJson: $listJson", TAG)
                    }
                    // 更新新数据
                    fullModeList.clear()
                    fullModeList.addAll(cacheList)
                } else {
                    if (!fullModeList.any { it.mode == TranslateMode.VIDEO}) {
                        // 防止越界异常
                        val index = if (fullModeList.filter { !it.isHidden }.size > 4) {
                            4
                        } else {
                            fullModeList.filter { !it.isHidden }.size
                        }
                        fullModeList.add(index, ChooseModelItem(TranslateMode.VIDEO))
                        val listJson = GsonUtil.toJson(fullModeList)
                        SpUtils.put(SpKey.MODE_LIST, listJson)
                        logD("processShowVideo default listJson: $listJson", TAG)
                    }
                }

                chooseModelAdapter.setList(fullModeList.filter { !it.isHidden })
                SpUtils.putBoolean(SpKey.IS_SHOW_VIDEO, true)

            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    private fun initAdapter() {
        val defaultList = mutableListOf(
            ChooseModelItem(TranslateMode.SIMUL),
            ChooseModelItem(TranslateMode.SPEAKER),
            ChooseModelItem(TranslateMode.SPEECH),
            ChooseModelItem(TranslateMode.KEYPAD),
            ChooseModelItem(TranslateMode.MEETING),
            ChooseModelItem(TranslateMode.SETTING),
            ChooseModelItem(TranslateMode.PHONE, true)
        )

        try {
            val cacheListJson = SpUtils.getString(SpKey.MODE_LIST, "")
            logD("initAdapter cacheListJson: $cacheListJson", TAG)

            if (cacheListJson.isNotEmpty()) {
                // 优先尝试解析为新数据结构
                try {
                    val type = object : TypeToken<List<ChooseModelItem>>() {}.type
                    fullModeList = GsonUtil.fromJson(cacheListJson, type)
                } catch (e: Exception) {
                    logE("保存列表解析成新结构失败", TAG)
                    // 解析失败，则尝试旧数据结构并转换
                    val type = object : TypeToken<List<TranslateMode>>() {}.type
                    val oldList: List<TranslateMode> = GsonUtil.fromJson(cacheListJson, type)
                    fullModeList = oldList.map { ChooseModelItem(it) }.toMutableList()
                }

                // 检查并添加 SPEECH 模式（如果不存在）
                if (fullModeList.none { it.mode == TranslateMode.SPEECH }) {
                    fullModeList.add(2, ChooseModelItem(TranslateMode.SPEECH))
                    val listJson = GsonUtil.toJson(fullModeList)
                    SpUtils.put(SpKey.MODE_LIST, listJson)
                    logD("initAdapter listJson: $listJson", TAG)
                }

                // 首次更新到新版本，隐藏 PHONE
                if (SpUtils.getBoolean(SpKey.MODE_LIST_REMOVE_PHONE, true)) {
                    fullModeList.removeIf { it.mode == TranslateMode.PHONE }
                    fullModeList.add(ChooseModelItem(TranslateMode.PHONE, true))
                    val listJson = GsonUtil.toJson(fullModeList)
                    SpUtils.put(SpKey.MODE_LIST, listJson)
                    SpUtils.putBoolean(SpKey.MODE_LIST_REMOVE_PHONE, false)
                }

            } else {
                fullModeList = defaultList
            }
        } catch (e: Exception) {
            e.printStackTrace()
            fullModeList = defaultList // 如果出现任何异常，则使用默认列表
        }

        chooseModelAdapter = ChooseModelAdapter(fullModeList) { list ->
            // 保存列表状态到SpUtils
            val listJson = GsonUtil.toJson(list)
            SpUtils.put(SpKey.MODE_LIST, listJson)
            logD("点击添加或隐藏，保存列表状态: $listJson", TAG)
        }
        mBinding.vRecycleView.adapter = chooseModelAdapter
    }

    override fun HomeActivityMainBinding.initView() {
        initAdapter()

        //进入home页面后初始化sip
        var url: String
        when(SpUtils.getString(SpKey.BASE_URL, NetUrl.RELEASE_URL)) {
            NetUrl.RELEASE_URL -> {
                url = ConController.init(1)
            }

            NetUrl.TEST_URL_X1_NEW -> {
                url = ConController.init(2)
            }

            NetUrl.DEV_URL -> {
                url = ConController.init(3)
            }

            else -> {
                url = ConController.init(2)
            }
        }

        logD("sip服务地址为: $url", TAG)

//        CallManager.init()

        mViewModel.init(this@HomeActivityMain)
        mViewModel.registerReceiver()
        initHeadsetState()

        val itemTouchHelper = ItemTouchHelper(object : ItemTouchHelper.Callback() {

            override fun getMovementFlags(
                recyclerView: RecyclerView, viewHolder: RecyclerView.ViewHolder
            ): Int {
                return makeMovementFlags(
                    ItemTouchHelper.UP or ItemTouchHelper.DOWN or ItemTouchHelper.START or ItemTouchHelper.END,
                    0
                )
            }

            override fun onMove(
                recyclerView: RecyclerView,
                oldHolder: RecyclerView.ViewHolder,
                targetHolder: RecyclerView.ViewHolder
            ): Boolean {
                if (!chooseModelAdapter.isEditMode) return false

                val fromPosition = oldHolder.adapterPosition
                val toPosition = targetHolder.adapterPosition

                // 找到第一个隐藏图标的索引作为边界
                val firstHiddenIndex = fullModeList.indexOfFirst { it.isHidden }

                // 如果存在隐藏图标，则不允许将可见图标拖入隐藏区域
                if (firstHiddenIndex != -1 && toPosition >= firstHiddenIndex) {
                    return false
                }

                // 在完整列表（fullModeList）中移动项
                Collections.swap(fullModeList, fromPosition, toPosition)

                // 通知adapter项已移动
                chooseModelAdapter.notifyItemMoved(fromPosition, toPosition)

                // 保存更新后的列表到SharedPreferences
                val listJson = GsonUtil.toJson(fullModeList)
                Log.d(TAG, "listJson after move: $listJson")
                SpUtils.put(SpKey.MODE_LIST, listJson)

                return true
            }

            override fun onChildDrawOver(
                c: Canvas,
                recyclerView: RecyclerView,
                viewHolder: RecyclerView.ViewHolder?,
                dX: Float,
                dY: Float,
                actionState: Int,
                isCurrentlyActive: Boolean
            ) {
                super.onChildDrawOver(
                    c, recyclerView, viewHolder, dX, dY, actionState, isCurrentlyActive
                )
                if (actionState == ItemTouchHelper.ACTION_STATE_DRAG && !isCurrentlyActive) {
                    (viewHolder as MyViewHolder).let {
                        Log.d(TAG, "onChildDrawOver")
                        it.getView<ConstraintLayout>(com.timekettle.module_home.R.id.rootLayout).background =
                            getDrawable(com.timekettle.module_home.R.mipmap.home_mode_img_bg_def)
                    }
                }
            }

//            override fun onSelectedChanged(viewHolder: RecyclerView.ViewHolder?, actionState: Int) {
//                super.onSelectedChanged(viewHolder, actionState)
//                if (actionState == ItemTouchHelper.ACTION_STATE_IDLE) {
//                    holder?.let {
//                        Log.d("sdfqweqwerqwegg3","ACTION_STATE_IDLE")
//                        it.getView<ConstraintLayout>(com.timekettle.module_home.R.id.rootLayout).background = getDrawable(com.timekettle.module_home.R.mipmap.home_mode_img_bg_def)
//                    }
//                }
//            }

            override fun onSwiped(viewHolder: RecyclerView.ViewHolder, direction: Int) {}

            override fun canDropOver(
                recyclerView: RecyclerView,
                current: RecyclerView.ViewHolder,
                target: RecyclerView.ViewHolder
            ) = true

            override fun isLongPressDragEnabled() = false
        })

        itemTouchHelper.attachToRecyclerView(vRecycleView)

        chooseModelAdapter.setOnItemLongClickListener { _, _, position ->
            if (isInEditMode) {
                val holder = vRecycleView.findViewHolderForAdapterPosition(position) as MyViewHolder
                holder.getView<ConstraintLayout>(com.timekettle.module_home.R.id.rootLayout).background =
                    getDrawable(com.timekettle.module_home.R.mipmap.home_mode_img_bg_sel)
                itemTouchHelper.startDrag(holder)
            } else {
                enterEditMode()
            }
            true
        }

        chooseModelAdapter.setOnItemClickListener { adapter, _, position ->
            if (isInEditMode) return@setOnItemClickListener
            val hasAgreePolicy = SpUtils.getBoolean(SpKey.AGREE_POLICY, true)
            val hasCert = DeviceUtil.hasCert()
            //点击跳转不同的模式页面
            when ((adapter as ChooseModelAdapter).data[position].mode) {
                TranslateMode.SIMUL -> { //一对一
                    meetingId = ""
                    if (hasAgreePolicy) {
                        // 去证书下载
                        if (!hasCert) {
                            if (NetworkUtils.isConnected()) {
                                startActivityByRoute(RouteUrl.Home.CertificateActivity)
                            } else {
                                startActivity(Intent(Settings.ACTION_WIFI_SETTINGS))
                            }
                        } else {
                            val isNeed = SpUtils.getBoolean(SIMUL_NO_LONGER_PROMPT_MIN, true)
                            if (isNeed) {
                                startKtxActivity<SimulActivityGuide>()
                            } else {
                                startActivityByRoute(RouteUrl.Translate.TranslateActivityHome)
                            }
                        }
                    } else {
                        ARouter.getInstance()
                            .build(RouteUrl.Main.PolicyActivity)
                            .withInt(CommIntentKey.POLICY_TO_TYPE, 0)
                            .navigation()
                    }
                }

                TranslateMode.SPEAKER -> { //访谈
                    meetingId = ""
                    if (hasAgreePolicy) {
                        // 去证书下载
                        if (!hasCert) {
                            if (NetworkUtils.isConnected()) {
                                startActivityByRoute(RouteUrl.Home.CertificateActivity)
                            } else {
                                startActivity(Intent(Settings.ACTION_WIFI_SETTINGS))
                            }
                        } else {
                            val isNeed = SpUtils.getBoolean(INTERVIEW_NO_LONGER_PROMPT_MIN, true)
                            if (isNeed) {
                                startKtxActivity<InterviewActivityGuide>()
                            } else {
                                startActivityByRoute(RouteUrl.Translate.InterviewActivityHome)
                            }
                        }
                    } else {
                        ARouter.getInstance()
                            .build(RouteUrl.Main.PolicyActivity)
                            .withInt(CommIntentKey.POLICY_TO_TYPE, 1)
                            .navigation()
                    }
                }

                TranslateMode.KEYPAD -> { //一键
                    meetingId = ""
                    if (hasAgreePolicy) {
                        // 去证书下载
                        if (!hasCert) {
                            if (NetworkUtils.isConnected()) {
                                startActivityByRoute(RouteUrl.Home.CertificateActivity)
                            } else {
                                startActivity(Intent(Settings.ACTION_WIFI_SETTINGS))
                            }
                        } else {
                            val isNeed = SpUtils.getBoolean(KEYPAD_NO_LONGER_PROMPT_MIN, true)
                            if (isNeed) {
                                startKtxActivity<KeypadActivityGuide>()
                            } else {
                                startActivityByRoute(RouteUrl.Translate.KeypadActivityMain)
                            }
                        }
                    } else {
                        ARouter.getInstance()
                            .build(RouteUrl.Main.PolicyActivity)
                            .withInt(CommIntentKey.POLICY_TO_TYPE, 2)
                            .navigation()
                    }
                }

                TranslateMode.VIDEO -> { //音视频
                    meetingId = ""
//                    val isNeed = SpUtils.getBoolean(INTERVIEW_NO_LONGER_PROMPT_MIN, true)
//                    if (isNeed) {
//                        startKtxActivity<InterviewActivityGuide>()
//                    } else {
//                        startActivityByRoute(RouteUrl.Translate.VideoActivityMain)
//                    }
//                    startActivityByRoute(RouteUrl.Translate.DongleUpgradeActivity)
                    if (hasAgreePolicy) {
                        // 去证书下载
                        if (!hasCert) {
                            if (NetworkUtils.isConnected()) {
                                startActivityByRoute(RouteUrl.Home.CertificateActivity)
                            } else {
                                startActivity(Intent(Settings.ACTION_WIFI_SETTINGS))
                            }
                        } else {
                            if (!DongleVersionUtil.versionEquals(
                                    DongleVersionUtil.DONGLE_FILE_PATH,
                                    SpUtils.getString(SpKey.DONGLE_VERSION, "")
                                )
                            ) {
                                startActivityByRoute(RouteUrl.Translate.DongleUpgradeActivity)
                            } else if (SpUtils.getBoolean(VIDEO_NO_LONGER_PROMPT_MIN, true)) {
                                startKtxActivity<VideoActivityGuide>()
                            } else {
                                startActivityByRoute(RouteUrl.Translate.VideoActivityConnect)
                            }
                        }
                    } else {
                        ARouter.getInstance()
                            .build(RouteUrl.Main.PolicyActivity)
                            .withInt(CommIntentKey.POLICY_TO_TYPE, 3)
                            .navigation()
                    }
                }

                TranslateMode.SPEECH -> { //演讲翻译
                    meetingId = ""
                    if (hasAgreePolicy) {
                        // 去证书下载
                        if (!hasCert) {
                            if (NetworkUtils.isConnected()) {
                                startActivityByRoute(RouteUrl.Home.CertificateActivity)
                            } else {
                                startActivity(Intent(Settings.ACTION_WIFI_SETTINGS))
                            }
                        } else {
                            val isNeed = SpUtils.getBoolean(SPEECH_NO_LONGER_PROMPT_MIN, true)
                            if (isNeed) {
                                startKtxActivity<SpeechActivityGuide>()
                            } else {
                                startActivityByRoute(RouteUrl.Translate.SpeechActivityHome)
                            }
                        }
                    } else {
                        ARouter.getInstance()
                            .build(RouteUrl.Main.PolicyActivity)
                            .withInt(CommIntentKey.POLICY_TO_TYPE, 4)
                            .navigation()
                    }
                }

                TranslateMode.PHONE -> { //电话

                    meetingId = ""
                    if (hasAgreePolicy) {
                        // 去证书下载
                        if (!hasCert) {
                            if (NetworkUtils.isConnected()) {
                                startActivityByRoute(RouteUrl.Home.CertificateActivity)
                            } else {
                                startActivity(Intent(Settings.ACTION_WIFI_SETTINGS))
                            }
                        } else {
                            val isNeed = SpUtils.getBoolean(PHONE_NO_LONGER_PROMPT_MIN, true)
                            if (isNeed) {
                                startKtxActivity<PhoneActivityGuide>()
                            } else {
                                startActivityByRoute(RouteUrl.Translate.PhoneActivityHome)
                            }
                        }
                    } else {
                        ARouter.getInstance()
                            .build(RouteUrl.Main.PolicyActivity)
                            .withInt(CommIntentKey.POLICY_TO_TYPE, 5)
                            .navigation()
                    }
                }

                TranslateMode.MEETING -> {  //会议
                    meetingId = ""

                    if (hasAgreePolicy) {
                        // 去证书下载
                        if (!hasCert) {
                            if (NetworkUtils.isConnected()) {
                                startActivityByRoute(RouteUrl.Home.CertificateActivity)
                            } else {
                                startActivity(Intent(Settings.ACTION_WIFI_SETTINGS))
                            }
                        } else {
                            val isNeed = SpUtils.getBoolean(MEETING_NO_LONGER_PROMPT_MIN, true)
                            if (isNeed) {
                                startActivityByRoute(RouteUrl.Translate.MeetingActivityGuide)
                            } else {
                                startActivityByRoute(RouteUrl.Translate.MeetingActivityHome)
                            }
                        }
                    } else {
                        ARouter.getInstance()
                            .build(RouteUrl.Main.PolicyActivity)
                            .withInt(CommIntentKey.POLICY_TO_TYPE, 6)
                            .navigation()
                    }

//                    TransServiceImplWrap.setPhoneMode(false)
                }

                TranslateMode.SETTING -> { //设置
                    meetingId = ""
                    startActivityByRoute(RouteUrl.Setting.SettingActivityMain)

                    SensorsUtil.trackEvent(SensorsCustomEvent.X1_Settings.name, null)
                }

                else -> {}
            }
        }


        bleInit()
        keyboardUtil = KeyboardUtil(this@HomeActivityMain)
        TransServiceImplWrap.initService()  // 初始化翻译模块的离线管理

        if (SpUtils.getBoolean(SpKey.SHOULD_MOVE_GUIDE_SHOW, true)) {
            pagMoveGuide.apply {
                composition = PAGFile.Load(assets, "ani_home_guide_hold_bmp.pag")
                setRepeatCount(0)
                setScaleMode(PAGScaleMode.Zoom)
            }
            clMoveGuide.setOnClickListener {
                SpUtils.putBoolean(SpKey.SHOULD_MOVE_GUIDE_SHOW, false)
                pagMoveGuide.stop()
                clMoveGuide.gone()
            }
            clMoveGuide.visible()
            pagMoveGuide.play()
        }
    }

    private fun bleInit() {
        //ble设备的监听
        myBleListener = object : BleUtil.Listener {
            override fun dispatchEvent(type: BleUtil.BleEventName?, perip: RawBlePeripheral?) {
                when (type) {
                    BleUtil.BleEventName.BleDidStatusUpdate -> {
                    }

                    BleUtil.BleEventName.BleConnectStandby -> {
                    }

                    BleUtil.BleEventName.BleDisconnectedPeripheral -> {
                    }

                    BleUtil.BleEventName.BleDisconnectedSubPeripheral -> {}
                    BleUtil.BleEventName.BleConnectedSubPeripheral -> {}
                    BleUtil.BleEventName.BleProtocolChanged -> {}
                    BleUtil.BleEventName.BleStError -> {}

                    else -> {}
                }

            }

            override fun onBluetoothStateUpdate(state: Int) {
                if (state == BluetoothAdapter.STATE_OFF) {
                    logD("蓝牙状态Change：系统蓝牙关闭", TAG)
                    lifecycleScope.launch {
                        requestBTEnable()
                    }
                } else if (state == BluetoothAdapter.STATE_ON) {
                    logD("蓝牙状态Change：系统蓝牙开启", TAG)
                    checkPermissionBtLocation()
                }
            }

        }

        BleUtil.shared.setLogLevel(2)
        BleUtil.shared.setLogCallback { level, tag, msg, _ ->
            when (level) {
                1 -> {
                    logE(msg, tag)
                }
                2 -> {
                    if (msg.contains("onScanResult:")) {
                        val currentTime = System.currentTimeMillis()
                        if (currentTime - lastScanTime >= ScanLogInterval) {
                            lastScanTime = currentTime
                            logD(msg, tag)
                        }
                    } else if (msg.contains("Left sendSound in")) {
                        val currentTime = System.currentTimeMillis()
                        if (currentTime - lastLeftSoundTime >= SoundLogInterval) {
                            lastLeftSoundTime = currentTime
                            logD(msg, tag)
                        }
                    } else if (msg.contains("Right sendSound in")) {
                        val currentTime = System.currentTimeMillis()
                        if (currentTime - lastRightSoundTime >= SoundLogInterval) {
                            lastRightSoundTime = currentTime
                            logD(msg, tag)
                        }
                    } else {
                        logD(msg, tag)
                    }
                }
            }
        }

        BleUtil.shared.addListener(myBleListener)

        lifecycleScope.launch(Dispatchers.Main) {
            delay(500)
            checkPermissionBtLocation()
        }

    }

    private fun checkPermissionBtLocation() {
        val permissions = mutableListOf<String>()
        permissions.add(Permission.ACCESS_FINE_LOCATION)
        permissions.add(Permission.ACCESS_COARSE_LOCATION)
        permissions.addAll(Permission.Group.BLUETOOTH)
        XXPermissions.with(ActivityUtils.getTopActivity()).permission(permissions)
            .request(object : OnPermissionCallback {
                override fun onGranted(permissions: MutableList<String>, all: Boolean) {
                    if (all) {
                       HallScanManager.judgeStartOrStopBleScan()
                    } else {
                        logE("部分权限未获取成功 $permissions")
                    }
                }

                override fun onDenied(permissions: MutableList<String>, never: Boolean) {
                    logE("定位权限获取失败 $permissions never:$never")
                }
            })
    }

    private fun requestBTEnable() {
        val bluetoothManager = this.getSystemService(Context.BLUETOOTH_SERVICE) as BluetoothManager
        val bluetoothAdapter = bluetoothManager.adapter
        if (bluetoothAdapter.isEnabled) return
        val intent = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
        btForResult.launch(intent)
    }

    private val btForResult =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result: ActivityResult ->
            if (result.resultCode == Activity.RESULT_OK) {
                logD("蓝牙开启成功", TAG)
                checkPermissionBtLocation()
            } else {
                logD("蓝牙开启失败", TAG)
            }
        }

    private lateinit var keyboardUtil: KeyboardUtil
    private var dstCode: String = ""

    @SuppressLint("ClickableViewAccessibility")
    override fun initListener() {
        mBinding.llChooseLanguage.setOnClickListener {
            try {
                intent = Intent("android.settings.LANGUAGE_SETTINGS")
                startActivity(intent)
            } catch (e: Exception) {
                showDebugToast("打开系统语言设置页面失败!")
            }
        }

        mBinding.llChooseLanguage.setOnLongClickListener {
            if (SpUtils.getBoolean(SpKey.IS_DEBUG_STATUS, false) || BuildConfig.DEBUG) {
                startActivityByRoute(RouteUrl.Comm.ControlPanel)
            }
            true
        }
        addDeviceView(mBinding.ctStatusBar)
        addWifiBatteryView(mBinding.ctStatusBar)

        mBinding.hotClickArea.setOnClickListener {
            val hasAgreePolicy = SpUtils.getBoolean(SpKey.AGREE_POLICY, true)
            if (hasAgreePolicy) {
                startActivity(Intent(Settings.ACTION_WIFI_SETTINGS))
            } else {
                ARouter.getInstance()
                    .build(RouteUrl.Main.PolicyActivity)
                    .withInt(CommIntentKey.POLICY_TO_TYPE, 7)
                    .navigation()
            }
        }

        mBinding.editDone.setOnClickListener {
            exitEditMode()
        }

        keyboardUtil.setOnConnectBtnClickListener { content ->
            SpUtils.putString(DEVICE_MAC_NUM, content)
        }

        //电话注册回调
        regListener = { code, msg ->
            runOnUiThread {
                if (code == 200) {
                    showDebugToast("注册成功")
                    logD("注册状态回调：注册成功", TAG)
                } else {
                    showDebugToast("注册失败")
                    logD("注册状态回调：注册失败，code = $code", TAG)
                }
            }
        }

        CallManager.onIncomingCallBack = { call, msg ->
            runOnUiThread {
                Log.d("asdfasdifwierewrg", "msg $msg")
                //2.01.13版本之前，dstCode是在上面获取，这是2.01.13版本之后加的
                if (msg.contains("rLANG: ") && msg.contains("Content-Type")) {
                    dstCode = msg.substring(
                        msg.indexOf("rLANG: ") + "rLANG: ".length,
                        msg.indexOf("Content-Type")
                    ).trim()
                    logD("来电语言code(新) $dstCode", TAG)
                } else {
                    dstCode = ""
                    logD("来电语言code(新)不正确 空", TAG)
                }

                if (HomeServiceImplWrap.isInMode()) {
                    CallManager.deleteCall(call)
                    logD("来电，当前正在模式中，主动挂断", TAG)
                } else {
                    CallManager.updateCall(call) {
                        TransServiceImplWrap.setCallType(1)
                    }
                }
            }
        }

        //电话拨号状态回调
        callStatusListener = { state, role, stateText, lastReason, remoteUri, localUri ->

            runOnUiThread {

//                val tag = localUri.substring(0, localUri.indexOf("<sip:"))
                val callType = TransServiceImplWrap.getCallType()

                logD("callStatusListener: callType=$callType", TAG)

                if (callType == 1) {

                    HomeServiceImplWrap.savePhoneState(state)

//                    logD("电话状态回调：${state}+${role}+${stateText}+${lastReason}+${remoteUri}")
                    logD("电话状态回调：$state", TAG)

                    if (state < PJSIP_INV_STATE_CONFIRMED) {

                        confirmed = false

                        if (role == 0) { // 0是去电
                            if (state == 1) {

                            } else if (state == 4) {

                            }
                        } else { // 1是来电
                            if (state == 3) { // 响铃

                                if (!MyActivityUtil.isUpgradingAty()) {

                                    hungUpRing?.stop()
                                    callRing = RingtoneManager.getRingtone(
                                        BaseApp.application, PhoneUtil.getRing("Champagne Edition")
                                    )
                                    callRing?.play()

                                    Log.d("remoteUri: $remoteUri", TAG)
                                    val s = remoteUri.split(" ")
                                    if (s.size > 1) {
                                        account =
                                            s[1].substring(s[1].indexOf(":") + 1, s[1].indexOf("@"))
                                        val code = s[0].replace(
                                            "\"",
                                            ""
                                        ) //兼容旧版本，2.01.13版本(含)之前，dstCode是在这里获取
                                        if (code.contains("-")) {
                                            dstCode = code
                                            logD("来电语言code(旧): $dstCode", TAG)
                                        } else {
                                            logD("来电语言code(旧)不正确: $dstCode", TAG)
                                        }
                                    }

                                    ARouter.getInstance()
                                        .build(RouteUrl.Translate.PhoneActivityComing)
                                        .withString(IntentKey.OtherLang, dstCode)
                                        .withString(IntentKey.Account, account).navigation()

                                }

                            } else if (state == 4) {
//                                //点击接通瞬间回调
//                                //发送给对方我正在使用的语言
//                                val selfLang = TransServiceImplWrap.getPhoneModelLang()
//                                CallManager.sendSip(ReqEntity(111, selfLang))
                            }
                        }

                    } else if (state == PJSIP_INV_STATE_CONFIRMED) {

                        confirmed = true

                        callRing?.stop()

                        lifecycleScope.launch {

                            val callRecord: CallRecordEntity

                            if (role == 0) { // 去电
                                callRecord = CallRecordEntity(
                                    Date(), TransServiceImplWrap.getAccount(), 0, 0
                                )

                                if (TransServiceImplWrap.getAccount().isNotBlank()) {
                                    insertOrUpdateRecord(
                                        TransServiceImplWrap.getAccount(),
                                        callRecord
                                    )
                                }
                            } else { //来电
                                callRecord = CallRecordEntity(Date(), account, 1, 0)

                                if (account.isNotBlank()) {
                                    insertOrUpdateRecord(account, callRecord)
                                }

                                //发送给对方我正在使用的语言
                                val selfLang = TransServiceImplWrap.getPhoneModelLang()
                                CallManager.sendSip(ReqEntity(111, selfLang))
                                logD("电话状态回调: 发送语言code=$selfLang", TAG)

                                ARouter.getInstance().build(RouteUrl.Translate.PhoneActivityMain)
                                    .withString(IntentKey.OtherLang, dstCode)
                                    .withString(IntentKey.Account, callRecord.phoneNumber)
                                    .navigation()
                            }

                        }

                    } else if (state == PJSIP_INV_STATE_DISCONNECTED) {

                        callRing?.stop()

                        var callRecord: CallRecordEntity? = null
                        var phoneNumber = ""

                        when (lastReason) {
                            PhoneState.DECLINE.state -> {
                                //取消拨打
                                if (role == 0) { //去电
                                    if (!confirmed) {
                                        showToast(getString(R.string.translate_phone_cancel_dialing))
                                        phoneNumber = TransServiceImplWrap.getAccount()
                                        callRecord = CallRecordEntity(
                                            Date(), TransServiceImplWrap.getAccount(), 0, 1
                                        )
                                    } else {
                                        showToast(getString(R.string.translate_phone_hung_up))
                                    }
                                } else { //来电
                                    showToast(getString(R.string.translate_phone_hung_up))
                                    if (!confirmed) {
                                        phoneNumber = account
                                        callRecord = CallRecordEntity(Date(), account, 1, 1)
                                    }
                                }
                            }

                            PhoneState.DECLINED.state -> { //去电
                                //电话无人接听
                                showToast(getString(R.string.translate_phone_unanswered))
                                phoneNumber = TransServiceImplWrap.getAccount()
                                callRecord = CallRecordEntity(
                                    Date(), TransServiceImplWrap.getAccount(), 0, 1
                                )
                            }

                            PhoneState.FORBIDDEN.state -> { //去电
                                lifecycleScope.launch {
                                    hungUpRing = RingtoneManager.getRingtone(
                                        BaseApp.application, PhoneUtil.getRing("Hydra")
                                    )
                                    hungUpRing?.play()

                                    delay(2000)

                                    hungUpRing?.stop()
                                }

                                //对方正忙
                                showToast(getString(R.string.translate_phone_busy))
                                phoneNumber = TransServiceImplWrap.getAccount()
                                callRecord = CallRecordEntity(
                                    Date(), TransServiceImplWrap.getAccount(), 0, 1
                                )
                            }

                            PhoneState.REQUEST_TERMINATED.state -> { //来电
                                //请求终止
//                                showToast("对方取消拨打")
                                showToast(getString(R.string.translate_phone_hungup))
                                phoneNumber = account
                                callRecord = CallRecordEntity(Date(), account, 1, 1)
                                EventBusUtils.postEvent(PhoneStateEvent(state))
                            }

                            PhoneState.NORMAL_CALL_CLEARING.state -> {
                                //对方已挂断
                                showToast(getString(R.string.translate_phone_hungup))
                            }

                            PhoneState.TEMPORARILY_UNAVAILABLE.state -> { //去电
                                //拨打的号码不存在
                                lifecycleScope.launch {
                                    delay(3000)
                                    showToast(getString(R.string.translate_phone_not_exist))
                                    callRecord = CallRecordEntity(
                                        Date(), TransServiceImplWrap.getAccount(), 0, 1
                                    )
                                    callRecord?.let {
                                        if (it.phoneNumber.isNotBlank()) {
                                            lifecycleScope.launch {
                                                insertOrUpdateRecord(
                                                    TransServiceImplWrap.getAccount(), it
                                                )
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        callRecord?.let {
                            if (it.phoneNumber.isNotBlank()) {
                                lifecycleScope.launch {
                                    insertOrUpdateRecord(phoneNumber, it)
                                }
                            }
                        }

                    }
                } else if (callType == 2) {

                    logD("$state $role $stateText $lastReason", TAG)

                    if (role == 1 || meetingId.isBlank()) {
                        logD("role: $role", TAG)
                        logD("meetingId: $meetingId", TAG)
                        return@runOnUiThread
                    }

                    if (state < PJSIP_INV_STATE_CONFIRMED) {//还没接通
//                        changeStateView(loading = true)
                        showDebugToast("接通中...")
                        logD("接通中...", TAG)

                    } else if (state >= PJSIP_INV_STATE_CONFIRMED) {
//                        changeStateView(hide = true)

                        if (state == PJSIP_INV_STATE_CONFIRMED) {

                            //接通以后保存当前的会议室信息,为了挂断以后将会议室清空
//                            val meetingBean = MeetingBean(meetingID, password)
//                            MeetingManager.saveMeeting(meetingBean)

                            //接通了会议
                            if (HomeServiceImplWrap.getSipAccount() == null) {
                                showDebugToast("账号注册失败，请退出当前页面，重新进入")
                                return@runOnUiThread
                            }

                            //接通
                            showDebugToast("已接通")

                            logD("当前会议使用的IP为: ${HomeServiceImplWrap.getSipAccount()?.registrar} 会议号为: $meetingId", TAG)

                            //拨号成功后跳转
                            IntentHelper.addObjectForKey(
                                HomeServiceImplWrap.getSipAccount(), IntentKey.SipAccount
                            )
                            ARouter.getInstance().build(RouteUrl.Translate.MeetingActivityMain)
                                .withString(IntentKey.MeetingID, meetingId)
                                .withString(IntentKey.MeetingPassword, password)
                                .withString(IntentKey.MeetingType, "1").navigation()

                            meetingId = ""
                            password = ""

//                            logD("MeetingBaseVm：密码$password")

                            //在进入main之后取消之前所有的监听，main里面的监听有单独的作用
//                        if (callStatusListener!=null){
//                            ConController.removeCallStateBackListener(callStatusListener)
//                        }

//                            isCallSuccess = true//标识当前已经打通过

                        } else if (state == PJSIP_INV_STATE_DISCONNECTED) {
                            //断开连接，无论是主动断开还是被动断开
                            showDebugToast("已断开")
                        }
                    }
                }
            }
        }

        ConController.addCallRegBackListener(regListener)
        ConController.addCallStateBackListener(callStatusListener)
    }

    private suspend fun insertOrUpdateRecord(phoneNumber: String, record: CallRecordEntity) {
//        val callRecord = callRecordDao.findCallRecordByPhoneNumber(phoneNumber)
//        if (callRecord == null) {
        if (SpUtils.getBoolean(SpKey.IS_SAVE_RECORD, true)) {
            callRecordDao.insert(record)
        }
//        } else {
//            val tempRecord = CallRecordEntity(
//                record.date,
//                callRecord.phoneNumber,
//                callRecord.phoneType,
//                record.phoneState,
//                callRecord.phoneMessages
//            )
//            callRecordDao.update(tempRecord)
//            Log.d("asdfasdfasdfasdfweweg", "update")
//        }
    }

    @RequiresApi(Build.VERSION_CODES.O)
    override fun initRequestData() {
        mViewModel.startRepeatFirmCheckJob(this@HomeActivityMain)
        SettingServiceImplWrap.startReadInfoJob()
        BaseApp.mCoroutineScope.launch {
            DongleOtaUtil.setDongleOff()
        }

    }

    override fun onResume() {
        super.onResume()
        // 回到首页时手动更新语言信息
        mBinding.tvSystemLan.text = getLanguageDisplayName()
        setLedLevel(this, 0)
        Log.d("asfasdfjkasdfasdf", "onResume")
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onNFCSend(event: MeetingEvent) {
        meetingId = event.meetingId
        password = event.password
        Log.d(TAG, "meetingId $meetingId")
        Log.d(TAG, "password $password")

//        TransServiceImplWrap.setMeetingId(meetingId)
//        TransServiceImplWrap.setPassword(password)

        HomeServiceImplWrap.getSipAccount()?.let {
            logD("onNFCSend", TAG)
//            mViewModel.createConference(it)

            logD("isBlank: ${meetingId.isBlank()}", TAG)

            if (meetingId.isBlank()) return

            ConController.verifyConference(
                DeviceTool.getSerialNumber(), meetingId, password, lifecycleScope
            ) { code, s ->
                logD("verifyConference: code=$code message=$s", TAG)
                if (code == Code.SUCCESS) {
                    HomeServiceImplWrap.getSipIp()?.let {
                        runOnUiThread {
                            val uri = "sip:$meetingId$password@${it.ip}"
                            logD("makeCall: uri=$uri", TAG)
                            TransServiceImplWrap.setCallType(2)
                            CallManager.makeCall(uri, DeviceManager.getLastlyMeetingUseLanguage())
                        }
                    }
                } else {
                    if (s.isNotEmpty()) {
                        showToast(s)
                    } else {
                        showToast(BaseApp.context.getString(R.string.translate_meeting_verify_fail))
                    }
                }
            }

            logD("ip: ${HomeServiceImplWrap.getSipIp()?.ip}", TAG)
        }
    }

//    @Subscribe(threadMode = ThreadMode.MAIN)
//    fun onSipRegister(event: SipRegisterEvent) {
//        logD("注册电话账号", TAG)
//        mViewModel.getSipAccount()
//    }

    @RequiresApi(Build.VERSION_CODES.O)
    @Subscribe(threadMode = ThreadMode.POSTING)
    fun onHeadsetUpdateCheck(event: CheckHeadsetUpdateEvent) {
        logD("收到闹钟事件，检测耳机升级更新Event", TAG)
        BaseApp.mCoroutineScope.launch(Dispatchers.IO) {
            mViewModel.reqOtaUpdateMsg()
        }
    }

    // region 事件订阅
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onOtaReady(event: OtaReadyEvent) {
        logD("OTA升级Event: $event", TAG)
        if (!MyActivityUtil.canShowUpgradeDialog()) {
            logD("当前不在主页，不弹升级弹窗", TAG)
            return
        }  // 不在主页界面，不弹窗
        if (headsetOtaDialog?.isShowing == true) return
        headsetOtaDialog = CountdownDialog(this@HomeActivityMain).apply {
            cancelText = getString(R.string.common_ignore)
            confirmText = getString(R.string.device_upgrade_now)
            content = getString(R.string.device_will_auto_upgrade).replace("XXX", "10")
                .replace("YYY", "20")
            confirmTodo = {
                ARouter.getInstance().build(RouteUrl.Setting.SettingActivityUpgrade)
                    .withString(RouteKey.DFU_MSG, GsonUtils.toJson(event.dfuMsg)).navigation()
            }
        }
        headsetOtaDialog?.let {
            it.show()
            it.setOnDismissListener {
                headsetOtaDialog = null
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.POSTING)
    fun onCertificateUpdate(event: CertificateUpdateEvent) {
        logD("收到事件，证书认证成功", TAG)
        mViewModel.requestAll()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onDownloadEvent(event: DownloadMultipleFileEvent) {
        when (event.status) {
            DownloadStatus.SUCCESS -> {
                logD("下载事件 SUCCESS", TAG)
            }

            DownloadStatus.FAIL -> {
                logD("下载事件 FAIL 离线包下载失败", TAG)
                if (!AppUtils.isAppForeground()) {
                    logD("下载事件 当前app不在前台", TAG)
                    return
                }
                lifecycleScope.launch(Dispatchers.Main) {
                    DialogFactory.createTipsDialog(
                        ActivityUtils.getTopActivity(),
                        titleText = getString(com.timekettle.upup.comm.R.string.common_alert_tip),
                        content = getString(R.string.trans_pkg_download_fail_because_net),
                        sureText = getString(com.timekettle.upup.comm.R.string.common_confirm),
                        onConfirmListener = { },
                        cancelable = true
                    ).show()
                }
            }

            else -> {}
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onUnZipTryAuthResultEvent(event: UnZipTryAuthResultEvent) {
        if (event.isSuccess) {
            val codeText = TransServiceImplWrap.getStyledTextByCode(event.code)
            val text =
                getString(R.string.mine_xxx_lan_pair_download_finish).replace("XXX", codeText)
            showToast(text)
            logE("解压+鉴权 成功！${event.code}", TAG)
        } else {
            logE("解压/鉴权 失败！$event", TAG)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onDevicePopShow(event: ShowDeviceDialogListEvent) {
        logD("ShowDeviceDialogListEvent $event 弹窗显示需要连接设备", TAG)
        val needCount = if (event.model == TranslateMode.SIMUL) 2 else 1
        if (event.show) {
            if (x1NeedConnectDialog?.isShowing == true) return
            x1NeedConnectDialog =
                DeviceNeedConnectDialog(ActivityUtils.getTopActivity(), needCount).apply {
                    connectDoneToDo = event.connectDoneToDo
                    connectOneToDo = event.connectOneToDo
                    onDismissListener = object : OnDismissListener() {
                        override fun onDismiss() {
                            x1NeedConnectDialog = null
                        }
                    }
                    showPopupWindow()
                }
        } else {
            x1NeedConnectDialog?.dismiss(false)
        }
    }

    override fun onDestroy() {
        BleUtil.shared.removeListener(myBleListener)
        ConController.removeCallRegBackListener(regListener)
        ConController.removeCallStateBackListener(callStatusListener)
        CallManager.onIncomingCallBack = null
        chooseModelAdapter.setOnItemClickListener(null)
        chooseModelAdapter.setOnItemLongClickListener(null)
        regListener = null
        callStatusListener = null
        myBleListener = null
        mViewModel.unRegisterReceiver()
        headsetOtaDialog?.dismiss()
        headsetOtaDialog = null
        x1NeedConnectDialog?.dismiss()
        x1NeedConnectDialog = null
        super.onDestroy()
    }

    companion object {
        private const val TAG = "HomeActivityMain"
        private const val PJSIP_INV_STATE_CONFIRMED = 5
        private const val PJSIP_INV_STATE_DISCONNECTED = 6
    }

    /**
     * 首次启动页面时，检测耳机的状态，如果不在仓内，显示感叹号
     */
    private fun initHeadsetState() {
        lifecycleScope.launchWhenResumed {
            if (HallUtil.isLeftOut()) vmTopDevice.updateDeviceState(true, X1Status.Broken.apply {
                desc = BrokenReason.HeadsetOutWhenStartApp.reasonDesc
            })
            if (HallUtil.isRightOut()) vmTopDevice.updateDeviceState(false, X1Status.Broken.apply {
                desc = BrokenReason.HeadsetOutWhenStartApp.reasonDesc
            })
        }
    }

    override fun onBackPressed() {
        Log.d(TAG, "onBackPressed")

        if (mBinding.clMoveGuide.isVisible) {
            SpUtils.putBoolean(SpKey.SHOULD_MOVE_GUIDE_SHOW, false)
            mBinding.pagMoveGuide.stop()
            mBinding.clMoveGuide.gone()
        }

        if (isInEditMode) {
            exitEditMode()
        }
    }

    private fun enterEditMode() {
        isInEditMode = true
        chooseModelAdapter.isEditMode = isInEditMode
        mBinding.ctStatusBar.gone()
        mBinding.hotClickArea.gone()
        mBinding.ctEditStatusBar.visible()
    }

    private fun exitEditMode() {
        isInEditMode = false
        chooseModelAdapter.isEditMode = isInEditMode
        mBinding.ctStatusBar.visible()
        mBinding.hotClickArea.visible()
        mBinding.ctEditStatusBar.gone()
    }

}