package com.timekettle.module_home.ui.activity

import android.annotation.SuppressLint
import android.bluetooth.BluetoothAdapter
import android.content.Context
import android.net.wifi.WifiInfo
import android.net.wifi.WifiManager
import androidx.activity.viewModels
import androidx.lifecycle.lifecycleScope
import com.alibaba.android.arouter.facade.annotation.Route
import com.blankj.utilcode.util.AppUtils
import com.timekettle.upup.base.BaseApp
import com.timekettle.upup.base.ktx.clickDelay
import com.timekettle.upup.base.ktx.gone
import com.timekettle.upup.base.ktx.visible
import com.timekettle.upup.base.utils.EventBusUtils
import com.timekettle.upup.base.utils.logE
import com.timekettle.upup.comm.base.BaseActivity
import com.timekettle.upup.comm.bean.CertState
import com.timekettle.upup.comm.constant.RouteUrl
import com.timekettle.upup.comm.databinding.MainCertBinding
import com.timekettle.upup.comm.model.CertificateUpdateEvent
import com.timekettle.upup.comm.model.SensorsCustomEvent
import com.timekettle.upup.comm.utils.DeviceUtil
import com.timekettle.upup.comm.utils.SensorsUtil
import com.timekettle.upup.comm.viewmodel.CertViewModel
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import org.libpag.PAGFile
import org.libpag.PAGScaleMode
import kotlin.getValue

@AndroidEntryPoint
@Route(path = RouteUrl.Home.CertificateActivity)
class CertificateActivity : BaseActivity<MainCertBinding, CertViewModel>() {
    override val mViewModel: CertViewModel by viewModels()

    override fun initObserve() {
        mViewModel.certState.onEach {
            when(it) {
                CertState.Loading -> {
                    with(mBinding) {
                        clLoadingCert.visible()
                        clCertSuccess.gone()
                        clCertFail.gone()
                    }
                }
                CertState.Success -> {
                    with(mBinding) {
                        clLoadingCert.gone()
                        clCertSuccess.visible()
                        clCertFail.gone()
                        certSuccessPag.play()
                        clCertSuccessFactory.gone()
                        // 成功，通知重新请求接口
                        EventBusUtils.postEvent(CertificateUpdateEvent())
                        lifecycleScope.launch {
                            delay(2000)
                            finish()
                        }
                        SensorsUtil.trackEvent(
                            SensorsCustomEvent.X1_DeviceAuthentication.name, hashMapOf(
                                "AuthResult" to "成功"
                            )
                        )
                    }
                }
                is CertState.Fail -> {
                    with(mBinding) {
                        clLoadingCert.gone()
                        clCertSuccess.gone()
                        clCertFail.visible()
                        certFailTitle.text = getString(com.timekettle.upup.comm.R.string.trans_cert_fail_title).replace("xxxxxxx", it.failCode.toString())
                        logE("认证错误：${it.failMessage}")
                        clCertFailUserDesc.visible()
                        clCertFailFactoryDesc.gone()
                        SensorsUtil.trackEvent(
                            SensorsCustomEvent.X1_DeviceAuthentication.name, hashMapOf(
                                "AuthResult" to "失败",
                                "AuthFailLog" to it.failMessage,
                                "AuthFailSN" to DeviceUtil.getSerialNumber()
                            )
                        )
                    }
                }
                else -> {}
            }
        }.launchIn(lifecycleScope)
    }

    override fun initRequestData() {
        mViewModel.startCert()
    }

    override fun onResume() {
        if (mViewModel.certState.value == CertState.Success) {
            finish()
        }
        super.onResume()
    }

    override fun MainCertBinding.initView() {
        addWifiBatteryView(vTitleBar.vTitleLayout, showWifi = true)
        vTitleBar.vTitleTv.text = getString(com.timekettle.upup.comm.R.string.trans_cert_title)

        certSuccessPag.apply {
            composition = PAGFile.Load(context.assets, "ani_newuser_earbus_success.pag")
            setRepeatCount(1)
            setScaleMode(PAGScaleMode.Stretch) // 拉伸填充
        }

        certFailUserDescTip.text = getString(com.timekettle.upup.comm.R.string.trans_cert_fail_user_desc_tip).replace("xxxxxxxxx", DeviceUtil.getSerialNumber())
        certFailFactoryDescSn.text = "SN: ${DeviceUtil.getSerialNumber()}"
        certFailFactoryDescMacAddress.text = "Mac地址：${getMacAddress()}"
        certFailFactoryDescBtAddress.text = "蓝牙地址：${getBluetoothMacAddress()}"

        certSuccessFactoryVersion.text = "版本号: XOS.${AppUtils.getAppVersionName()}"
        certSuccessFactorySn.text = "SN: ${DeviceUtil.getSerialNumber()}"
    }

    override fun initListener() {
//        mBinding.btnReset.setOnClickListener {
//            logD("工厂恢复出厂并关机")
//            Thread{
//                DeviceUtil.resetShutdown(requireContext())
//            }.start()
//        }
        mBinding.btnBack.setOnClickListener {
            finish()
        }
        mBinding.btnRetry.clickDelay {
            mViewModel.startCert()
        }
    }

    @SuppressLint("HardwareIds")
    fun getBluetoothMacAddress(): String {
        val bluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
        return bluetoothAdapter?.address.toString()
    }


    fun getMacAddress(): String {
        val wifiManager = BaseApp.context.getSystemService(Context.WIFI_SERVICE) as WifiManager
        val wifiInfo: WifiInfo? = wifiManager.connectionInfo
        return wifiInfo?.macAddress.toString().uppercase()
    }
}