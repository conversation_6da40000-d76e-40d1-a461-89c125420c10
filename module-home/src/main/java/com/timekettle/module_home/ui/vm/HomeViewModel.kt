package com.timekettle.module_home.ui.vm

import SpConstant
import android.app.Activity
import android.app.AlarmManager
import android.content.BroadcastReceiver
import android.content.ContentResolver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.net.ConnectivityManager
import android.net.Uri
import android.os.Build
import android.os.Handler
import android.provider.Settings
import android.util.Log
import androidx.annotation.RequiresApi
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import androidx.work.OneTimeWorkRequest
import co.timekettle.sip.call.CallManager
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.AppUtils
import com.blankj.utilcode.util.DeviceUtils
import com.blankj.utilcode.util.FileUtils
import com.blankj.utilcode.util.NetworkUtils
import com.blankj.utilcode.util.ThreadUtils
import com.timekettle.module_home.receiver.HeadsetFirmCheckWorker
import com.timekettle.module_home.tool.TransLanguageTool
import com.timekettle.upup.base.BaseApp
import com.timekettle.upup.base.mvvm.vm.BaseViewModel
import com.timekettle.upup.base.utils.EventBusUtils
import com.timekettle.upup.base.utils.SpUtils
import com.timekettle.upup.base.utils.logD
import com.timekettle.upup.base.utils.logE
import com.timekettle.upup.base.utils.showToast
import com.timekettle.upup.base.utils.startActivityByRoute
import com.timekettle.upup.comm.R
import com.timekettle.upup.comm.bean.MeetEngineHost
import com.timekettle.upup.comm.conference.Code
import com.timekettle.upup.comm.conference.ConController
import com.timekettle.upup.comm.constant.Constant
import com.timekettle.upup.comm.constant.RouteUrl
import com.timekettle.upup.comm.constant.SpKey
import com.timekettle.upup.comm.model.AccountBean
import com.timekettle.upup.comm.model.DfuNotifyMsg
import com.timekettle.upup.comm.model.IpBean
import com.timekettle.upup.comm.model.OtaReadyEvent
import com.timekettle.upup.comm.model.ScreenOnOffEvent
import com.timekettle.upup.comm.model.SensorsCustomEvent
import com.timekettle.upup.comm.net.helper.BusinessManager
import com.timekettle.upup.comm.net.interceptor.TokenInterceptor
import com.timekettle.upup.comm.receiver.BatteryReceiver
import com.timekettle.upup.comm.receiver.BleVolumeObserver
import com.timekettle.upup.comm.repo.CommRepository
import com.timekettle.upup.comm.service.home.HomeServiceImplWrap
import com.timekettle.upup.comm.service.trans.TransServiceImplWrap
import com.timekettle.upup.comm.tools.AKSManager
import com.timekettle.upup.comm.tools.DeviceManager
import com.timekettle.upup.comm.tools.DeviceTool
import com.timekettle.upup.comm.tools.HallScanManager
import com.timekettle.upup.comm.tools.PKCS12Manager
import com.timekettle.upup.comm.utils.DeviceUtil
import com.timekettle.upup.comm.utils.DownloadUtil
import com.timekettle.upup.comm.utils.MyActivityUtil
import com.timekettle.upup.comm.utils.NetApiUtil
import com.timekettle.upup.comm.utils.OtaCheckUtil
import com.timekettle.upup.comm.utils.SensorsUtil
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import okhttp3.OkHttpClient
import okhttp3.Request
import org.json.JSONObject
import java.net.InetAddress
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Locale
import javax.inject.Inject
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

/**
 *
 * @author: licoba
 * @date: 2023/4/6
 */
@HiltViewModel
class HomeViewModel @Inject constructor(private val commRepository: CommRepository) :
    BaseViewModel() {

    private lateinit var activity: Activity
    private val downloader: DownloadUtil = DownloadUtil.get()
    private var currentTime = System.currentTimeMillis()
    private var snCurrentTime = System.currentTimeMillis()
    private var isSipIpGained = false
    private var isSpeechIpGained = false
    private var myWorkRequest: OneTimeWorkRequest? = null  // 一次性的工作
    var showVideoLiveData = MutableLiveData<Boolean>()

    /**
     * 检查某个产品的OTA的升级信息
     * 可以手动调用，也可以由任务半小时调用一次
     * @param firmVersion String
     */
    @RequiresApi(Build.VERSION_CODES.O)
    suspend fun reqOtaUpdateMsg() {
        logD("尝试从服务器获取固件升级消息... APP版本: ${AppUtils.getAppVersionName()}", TAG)
        val appVersion = AppUtils.getAppVersionName()
        commRepository.getDfuNotifyV2(appVersion).catch {
            logE("获取耳机固件升级推送消息异常: ${it.message}", TAG)
        }.collect {
            if (it == null) return@collect
            logD("获取到了固件升级推送消息: $it", TAG)
            if (doDownload(it)) { // 首先下载固件，这样在下次满足升级条件的时候就可以直接升级，不用再次下载
                if (MyActivityUtil.isUpgradingAty()) {
                    logD("当前正在升级页面，不提示升级", TAG)
                    return@collect
                }
                if (OtaCheckUtil.isMeetCondition(it)) {
                    logD("三个升级条件（电量、版本、双耳入仓）都满足，可以升级", TAG)
                    delay(100)
                    EventBusUtils.postEvent(OtaReadyEvent(it))
                } else {
                    logD("升级条件（电量、版本、双耳入仓）不满足，不提示升级", TAG)
                }
                // 不在升级页面，保存升级消息到本地
                DeviceManager.saveNotifyMsg(it)
            }
        }
    }


    private suspend fun doDownload(msg: DfuNotifyMsg): Boolean {
        logD("开始下载两个升级包", TAG)
        val uriA = msg.uriA?.trim().toString()
        val uriB = msg.uriB?.trim().toString()
        if (uriA.isEmpty()) {
            logD("uriA是空的，不下载", TAG)
            return false
        }
        if (uriB.isEmpty()) {
            logD("uriB是空的，不下载", TAG)
            return false
        }
        if (!uriA.startsWith("http")) {
            logE("串口升级url[${uriA}] 下载链接格式错误，必须以包含http头", TAG)
            return false
        }
        if (!uriB.startsWith("http")) {
            logE("BLE升级url[${uriB}] 下载链接格式错误，必须以包含http头", TAG)
            return false
        }
        // 先下载串口的链接
        val ret1 = downloadFile(uriA, msg.saveDir)
        if (ret1.isEmpty()) logE("串口升级包链接下载失败！", TAG)
        else {
            DownloadUtil.setUpgradeFileDownloadFinish(uriA)
            logD("串口升级包下载成功！$ret1", TAG)
        }
        // 再下载BLE的链接
        val ret2 = downloadFile(uriB, msg.saveDir)
        if (ret2.isEmpty()) logE("BLE升级包链接下载失败！")
        else {
            DownloadUtil.setUpgradeFileDownloadFinish(uriB)
            logD("BLE升级包下载成功！$ret2", TAG)
        }
        return ret1.isNotEmpty() && ret2.isNotEmpty()
    }

    /**
     * 下载成功则返回链接在本地的目录
     * 下载不成功，则返回null
     * 如果本地目录已经有了，则直接返回，节省服务器流量
     */
    private suspend fun downloadFile(link: String, saveDir: String): String {
        val filePath = saveDir + link.substringAfterLast("/", "xxx.zip")
        if (DownloadUtil.isUpgradeFileDownloadFinish(link)) return filePath   // 本地已经下载完成了
        return try {
            withContext(Dispatchers.IO) {
                val result = suspendCoroutine { continuation ->
                    downloader.download(link, saveDir, object : DownloadUtil.OnDownloadListener {
                        override fun onDownloadSuccess(url: String?, filePath: String) {
                            continuation.resume(filePath)
                        }

                        override fun onDownloading(url: String?, progress: Int, size: Long) {
                            // 进度回调，如果需要可以在此处处理进度信息
                        }

                        override fun onDownloadFailed(e: Exception) {
                            continuation.resume("")
                            FileUtils.delete(filePath)
                        }
                    })
                }
                result
            }
        } catch (e: Exception) {
            ""
        }
    }

    /**
     * 开启自动固件版本更新任务的检测，半小时一次
     */
    @RequiresApi(Build.VERSION_CODES.O)
    fun startRepeatFirmCheckJob(context: Context) {
        DeviceManager.removeNotifyMsg()  // 首次启动的时候清空本地升级消息，避免切环境之后消息干扰

        HeadsetFirmCheckWorker.activeJob(BaseApp.context)
        viewModelScope.launch(Dispatchers.IO) {
            reqOtaUpdateMsg()
//            doTask(5 * 60 * 1000L) {
//                withContext(Dispatchers.Main) {
//                    getSipAccount()
//                    logD("轮询注册sip账号")
//                    if (isNetConnected(context)) {
//                        CallManager.setRegistration(true)
//                    }
//                }
//            }
        }
    }

    private fun isNetConnected(context: Context): Boolean {
        val connectivityManager =
            context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val activeNetworkInfo = connectivityManager.activeNetworkInfo
        return activeNetworkInfo != null && activeNetworkInfo.isConnected && activeNetworkInfo.isAvailable
    }

    /**
     * 第一次联网之后，更新时区信息
     */
    private fun updateTimeZone() {
        viewModelScope.launch {
            commRepository.getTimeZone()
                .catch {
                    logE("获取时区信息错误 $it", TAG)
                }
                .collect {
                    logD("获取到了时区信息 $it", TAG)
                    try {
                        val alarm =
                            BaseApp.context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
                        alarm.setTimeZone(it?.timezone.toString()) //设置上海时区
                        logD("更新系统时区成功！", TAG)
                    } catch (e: Exception) {
                        logE("更新系统时区失败！ $e", TAG)
                    }
                }
        }

    }

    fun getFastestIp(callBack: ((Boolean) -> Unit)? = null) {
        logD("获取ip列表 开始", TAG)
        BusinessManager.getMeetingIdList().catch {
            logE("获取ip列表 失败 $it", TAG)
        }.onEach {
            if (it == null) {
                logE("获取ip列表 失败 null", TAG)
                errorRequest(callBack)
            } else if (it.isEmpty()) {
                logE("获取ip列表 失败 empty", TAG)
                errorRequest(callBack)
            } else {
                withContext(Dispatchers.IO){
                    testIpList(it)
                }
                val fHost = it.filter { item -> item.ping_result }
                if (fHost.isNotEmpty()) {
                    logD("获取ip列表 fHost size ${fHost.size}", TAG)

                    val ipEntity = fHost.minBy { it.connectTime }
                    logD("获取ip列表 fastIp ${ipEntity.ip} + -- + ${ipEntity.connectTime}", TAG)
                    logD("获取ip列表 historyIp ${HomeServiceImplWrap.getSipIp()?.ip}", TAG)

                    if (HomeServiceImplWrap.getSipIp()?.ip != ipEntity.ip + ":5166;transport=tcp") {
                        HomeServiceImplWrap.saveSipIp(IpBean(ipEntity.id, ipEntity.ip + ":5166;transport=tcp"))
                        logD("获取ip列表 saveIp ${HomeServiceImplWrap.getSipIp()?.ip}", TAG)
                        callBack?.invoke(true)
                    } else {
                        logD("获取ip列表 ip没变", TAG)
                        callBack?.invoke(false)
                    }
                } else {
                    logE("ip列表全部ping失败", TAG)
                    errorRequest(callBack)
                }
            }
        }.launchIn(viewModelScope)
    }

    /**
     * 获取最快的ip服务
     */
    fun getFastestIp11(callBack: ((Boolean) -> Unit)? = null) {
        viewModelScope.launch(Dispatchers.IO) {
            logD("获取ip列表 开始", TAG)
            TransServiceImplWrap.fetchMeetHosts { code, hosts ->
                if (code == 0) {
                    testIpList(hosts)

                    val fHost = hosts.filter { it.ping_result }
                    if (fHost.isNotEmpty()) {
                        logD("获取ip列表 fHost size ${fHost.size}", TAG)

                        val ipEntity = fHost.minBy { it.connectTime }
                        logD("获取ip列表 fastIp ${ipEntity.ip} + -- + ${ipEntity.connectTime}", TAG)
                        logD("获取ip列表 historyIp ${HomeServiceImplWrap.getSipIp()?.ip}", TAG)

                        if (HomeServiceImplWrap.getSipIp()?.ip != ipEntity.ip + ":5166;transport=tcp") {
                            HomeServiceImplWrap.saveSipIp(IpBean(ipEntity.id, ipEntity.ip + ":5166;transport=tcp"))
                            logD("获取ip列表 saveIp ${HomeServiceImplWrap.getSipIp()?.ip}", TAG)
                            callBack?.invoke(true)
                        } else {
                            logD("获取ip列表 ip没变", TAG)
                            callBack?.invoke(false)
                        }
                    } else {
                        logD("获取ip列表 失败 fHost is Empty", TAG)
                        errorRequest(callBack)
                    }
                } else {
                    logD("获取ip列表 失败 $code", TAG)
                    errorRequest(callBack)
                }
            }
        }
    }

    private fun errorRequest(callBack: ((Boolean) -> Unit)? = null) {
        if (!CallManager.accountIsInitialized()) {
            if (HomeServiceImplWrap.getSipIp() == null) {
                HomeServiceImplWrap.saveSipIp(IpBean(1, "************" + ":5166;transport=tcp"))
                logD("获取ip列表，使用默认ip进行注册 ${HomeServiceImplWrap.getSipIp()?.ip}", TAG)
                callBack?.invoke(true)
            } else {
                HomeServiceImplWrap.getSipIp()?.let {
                    if (it.ip.contains(";transport=tcp")) {
                        logD("获取ip列表，使用缓存tcp格式ip进行注册 ${it.ip}", TAG)
                        callBack?.invoke(true)
                    } else {
                        logD("获取ip列表，使用缓存udp转tcp格式ip进行注册 ${it.ip}", TAG)
                        HomeServiceImplWrap.saveSipIp(IpBean(it.id, it.ip + ";transport=tcp"))
                        logD("获取ip列表，使用缓存udp转tcp格式ip进行注册 转换后的ip=${HomeServiceImplWrap.getSipIp()?.ip}", TAG)
                        callBack?.invoke(true)
                    }
                }
            }
        } else {
            logD("获取ip列表，errorRequest 账号已创建", TAG)
        }
    }

    /**
     * ip地址测速
     */
    private fun testIpList(list: List<MeetEngineHost>) {
        list.forEach {
            try {
                val inetAddress = InetAddress.getByName(it.ip)
                val startTime = System.currentTimeMillis()
                val reachable = inetAddress.isReachable(5000) // 5000毫秒的超时时间
                val endTime = System.currentTimeMillis()

                if (reachable) {
                    val pingTime: Long = endTime - startTime
                    it.connectTime = pingTime
                    it.ping_result = true
//                    Log.d("获取ip列表", "Ping成功，时长：" + it.connectTime + "毫秒")
                    logD("获取ip列表 Ping成功 ip = ${it.ip} 时长 = ${it.connectTime}毫秒", TAG)
                } else {
                    it.ping_result = false
//                    Log.d("获取ip列表", "Ping失败")
                    logD("获取ip列表 Ping失败 ip = ${it.ip}", TAG)
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    /**
     * 全局注册sip账号，语言默认给本地系统的语言
     */
    fun getSipAccount(isIpChanged: Boolean) {
        val mac = DeviceTool.getSerialNumber()
        val langCode = TransLanguageTool.getDefaultSelfLan()
        ConController.getSipAccount(mac, langCode, viewModelScope) { code, account, msg ->
            ThreadUtils.runOnUiThread {
                logD("获取sip账号 code=$code", TAG)
                if (code == Code.SUCCESS) {
                    logD("获取sip账号成功", TAG)
                    HomeServiceImplWrap.saveSipAccount(account!!)
                    if (!CallManager.accountIsInitialized()) {
                        logD("sip账号未创建，准备创建", TAG)
                        registerAccount(account)
                    } else {
                        if (isIpChanged) {
                            logD("sip账号已创建，准备修改", TAG)
                            modifyAccount(account)
                        } else {
                            logD("sip账号已创建 无需修改", TAG)

                            // TODO: 注释掉式因为这里有时候会导致崩溃，也可以在这里加try catch
//                            if (MyNetWorkUtil.isNetConnected(activity) && !CallManager.regIsActive()) {
//                                logD("sip账号已创建，准备激活", TAG)
//                                CallManager.setRegistration(true)
//                                logD("激活sip账号成功", TAG)
//                            }

                        }
                    }
                } else {
                    logD("获取sip账号失败: $msg", TAG)
                }
            }
        }
    }

    private fun registerAccount(account: AccountBean) {
        CallManager.createAccount(
            DeviceManager.getMeetingNickname().ifEmpty { Constant.MeetingUserName },
            account.sipName!!,
            account.sipPwd!!,
            account.accId!!,
            account.registrar!!
        )
        logD("注册sip账号成功", TAG)
    }

    private fun modifyAccount(account: AccountBean) {
        try {
            CallManager.modifyAccount(
                DeviceManager.getMeetingNickname().ifEmpty { Constant.MeetingUserName },
                account.sipName!!,
                account.sipPwd!!,
                account.accId!!,
                account.registrar!!
            )

            logD("修改sip账号成功", TAG)

        } catch (e: Exception) {
            logD("修改sip账号失败 ${e.message}", TAG)
        }
    }

//    fun createConference(account: AccountBean) {
//        val nickName =
//            DeviceManager.getMeetingNickname().ifEmpty { Constant.MeetingUserName } //获取昵称
//        CallManager.createAccount(
//            nickName,
//            account.sipName!!,
//            account.sipPwd!!,
//            account.accId!!,
//            account.registrar!!
//        )
//    }

    private fun checkSn() = BusinessManager.checkSN().catch {
        logE("获取是否显示音视频 失败：响应异常 $it", TAG)
    }.onEach {
        if (it == null) {
            logE("获取是否显示音视频 失败：no success")
        } else if (it.permission) {
            logD("获取是否显示音视频 成功，显示入口")
            showVideoLiveData.postValue(true)
        } else {
            logD("获取是否显示音视频 成功，不显示入口")
        }
    }.launchIn(viewModelScope)

    private fun uploadPolicy() {
        logD("已同意隐私政策，上传归档", TAG)
        val format =  SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssXXX", Locale.getDefault())
        val time = format.format(SpUtils.getLong(SpKey.AGREE_POLICY_TIME, 0))
        BusinessManager.savePrivacyInfo(
            NetApiUtil.createJsonRequestBody(
                "action_time" to time,
                "consent_action" to "granted",
                "consents" to listOf(
                    mapOf(
                        "consent_type" to "User_Agreement",
                        "version" to "1.0"
                    ),
                    mapOf(
                        "consent_type" to "Privacy_Policy",
                        "version" to "1.0"
                    )
                )
            )
        ).onEach {
            if (it.code == 0) {
                logD("上传归档成功，$it", TAG)
                SpUtils.putLong(SpKey.AGREE_POLICY_TIME, 0)
            } else {
                logE("归档请求成功，但状态码异常：$it", TAG)
            }
        }.catch {
            logE("上传归档失败，异常：${it.message}", TAG)
            it.printStackTrace()
        }.launchIn(viewModelScope)
    }

    private fun uploadUserInfo() = BusinessManager.saveUserInfo(
        SpUtils.getString(SpKey.USER_EMAIL, ""),
        SpUtils.getInt(SpKey.USER_INDUSTRY, 0)
    ).catch {
        logE("上传用户信息失败 失败：响应异常 ${it.message}", TAG)
    }.onEach {
        if (it.code == 0) {
            logD("上传用户信息成功，$it", TAG)
            SpUtils.putBoolean(SpKey.ALREADY_UPLOAD_USER_INFO, true)
        } else {
            logE("用户信息请求成功，但状态码异常：$it", TAG)
        }
    }.launchIn(viewModelScope)

    fun init(activity: Activity) {
        this.activity = activity
    }

    private fun getNetType(): String {
        return when (NetworkUtils.getNetworkType()) {
            NetworkUtils.NetworkType.NETWORK_ETHERNET -> "以太网"
            NetworkUtils.NetworkType.NETWORK_WIFI -> "Wi-Fi"
            NetworkUtils.NetworkType.NETWORK_2G -> "2G"
            NetworkUtils.NetworkType.NETWORK_3G -> "3G"
            NetworkUtils.NetworkType.NETWORK_4G -> "4G"
            NetworkUtils.NetworkType.NETWORK_5G -> "5G"
            NetworkUtils.NetworkType.NETWORK_UNKNOWN -> "未知网络"
            NetworkUtils.NetworkType.NETWORK_NO -> "无网络"
        }
    }

    private val connectionReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            val connectivityManager =
                context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
            val activeNetwork = connectivityManager.activeNetworkInfo
            val isConnected =
                activeNetwork != null && activeNetwork.isConnectedOrConnecting && activeNetwork.isAvailable

            if (isConnected) {
                logD("网络已连接: 网络类型(${getNetType()})", TAG)

                updateTimeZone()

                if (DeviceUtil.hasCert()) {
                    requestAll()
                } else {
                    logD("当前设备未持有证书，进入下载页面", TAG)
                    startActivityByRoute(RouteUrl.Home.CertificateActivity)
                }

                if (!isSpeechIpGained) {
                    viewModelScope.launch(Dispatchers.IO) {
                        TransServiceImplWrap.fetchHosts()
                        isSpeechIpGained = true
                    }
                }

            } else {
                showToast(activity.getString(R.string.common_network_error_check_it))
                logD("网络已断开", TAG)
            }
        }
    }

    fun requestAll() = viewModelScope.launch(Dispatchers.IO) {
        if (!SpUtils.getBoolean(SpKey.IS_SHOW_VIDEO, false)) {
            checkSn()
        }

        if (SpUtils.getBoolean(
                SpKey.AGREE_POLICY,
                false
            ) && SpUtils.getLong(SpKey.AGREE_POLICY_TIME, 0) != 0L
        ) {
            uploadPolicy()
        }

        if (SpUtils.getBoolean(SpKey.IS_DEBUG_STATUS, false)) {
            val hostIp = SpUtils.getString(SpKey.TMK_SIP_HOST_IP, "")
            if (hostIp.isNotEmpty()) {
                logD("选择了指定服务器: $hostIp", TAG)
                getSipAccount(true)
            } else {
                logD( "没有指定服务器，由程序自己选择", TAG)
                getFastestIp {
                    getSipAccount(it)
                }
            }
        } else {
            logD("没有开启调试面板", TAG)
            getFastestIp {
                getSipAccount(it)
            }
        }

        if (!SpUtils.getBoolean(SpKey.ALREADY_UPLOAD_USER_INFO, false)) {
            logD("未上传过用户信息，上传用户信息", TAG)
            uploadUserInfo()
        }
    }

    private val powerReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(p0: Context?, p1: Intent?) {
            Log.d(TAG, "执行到此")
            if (!MyActivityUtil.isUpgradingAty() && !TransServiceImplWrap.getOnNFCReceive()
                && "co.timekettle.module_translate.ui.activity.DongleUpgradeActivity" != ActivityUtils.getTopActivity().localClassName &&
                !BatteryReceiver.isBaseCharging()
            ) {
                startActivityByRoute(RouteUrl.Translate.SendActivity)
            }
        }
    }

    private val screenStatusReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        private val SCREEN_ON = "android.intent.action.SCREEN_ON"
        private val SCREEN_OFF = "android.intent.action.SCREEN_OFF"

        override fun onReceive(context: Context, intent: Intent) {
            if (SCREEN_ON == intent.action) {
                logD("亮屏", TAG)
                HallScanManager.isScreenOn = true
                HallScanManager.judgeStartOrStopBleScan()
                if (System.currentTimeMillis() - currentTime >= TIME_GET_HOST) {
                    currentTime = System.currentTimeMillis()
                    viewModelScope.launch(Dispatchers.IO) {
                        TransServiceImplWrap.fetchHosts()
                    }

//                    getFastestIp {
//                        if (!CallManager.accountIsInitialized()) {
//                            logD("sip账号未创建，准备创建", TAG)
//                            getSipAccount()
//                        }
//                    }

                }

                if (!SpUtils.getBoolean(SpKey.IS_SHOW_VIDEO, false)) {
                    if (System.currentTimeMillis() - snCurrentTime >= TIME_CHECK_SN) {
                        snCurrentTime = System.currentTimeMillis()
                        checkSn()
                    }
                }

                if (Constant.isMeeting) {
                    ConController.getConferenceInfo()
                }

                if (!isSameDay()) {
                    SensorsUtil.trackEvent(SensorsCustomEvent.X1_ScreenisOn.name, null)
                }

            } else if (SCREEN_OFF == intent.action) {
                EventBusUtils.postEvent(ScreenOnOffEvent(isScreenOff = true))
                logD("熄屏", TAG)
                HallScanManager.isScreenOn = false
                HallScanManager.stopBleScan()
            }
        }
    }

    fun isSameDay(): Boolean {
        val lastDateString = SpUtils.getString(SpKey.LAST_DATE,"")

        val currentDate = Calendar.getInstance().time
        val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
        val currentDateString = dateFormat.format(currentDate)

        return if (lastDateString == currentDateString) {
            true
        } else {
            SpUtils.putString(SpKey.LAST_DATE, currentDateString)
            false
        }
    }

    fun registerReceiver() {
        val connectFilter = IntentFilter()
        connectFilter.addAction(ConnectivityManager.CONNECTIVITY_ACTION)
        val screenStatusFilter = IntentFilter()
        screenStatusFilter.addAction(Intent.ACTION_SCREEN_ON)
        screenStatusFilter.addAction(Intent.ACTION_SCREEN_OFF)
        activity.registerReceiver(screenStatusReceiver, screenStatusFilter)
        activity.registerReceiver(connectionReceiver, connectFilter)
        activity.registerReceiver(powerReceiver, IntentFilter(POWER_ACTION))
        initBleVolumeObserver()

    }

    fun unRegisterReceiver() {
        activity.unregisterReceiver(screenStatusReceiver)
        activity.unregisterReceiver(connectionReceiver)
        activity.unregisterReceiver(powerReceiver)
    }

    /**
     * BLE音量的监听
     */
    private fun initBleVolumeObserver() {
        val observer = BleVolumeObserver(Handler())
        val resolver: ContentResolver = activity.contentResolver
        val uri: Uri = Settings.System.getUriFor("BLE_VOLUME")
        resolver.registerContentObserver(uri, true, observer)
    }

    suspend fun doTask(intervalMillis: Long, task: suspend () -> Unit) {
        while (true) { // 无限循环
            delay(intervalMillis) // 等待指定时间间隔
            task() // 执行任务
        }
    }

    companion object {
        private const val TAG = "HomeViewModel"
        private const val POWER_ACTION = "nstart.intent.action.IceBreaking"
        private const val TIME_GET_HOST = 30 * 60 * 1000L
        private const val TIME_CHECK_SN = 1 * 60 * 1000L
    }

}

