<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="130dp"
    android:layout_height="162dp"
    tools:background="#000000"
    tools:ignore="MissingDefaultResource,ResourceName">

    <!--    android:background="@drawable/bg_mode_item_selector"-->

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/rootLayout"
        android:layout_width="118dp"
        android:layout_height="150dp"
        android:layout_marginStart="6dp"
        android:layout_marginEnd="6dp"
        android:layout_marginTop="12dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:background="@mipmap/home_mode_img_bg_def"
        tools:background="#000000"
        tools:ignore="MissingDefaultResource,ResourceName">

        <ImageView
            android:id="@+id/vModelImg"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_marginTop="20.5dp"
            android:src="@mipmap/home_mode_icon_simul"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textColor="@color/white"
            android:textSize="15sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/vModelImg"
            tools:text="One-on-One" />

        <!--    <ImageView-->
        <!--        android:id="@+id/ivSelect"-->
        <!--        android:layout_width="20dp"-->
        <!--        android:layout_height="20dp"-->
        <!--        android:layout_marginTop="8dp"-->
        <!--        android:layout_marginRight="14dp"-->
        <!--        android:src="@mipmap/uikit_control_picker_sel"-->
        <!--        app:layout_constraintRight_toRightOf="parent"-->
        <!--        app:layout_constraintTop_toTopOf="parent" />-->

        <!-- Mask for hidden items in edit mode -->
        <View
            android:id="@+id/vMask"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="#80000000"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- Remove button -->
    <ImageView
        android:id="@+id/iv_edit_remove"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginTop="1dp"
        android:layout_marginEnd="1dp"
        android:src="@mipmap/home_hold_icon_delete"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <!-- Add button -->
    <ImageView
        android:id="@+id/iv_edit_add"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginTop="1dp"
        android:layout_marginEnd="1dp"
        android:src="@mipmap/home_hold_icon_add"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>
