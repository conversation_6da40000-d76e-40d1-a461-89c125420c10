package com.timekettle.module_main.ui.fragment

import android.annotation.SuppressLint
import android.bluetooth.BluetoothAdapter
import android.content.Context
import android.net.wifi.WifiInfo
import android.net.wifi.WifiManager
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import androidx.navigation.findNavController
import androidx.navigation.fragment.findNavController
import com.blankj.utilcode.util.AppUtils
import com.timekettle.upup.comm.bean.CertState
import com.timekettle.upup.comm.viewmodel.CertViewModel
import com.timekettle.upup.base.BaseApp
import com.timekettle.upup.base.ktx.clickDelay
import com.timekettle.upup.base.ktx.gone
import com.timekettle.upup.base.ktx.visible
import com.timekettle.upup.base.utils.logD
import com.timekettle.upup.base.utils.logE
import com.timekettle.upup.comm.base.BaseFragment
import com.timekettle.upup.comm.databinding.MainCertBinding
import com.timekettle.upup.comm.model.SensorsCustomEvent
import com.timekettle.upup.comm.utils.DeviceUtil
import com.timekettle.upup.comm.utils.SensorsUtil
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import org.libpag.PAGFile
import org.libpag.PAGScaleMode
import kotlin.collections.hashMapOf

class CertFragment : BaseFragment<MainCertBinding, CertViewModel>() {
    override val mViewModel: CertViewModel by activityViewModels()

    override fun initObserve() {
        mViewModel.certState.onEach {
            when(it) {
                CertState.Loading -> {
                    with(mBinding) {
                        clLoadingCert.visible()
                        clCertSuccess.gone()
                        clCertFail.gone()
                    }
                }
                CertState.Success -> {
                    with(mBinding) {
                        clLoadingCert.gone()
                        clCertSuccess.visible()
                        clCertFail.gone()
                        certSuccessPag.play()
                        if (mViewModel.pingFactorySuccess) {
                            clCertSuccessFactory.visible()
                        } else {
                            clCertSuccessFactory.gone()
                            lifecycleScope.launch {
                                delay(2000)
                                findNavController().navigate(com.timekettle.module_main.R.id.action_certfragment_to_talkguidefragment)
                            }
                        }
                        SensorsUtil.trackEvent(
                            SensorsCustomEvent.X1_DeviceAuthentication.name, hashMapOf(
                                "AuthResult" to "成功"
                            )
                        )
                    }
                }
                is CertState.Fail -> {
                    with(mBinding) {
                        clLoadingCert.gone()
                        clCertSuccess.gone()
                        clCertFail.visible()
                        certFailTitle.text = getString(com.timekettle.upup.comm.R.string.trans_cert_fail_title).replace("xxxxxxx", it.failCode.toString())
                        logE("认证错误：${it.failMessage}")
                        if (mViewModel.pingFactorySuccess) {
                            clCertFailUserDesc.gone()
                            clCertFailFactoryDesc.visible()
                        } else {
                            clCertFailUserDesc.visible()
                            clCertFailFactoryDesc.gone()
                        }
                        SensorsUtil.trackEvent(
                            SensorsCustomEvent.X1_DeviceAuthentication.name, hashMapOf(
                                "AuthResult" to "失败",
                                "AuthFailLog" to it.failMessage,
                                "AuthFailSN" to DeviceUtil.getSerialNumber()
                            )
                        )
                    }
                }
                else -> {}
            }
        }.launchIn(lifecycleScope)
    }

    override fun initRequestData() {
        mViewModel.startCert()
    }

    override fun onResume() {
        if (mViewModel.certState.value == CertState.Success && !mViewModel.pingFactorySuccess) {
            findNavController().navigate(com.timekettle.module_main.R.id.action_certfragment_to_talkguidefragment)
        }
        super.onResume()
    }

    override fun MainCertBinding.initView() {
        addWifiBatteryView(vTitleBar.vTitleLayout, showWifi = false)
        vTitleBar.vTitleTv.text = getString(com.timekettle.upup.comm.R.string.trans_cert_title)

        certSuccessPag.apply {
            composition = PAGFile.Load(context.assets, "ani_newuser_earbus_success.pag")
            setRepeatCount(1)
            setScaleMode(PAGScaleMode.Stretch) // 拉伸填充
        }

        certFailUserDescTip.text = getString(com.timekettle.upup.comm.R.string.trans_cert_fail_user_desc_tip).replace("xxxxxxxxx", DeviceUtil.getSerialNumber())
        certFailFactoryDescSn.text = "SN: ${DeviceUtil.getSerialNumber()}"
        certFailFactoryDescMacAddress.text = "Mac地址：${getMacAddress()}"
        certFailFactoryDescBtAddress.text = "蓝牙地址：${getBluetoothMacAddress()}"

        certSuccessFactoryVersion.text = "版本号: XOS.${AppUtils.getAppVersionName()}"
        certSuccessFactorySn.text = "SN: ${DeviceUtil.getSerialNumber()}"
        certSuccessFactoryMacAddress.text = "Mac地址：${getMacAddress()}"
        certSuccessFactoryBtAddress.text = "蓝牙地址：${getBluetoothMacAddress()}"
    }

    override fun initListener() {
        mBinding.btnReset.setOnClickListener {
            logD("工厂恢复出厂并关机")
            Thread{
                DeviceUtil.resetShutdown(requireContext())
            }.start()
        }
        mBinding.btnBack.setOnClickListener {
            if (!mViewModel.pingFactorySuccess) {
                it.findNavController().navigateUp()
            }
        }
        mBinding.btnRetry.clickDelay {
            mViewModel.startCert()
        }
    }

    @SuppressLint("HardwareIds")
    fun getBluetoothMacAddress(): String {
        val bluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
        return bluetoothAdapter?.address.toString()
    }


    fun getMacAddress(): String {
        val wifiManager = BaseApp.context.getSystemService(Context.WIFI_SERVICE) as WifiManager
        val wifiInfo: WifiInfo? = wifiManager.connectionInfo
        return wifiInfo?.macAddress.toString().uppercase()
    }

}