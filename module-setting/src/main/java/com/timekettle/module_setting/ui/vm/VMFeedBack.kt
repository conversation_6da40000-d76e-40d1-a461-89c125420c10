package com.timekettle.module_setting.ui.vm

import android.annotation.SuppressLint
import android.hardware.usb.UsbDevice
import android.os.Build
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.viewModelScope
import com.blankj.utilcode.util.ConvertUtils
import com.blankj.utilcode.util.FileUtils
import com.blankj.utilcode.util.LogUtils
import com.blankj.utilcode.util.NetworkUtils
import com.blankj.utilcode.util.PathUtils
import com.blankj.utilcode.util.SDCardUtils
import com.blankj.utilcode.util.ThreadUtils.runOnUiThread
import com.blankj.utilcode.util.ZipUtils
import com.kongzue.dialogx.dialogs.PopTip
import com.kongzue.dialogx.dialogs.TipDialog
import com.kongzue.dialogx.dialogs.WaitDialog
import com.qiniu.android.storage.UploadManager
import com.timekettle.module_setting.ui.bean.StorageSpaceBean
import com.timekettle.module_setting.ui.bean.UartUpdMTxCmd
import com.timekettle.module_setting.ui.repo.SettingRepository
import com.timekettle.module_setting.ui.tools.MyUtil
import com.timekettle.module_setting.ui.tools.MyUtil.getCheckSum
import com.timekettle.upup.base.BaseApp
import com.timekettle.upup.base.mvvm.vm.BaseViewModel
import com.timekettle.upup.base.utils.DateUtils
import com.timekettle.upup.base.utils.SpUtils
import com.timekettle.upup.base.utils.logD
import com.timekettle.upup.base.utils.logE
import com.timekettle.upup.base.utils.showToast
import com.timekettle.upup.comm.R
import com.timekettle.upup.comm.constant.SpKey
import com.timekettle.upup.comm.dialog.DialogFactory
import com.timekettle.upup.comm.net.helper.BusinessManager
import com.timekettle.upup.comm.net.helper.ReasonCodeEnum
import com.timekettle.upup.comm.net.helper.ReasonException
import com.timekettle.upup.comm.service.login.LoginServiceImplWrap
import com.timekettle.upup.comm.tools.DeviceTool
import com.timekettle.upup.comm.utils.DownloadUtil
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers

import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.onEmpty
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileInputStream
import java.io.IOException
import java.lang.Exception
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.zip.ZipEntry
import java.util.zip.ZipOutputStream
import javax.inject.Inject
import kotlin.random.Random

/**
 *
 * @author: licoba
 * @date: 2023/05/19
 */
@HiltViewModel
class VMFeedBack @Inject constructor(private val repo: SettingRepository) : BaseViewModel() {

    private val uploadLogDir = PathUtils.getInternalAppCachePath() + "/ExportLog/"
    var qiNiuFileKey = MutableLiveData<String>()
    var liveUploading: MutableLiveData<Boolean> = MutableLiveData(false)
    var liveUploadSuccess: MutableLiveData<Boolean> = MutableLiveData(false)
    val emailFlow = MutableSharedFlow<String>()

    private var uploadEmail = ""

    init {
        FileUtils.createOrExistsDir(uploadLogDir)
    }

    fun getSavedEmail() = BusinessManager.getUserEmail()
        .catch {
            logE("获取用户email失败 ${it.message}")
        }.onStart {
            val feedbackEmail = SpUtils.getString(SpKey.FEEDBACK_EMAIL, "")
            if (feedbackEmail.isNotEmpty()) {
                emailFlow.emit(feedbackEmail)
            }
        }
        .onEach {
            if (it.email.isNotEmpty()) {
                emailFlow.emit(it.email)
            }
        }.launchIn(viewModelScope)

    fun submitLog(feedbackTime: String, email: String) {
        viewModelScope.launch(Dispatchers.IO) {
            zipLog(feedbackTime)?.let { file ->
//                getQiNiuToken()?.let { token ->
//                    reqQiNiuUpload(file, token, feedbackTime)
//                }
                reqS3Upload(file, feedbackTime, email)
            }
        }
    }

    @SuppressLint("MissingPermission")
    private suspend fun getQiNiuToken(): String? {
        logD("获取七牛上传Token...")
        var token: String? = null
        repo.requestOssToken()
            .catch {
                if (!NetworkUtils.isAvailable()) showToast(BaseApp.context.getString(R.string.common_network_error_check_it))
                liveUploading.postValue(false)
            }
            .onEmpty {
                liveUploading.postValue(false)
            }
            .onStart { liveUploading.postValue(true) }
            .collect {
                logD("拿到了七牛上传Token...$it")
                token = it
            }
        return token
    }

    private fun zipLog(feedbackTime: String): File? {
        liveUploading.postValue(true)
        if (SpUtils.getBoolean(SpKey.CACHE_ALL_FILE, false)) {
            val srcPath = PathUtils.getExternalAppCachePath() + "/userlog/"
            val dstPath = "${uploadLogDir}日志(catch)${DateUtils.getCurLogDateTime()}.zip"
            return try {
                if (zipCachedFile(srcPath, dstPath)) {
                    logD("catch 日志导出成功 $dstPath")
                }
                File(dstPath)
            } catch (e: Exception) {
                liveUploading.postValue(false)
                logE("catch 日志导出失败 $e")
                null
            }
        } else {
            val srcPath = PathUtils.getInternalAppCachePath() + "/Log/"
            val wifiPath = PathUtils.getExternalStoragePath() + "/WifiLog/"
            FileUtils.createOrExistsDir(wifiPath)
            val dstPath = "${uploadLogDir}日志${DateUtils.getCurLogDateTime()}.zip"
            return try {
                if (ZipUtils.zipFiles(listOf(srcPath, wifiPath), dstPath)) {
                    logD("日志导出成功 $dstPath")
                }
                File(dstPath)
            } catch (e: Exception) {
                liveUploading.postValue(false)
                logE("日志导出失败 $e")
                null
            }
        }
    }

    private fun getRelativePath(directory: File, file: File): String {
        val dirPath = directory.absolutePath
        val filePath = file.absolutePath

        // 确保目录是文件的前缀
        if (filePath.startsWith(dirPath)) {
            return filePath.substring(dirPath.length + 1)  // 去掉目录路径部分并返回相对路径
        }
        return ""  // 如果文件路径不以目录路径为前缀，返回空字符串或根据需求返回其他内容
    }

    private fun zipCachedFile(directoryPath: String, outputZipFile: String): Boolean {
        val directory = File(directoryPath)
        if (!directory.exists() || !directory.isDirectory) {
            println("指定的路径不是有效的目录！")
            return false
        }

        // 获取所有文件及文件夹内的文件，分别获取每个目录下的最新10个文件
        val filesToCompress = mutableListOf<File>()
        val upperLogFiles = mutableListOf<File>()

        // 获取目录中所有文件的最新10个文件（包括子目录）
        directory.listFiles()?.forEach { file ->
            if (file.isDirectory) {
                // 对每个子目录单独获取最新的10个文件
                filesToCompress.addAll(getLatestFilesFromDirectory(file, FILE_LIMIT))
            } else {
                upperLogFiles.add(file)
            }
        }
        filesToCompress.addAll(upperLogFiles.sortedByDescending { it.lastModified() }.take(FILE_LIMIT))
//        filesToCompress.addAll(getLatestFilesFromDirectory(directory, FILE_LIMIT))
        // 创建输出的压缩文件
        val zipFile = File(outputZipFile)
        ZipOutputStream(zipFile.outputStream()).use { zipOut ->
            filesToCompress.forEach { file ->
                val relativePath =
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                        directory.toPath().relativize(file.toPath()).toString()
                    } else {
                        getRelativePath(directory, file)
                    }  // 获取相对路径
                logD("file = $file,relativePath = $relativePath")
                try {
                    // 处理目录结构
                    if (file.isDirectory) {
                        zipOut.putNextEntry(ZipEntry("$relativePath/"))
                    } else {
                        FileInputStream(file).use { fis ->
                            val zipEntry = ZipEntry(relativePath)  // 保持目录结构
                            zipOut.putNextEntry(zipEntry)
                            fis.copyTo(zipOut)  // 将文件内容复制到压缩文件中
                        }
                    }
                    zipOut.closeEntry()
                } catch (e: IOException) {
                    println("错误: ${e.message}")
                }
            }
        }

        println("已成功压缩最新的文件并保持目录结构到 $outputZipFile")
        return true
    }

    // 获取指定目录中最新的 n 个文件
    private fun getLatestFilesFromDirectory(directory: File, limit: Int): List<File> {
        val allFiles = mutableListOf<File>()
        val bleDirFiles = mutableListOf<File>()
        val bleFiles = mutableListOf<File>()

        // 获取目录中的文件
        directory.listFiles()?.forEach { file ->
            if (file.isFile) {
                allFiles.add(file)  // 仅添加文件，不包括目录
            } else if (file.isDirectory && file.name.contains("BLE")) {
                getLatestFilesFromDirectory(file, limit)
            } else if (file.isDirectory && (file.name.contains("Audio") || file.name.matches(Regex("^\\d+$")))){
                bleDirFiles.add(file)
            }
        }

        bleDirFiles.sortedByDescending { it.lastModified() }.take(limit).forEach {
            logD("bleFiles dir = $it")
            if (it.isDirectory) {
                bleFiles.addAll(it.walkTopDown().toList())
            }
        }
        logD("bleFiles = $bleFiles")
        // 按照文件的最后修改时间排序并取最新的 limit 个文件
        return allFiles.sortedByDescending { it.lastModified() }.take(limit) + bleFiles
    }

    @SuppressLint("SimpleDateFormat")
    fun reqQiNiuUpload(file: File, token: String, feedbackTime: String) {
        logD("上传文件到七牛")
        val formatter = SimpleDateFormat("yyyy_MM_dd_HH_mm_ss")
        val dateStr = formatter.format(Date())
        val folderName = if (SpUtils.getBoolean(SpKey.CACHE_ALL_FILE, false)) {
            "userlog_dev/x1"
        } else {
            "feedback-x1"
        }

        val key =
            "$folderName/${DeviceTool.getSerialNumber()}_Occur${feedbackTime}+${dateStr}.zip" // <指定七牛服务上的文件名，或 null>;
        val uploadManager = UploadManager()
        uploadManager.put(
            file, key, token,
            { myKey, info, response ->
                if (info.isOK) {
                    logD("七牛文件上传完成！\n key:${myKey} \ninfo:$info \nresponse:$response")
                    qiNiuFileKey.postValue(key)
                } else {
                    liveUploading.postValue(false)
                    logE("七牛文件上传失败！\n key:${myKey} \ninfo:$info \nresponse:$response")
                }
            }, null
        )
        /**
         *  key:user-feedback-appendix/tmkLog_2022_10_20_17_01_27_android.zip
         *
         *  info:{ver:8.4.4,ResponseInfo:1666256487984307,status:200,
         *  reqId:y_MAAAAzcc5yux8X, xlog:X-Log, xvia:null,
         *  host:upload.qiniup.com, time:1666256488,error:null}
         *
         *  response:{"hash":"FhGTofdT7zAzZHBhQAMnBJZM_QJR","key":"user-feedback-appendix\/tmkLog_2022_10_20_17_01_27_android.zip"}
         *
         */
    }

    @SuppressLint("MissingPermission")
    fun reqS3Upload(file: File, feedbackTime: String, email: String) {
        logD("上传文件到S3")
        uploadEmail = email
        val formatter = SimpleDateFormat("yyyy_MM_dd_HH_mm_ss", Locale.getDefault())
        val dateStr = formatter.format(Date())
        val folderName = if (SpUtils.getBoolean(SpKey.CACHE_ALL_FILE, false)) {
            "userlog_dev/x1"
        } else {
            "feedback-x1"
        }

        val key =
            "$folderName/" +
                    "${DeviceTool.getSerialNumber()}_Occur${feedbackTime}+${dateStr}.zip" // <指定S3服务上的文件名，或 null>;
        BusinessManager.getS3UploadUrl(key).catch {
            logE("获取S3上传地址失败 ${it.message}")
            withContext(Dispatchers.IO) {
                if (!NetworkUtils.isAvailable()) showToast(BaseApp.context.getString(R.string.common_network_error_check_it))
            }
            liveUploading.postValue(false)
        }.onStart {
            liveUploading.postValue(true)
        }.onEach {
            if (it.code == 0) {
                logD("获取S3上传地址成功 $it")
                uploadFileToS3(file, it.data.url, key)
            } else {
                logE("获取S3上传地址失败 $it")
                liveUploading.postValue(false)
            }
        }.launchIn(viewModelScope)
    }

    fun uploadFileToS3(file: File, url: String, key: String) {
        logD("上传文件")
        BusinessManager.uploadFile(file, url).catch {
            logE("上传文件失败 ${it.message}")
            liveUploading.postValue(false)
        }.onEach {
            if (it.isSuccessful) {
                logD("上传文件成功 $it")
                qiNiuFileKey.postValue(key)
            } else {
                logE("上传文件失败 $it")
                liveUploading.postValue(false)
            }
        }.launchIn(viewModelScope)
    }

    fun reqUploadFeedback(
        appendixUrl: String,
        loggingTime: String,
    ) {
        viewModelScope.launch(Dispatchers.IO) {
//            repo.requestUploadFeedback(appendixUrl, loggingTime)
            BusinessManager.saveUploadFeedback(uploadEmail, appendixUrl, loggingTime)
                .catch {
                    logE("提交反馈异常 $it")
                    liveUploading.postValue(false)
                }
                .onStart {
                }
                .collect {
                    logD("提交反馈成功 $it")
                    liveUploading.postValue(false)
                    liveUploadSuccess.postValue(true)
                }
        }
    }

    companion object {
        const val FILE_LIMIT = 10
    }


}