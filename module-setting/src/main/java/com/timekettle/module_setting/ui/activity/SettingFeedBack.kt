package com.timekettle.module_setting.ui.activity

import android.annotation.SuppressLint
import android.app.Dialog
import android.content.Intent
import android.graphics.Color
import android.provider.Settings
import androidx.activity.viewModels
import androidx.appcompat.content.res.AppCompatResources
import androidx.lifecycle.lifecycleScope
import com.blankj.utilcode.util.NetworkUtils
import com.gyf.immersionbar.BarHide
import com.gyf.immersionbar.ImmersionBar
import com.timekettle.module_setting.R
import com.timekettle.module_setting.databinding.SettingFeedbackBinding
import com.timekettle.module_setting.ui.vm.VMFeedBack
import com.timekettle.upup.base.ktx.clickDelay
import com.timekettle.upup.base.ktx.observeLiveData
import com.timekettle.upup.base.ktx.setClickEffect
import com.timekettle.upup.base.utils.DateUtils
import com.timekettle.upup.base.utils.SpUtils
import com.timekettle.upup.base.utils.logD
import com.timekettle.upup.base.utils.showToast
import com.timekettle.upup.base.utils.startActivityByRoute
import com.timekettle.upup.comm.base.BaseActivity
import com.timekettle.upup.comm.constant.RouteUrl
import com.timekettle.upup.comm.constant.SpKey
import com.timekettle.upup.comm.dialog.DialogFactory
import com.timekettle.upup.comm.model.SensorsCustomEvent
import com.timekettle.upup.comm.service.login.LoginServiceImplWrap
import com.timekettle.upup.comm.tools.AKSManager
import com.timekettle.upup.comm.tools.PKCS12Manager
import com.timekettle.upup.comm.utils.DeviceUtil
import com.timekettle.upup.comm.utils.SensorsUtil
import com.timekettle.upup.comm.widget.KeyboardUtil
import com.timekettle.upup.comm.widget.MyKeyboardView
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import java.text.SimpleDateFormat
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.util.Calendar
import java.util.Locale


/**
 * 设置-反馈页面，帮助反馈页面、日志上传页面
 */
@AndroidEntryPoint
class SettingFeedBack : BaseActivity<SettingFeedbackBinding, VMFeedBack>() {
    override val mViewModel: VMFeedBack by viewModels()
    private lateinit var keyboardUtil: KeyboardUtil

    private var uploadIngDialog: Dialog? = null

    var dateStr = DateUtils.getFeedBackDate()
    var timeStr = DateUtils.getFeedBackTime()
    override fun initObserve() {
        observeLiveData(mViewModel.liveUploading, ::processIsUploading)
        observeLiveData(mViewModel.qiNiuFileKey, ::processQiNiuFileKey)
        observeLiveData(mViewModel.liveUploadSuccess, ::processUploadSuccess)
        mViewModel.emailFlow.onEach {
            mBinding.tvEmailInput.text = it
            mBinding.tvEmailInput.setTextColor(Color.WHITE)
        }.launchIn(lifecycleScope)
    }

    override fun initRequestData() {
        mViewModel.getSavedEmail()
    }

    @SuppressLint("MissingPermission")
    override fun initListener() {
        mBinding.btnSubmit.clickDelay {
            if (mBinding.tvEmailInput.text.toString() == getString(com.timekettle.upup.comm.R.string.trans_email_tip)) {
                showToast(getString(com.timekettle.upup.comm.R.string.trans_log_upload_email_tip))
                return@clickDelay
            }
            val hasCert = DeviceUtil.hasCert()
            // 去证书下载
            if (!hasCert) {
                if (NetworkUtils.isConnected()) {
                    startActivityByRoute(RouteUrl.Home.CertificateActivity)
                } else {
                    startActivity(Intent(Settings.ACTION_WIFI_SETTINGS))
                }
            } else {
                if(mViewModel.liveUploadSuccess.value == false){
                    mViewModel.submitLog(feedbackTime = "${dateStr}_$timeStr", mBinding.tvEmailInput.text.toString())

                    SensorsUtil.trackEvent(SensorsCustomEvent.X1_ClickUploadlog.name, null)
                }else{
                    finish()
                }
            }
        }

        mBinding.ivDateNext.setOnClickListener {
            dateStr = getNextDateStr(dateStr)
            mBinding.tvDate.text = dateStr
        }
        mBinding.ivDatePre.setOnClickListener {
            dateStr = getPreDateStr(dateStr)
            mBinding.tvDate.text = dateStr
        }

        mBinding.ivTimeNext.setOnClickListener {
            timeStr = getNextTimeStr(timeStr)
            mBinding.tvTime.text = timeStr
        }
        mBinding.ivTimePre.setOnClickListener {
            timeStr = getPreTimeStr(timeStr)
            mBinding.tvTime.text = timeStr
        }

        mBinding.llEmailInput.setOnClickListener {
            keyboardUtil.initKeyboard(MyKeyboardView.KEYBOARDTYPE_ABC)
        }

        keyboardUtil.setOnConnectBtnClickListener { content ->
            mBinding.tvEmailInput.setTextColor(Color.WHITE)
            mBinding.tvEmailInput.text = content
            SpUtils.putString(SpKey.FEEDBACK_EMAIL, content)
        }
    }


    private fun processIsUploading(b: Boolean) {
        if (b) {
            uploadIngDialog?.show()
        } else {
            uploadIngDialog?.dismiss()
        }
    }

    override fun SettingFeedbackBinding.initView() {
        ImmersionBar.with(this@SettingFeedBack).hideBar(BarHide.FLAG_HIDE_NAVIGATION_BAR).init()
        mTitleTv?.text = getString(com.timekettle.upup.comm.R.string.setting_upload_log)
        mBinding.tvDate.text = dateStr
        mBinding.tvTime.text = timeStr
        setClickEffect(ivDatePre, ivDateNext, ivTimeNext, ivTimePre, llEmailInput)
        uploadIngDialog = DialogFactory.createProcessDialog(
            this@SettingFeedBack,
            getString(com.timekettle.upup.comm.R.string.setting_upload_log_ing)
        )
        keyboardUtil = KeyboardUtil(this@SettingFeedBack)
        keyboardUtil.setEditTextHintAndMinLength(
            "",
            1,
            false,
            false
        )
        keyboardUtil.setNeedCheckEmail(true)
    }

    private fun processUploadSuccess(b: Boolean) {
        if (b) {
            mBinding.btnSubmit.text =
                getText(com.timekettle.upup.comm.R.string.setting_upload_log_done)
            mBinding.btnSubmit.setBackgroundDrawable(
                AppCompatResources.getDrawable(
                    this@SettingFeedBack,
                    R.drawable.bg_submit_success
                )
            )
        }
    }

    private fun processQiNiuFileKey(data: String) {
        logD("七牛上传完后的的key：$data")
        mViewModel.reqUploadFeedback(
            data, "$dateStr $timeStr"
        )
    }

    // 取dateStr的下一天，如果日期已经超过了当前日期，那么就循环到7天前，总是在1个星期之内循环
    private fun getNextDateStr(nowDataStr: String): String {
        val sdf = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
        val date = sdf.parse(nowDataStr)
        val calendar = Calendar.getInstance()
        if (date != null) {
            calendar.time = date
        }
        calendar.add(Calendar.DATE, 1)
        val now = Calendar.getInstance()
        if (calendar.after(now)) {
            calendar.add(Calendar.DATE, -8)
        }
        return sdf.format(calendar.time)
    }

    // 取dateStr的前一天，如果日期比一个星期之前还早，那么变成到7天后，总是在前1个星期之内循环
    @SuppressLint("NewApi")
    fun getPreDateStr(nowDataStr: String): String {
        val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")
        var date = LocalDate.parse(nowDataStr, formatter)
        date = date.minusDays(1)
        if (date.isBefore(LocalDate.now().minusWeeks(1))) {
            date = LocalDate.now()
        }
        return date.format(formatter)
    }


    // 将当前时间timeStr再加一小时，返回String，按照24小时制，如果等于24则归零
    private fun getNextTimeStr(nowTimeStr: String): String {
        val sdf = SimpleDateFormat("HH:mm", Locale.getDefault())
        val date = sdf.parse(nowTimeStr)
        val calendar = Calendar.getInstance()
        if (date != null) {
            calendar.time = date
        }
        calendar.add(Calendar.HOUR, 1)
        return sdf.format(calendar.time)
    }


    // 将当前时间timeStr再减一小时，返回String，按照24小时制，如果等于24则归零
    private fun getPreTimeStr(nowTimeStr: String): String {
        val sdf = SimpleDateFormat("HH:mm", Locale.getDefault())
        val date = sdf.parse(nowTimeStr)
        val calendar = Calendar.getInstance()
        if (date != null) {
            calendar.time = date
        }
        calendar.add(Calendar.HOUR, -1)
        return sdf.format(calendar.time)
    }

}


